/// Base class for all exceptions in the application
abstract class AppException implements Exception {
  final String message;
  final String? code;

  const AppException(this.message, {this.code});

  @override
  String toString() => 'AppException(message: $message, code: $code)';
}

/// Server-related exceptions
class ServerException extends AppException {
  const ServerException([super.message = 'Server error occurred', String? code])
      : super(code: code);
}

/// Network-related exceptions
class NetworkException extends AppException {
  const NetworkException([super.message = 'Network error occurred', String? code])
      : super(code: code);
}

/// Cache-related exceptions
class CacheException extends AppException {
  const CacheException([super.message = 'Cache error occurred', String? code])
      : super(code: code);
}

/// Authentication-related exceptions
class AuthException extends AppException {
  const AuthException([super.message = 'Authentication error occurred', String? code])
      : super(code: code);
}

/// Validation-related exceptions
class ValidationException extends AppException {
  const ValidationException([super.message = 'Validation error occurred', String? code])
      : super(code: code);
}

/// Permission-related exceptions
class PermissionException extends AppException {
  const PermissionException([super.message = 'Permission error occurred', String? code])
      : super(code: code);
}

/// Not found exceptions
class NotFoundException extends AppException {
  const NotFoundException([super.message = 'Resource not found', String? code])
      : super(code: code);
}

/// Conflict exceptions (e.g., duplicate data)
class ConflictException extends AppException {
  const ConflictException([super.message = 'Conflict occurred', String? code])
      : super(code: code);
}

/// Unknown/unexpected exceptions
class UnknownException extends AppException {
  const UnknownException([super.message = 'Unknown error occurred', String? code])
      : super(code: code);
}
