{"@@locale": "en", "appTitle": "MedyTrack", "@appTitle": {"description": "The title of the application"}, "dashboard": "Dashboard", "medicines": "Medicines", "myMedicines": "My Medicines", "alerts": "<PERSON><PERSON><PERSON>", "reminders": "Reminders", "settings": "Settings", "profile": "Profile", "locations": "Locations", "family": "Family", "notificationSettings": "Notification Settings", "languageSettings": "Language Settings", "profileSecurity": "Profile & Security", "personalization": "Personalization", "dataManagement": "Data Management", "expiryAlerts": "Expiry <PERSON><PERSON>", "lowStockAlerts": "Low Stock Alerts", "medicationReminders": "Medication Reminders", "pushNotifications": "Push Notifications", "emailNotifications": "Email Notifications", "enabled": "Enabled", "disabled": "Disabled", "activated": "Activated", "deactivated": "Deactivated", "language": "Language", "french": "French", "english": "English", "arabic": "Arabic", "darkMode": "Dark Mode", "dateFormat": "Date Format", "autoSync": "Auto Sync", "testNotifications": "Test Notifications", "sendTestNotification": "Send a test notification", "test": "Test", "testNotificationSent": "Test notification sent!", "error": "Error", "expiryThreshold": "Expiry <PERSON><PERSON><PERSON><PERSON>", "lowStockThreshold": "Low Stock Threshold", "daysBeforeExpiry": "{days} days before expiry", "@daysBeforeExpiry": {"placeholders": {"days": {"type": "int"}}}, "unitsRemaining": "{units} units or less", "@unitsRemaining": {"placeholders": {"units": {"type": "int"}}}, "save": "Save", "cancel": "Cancel", "ok": "OK", "addReminder": "<PERSON><PERSON>", "editReminder": "<PERSON>minder", "deleteReminder": "Delete Reminder", "reminderSettings": "<PERSON><PERSON><PERSON><PERSON>", "activeReminders": "Active Reminders", "medicinesWithoutReminders": "Medicines without Reminders", "noActiveReminders": "No active reminders", "noMedicinesWithoutReminders": "All medicines have reminders", "reminderTime": "Reminder Time", "reminderFrequency": "Frequency", "daily": "Daily", "weekly": "Weekly", "hourlyInterval": "Hourly Interval", "selectDays": "Select Days", "intervalHours": "Interval (hours)", "startDate": "Start Date", "endDate": "End Date", "isActive": "Active", "reminderAdded": "<PERSON><PERSON><PERSON> added successfully", "reminderUpdated": "Reminder updated successfully", "reminderDeleted": "<PERSON><PERSON><PERSON> deleted successfully", "medicineReminderTitle": "💊 Medicine Reminder", "timeToTakeMedicine": "Time to take {<PERSON><PERSON>ame}", "@timeToTakeMedicine": {"placeholders": {"medicineName": {"type": "String"}}}, "everyXHours": "every {hours} hours", "@everyXHours": {"placeholders": {"hours": {"type": "int"}}}, "loadingError": "Loading error", "featureComingSoon": "This feature will be available in a future version.", "preferences": "Preferences", "familyMembers": "Family Members", "storageLocations": "Storage Locations", "tags": "Tags", "dangerZone": "Danger Zone", "security": "Security", "minimumExpiryThreshold": "Minimum Expiry Threshold", "defaultLocation": "Default Location", "defaultFamilyMember": "De<PERSON>ult Family Member", "showExpiredMedicines": "Show Expired Medicines", "groupByLocation": "Group by Location", "addMember": "Add Member", "editMember": "Edit Member", "addLocation": "Add Location", "editLocation": "Edit Location", "addTag": "Add Tag", "editTag": "Edit Tag", "name": "Name", "description": "Description", "relationship": "Relationship", "category": "Category", "color": "Color", "none": "None", "days": "days", "units": "units", "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "updatePassword": "Update Password", "deleteAccount": "Delete Account", "accountDeletion": "Account Deletion", "personalInformation": "Personal Information", "email": "Email", "updateProfile": "Update Profile", "avatar": "Avatar", "uploadPhoto": "Upload Photo", "removePhoto": "Remove Photo", "inviteToHousehold": "Invite to Household", "generateInviteCode": "Generate Invite Code", "inviteCodeGenerated": "Invite Code Generated", "shareCode": "Share Code", "joinHousehold": "Join <PERSON>", "scanQRCode": "Scan QR Code", "enterInviteCode": "Enter Invite Code", "inviteCode": "Invite Code", "enterInviteCodeHint": "Enter the invitation code", "inviteCodeRequired": "Invite code is required", "join": "Join", "copyCode": "Copy Code"}