import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../domain/entities/medicine.dart';
import '../../bloc/medicine_detail/medicine_detail_bloc.dart';
import '../../bloc/medicine_detail/medicine_detail_event.dart';
import '../../bloc/medicine_detail/medicine_detail_state.dart';

class MedicineDetailPage extends StatefulWidget {
  final String medicineId;

  const MedicineDetailPage({
    super.key,
    required this.medicineId,
  });

  @override
  State<MedicineDetailPage> createState() => _MedicineDetailPageState();
}

class _MedicineDetailPageState extends State<MedicineDetailPage> {
  @override
  void initState() {
    super.initState();
    // Load medicine details
    context.read<MedicineDetailBloc>().add(
          MedicineDetailLoadRequested(medicineId: widget.medicineId),
        );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.teal,
      body: BlocListener<MedicineDetailBloc, MedicineDetailState>(
        listener: (context, state) {
          if (state is MedicineDetailOperationSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.success,
              ),
            );

            // Navigate back if medicine was deleted
            if (state.operationType ==
                MedicineDetailOperationType.medicineDeleted) {
              context.pop();
            }
          } else if (state is MedicineDetailError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          }
        },
        child: BlocBuilder<MedicineDetailBloc, MedicineDetailState>(
          builder: (context, state) {
            if (state is MedicineDetailLoading) {
              return _buildLoadingState();
            }

            if (state is MedicineDetailError) {
              return _buildErrorState(state.message);
            }

            if (state is MedicineDetailLoaded) {
              return _buildLoadedState(state.medicine, state.isUpdating);
            }

            return _buildLoadingState();
          },
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Column(
      children: [
        // Header with teal background
        Container(
          padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
          child: Row(
            children: [
              GestureDetector(
                onTap: () => context.pop(),
                child: Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.arrow_back,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  'Détails du médicament',
                  style: AppTextStyles.headlineMedium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
        // Content container with white background
        Expanded(
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(24),
                topRight: Radius.circular(24),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, -4),
                ),
              ],
            ),
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorState(String message) {
    return Column(
      children: [
        // Header with teal background
        Container(
          padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
          child: Row(
            children: [
              GestureDetector(
                onTap: () => context.pop(),
                child: Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.arrow_back,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  'Erreur',
                  style: AppTextStyles.headlineMedium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
        // Content container with white background
        Expanded(
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(24),
                topRight: Radius.circular(24),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, -4),
                ),
              ],
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: AppColors.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Erreur de chargement',
                    style: AppTextStyles.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    message,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.grey600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      context.read<MedicineDetailBloc>().add(
                            MedicineDetailLoadRequested(
                                medicineId: widget.medicineId),
                          );
                    },
                    child: const Text('Réessayer'),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLoadedState(Medicine medicine, bool isUpdating) {
    return Column(
      children: [
        // Header with teal background
        Container(
          padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
          child: Row(
            children: [
              GestureDetector(
                onTap: () => context.pop(),
                child: Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.arrow_back,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  medicine.displayName,
                  style: AppTextStyles.headlineMedium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              GestureDetector(
                onTap: () => context.push('/medicines/${medicine.id}/edit'),
                child: Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.edit,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ],
          ),
        ),
        // Content container with white background
        Expanded(
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(24),
                topRight: Radius.circular(24),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, -4),
                ),
              ],
            ),
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Status Card
                  _buildStatusCard(medicine),
                  const SizedBox(height: 20),

                  // Medicine Information
                  _buildInformationCard(medicine),
                  const SizedBox(height: 20),

                  // Location and Assignment
                  _buildLocationCard(medicine),
                  const SizedBox(height: 20),

                  // Tags
                  if (medicine.tags.isNotEmpty) ...[
                    _buildTagsCard(medicine),
                    const SizedBox(height: 20),
                  ],

                  // Notes
                  if (medicine.notes != null && medicine.notes!.isNotEmpty) ...[
                    _buildNotesCard(medicine),
                    const SizedBox(height: 20),
                  ],

                  // Action Buttons
                  _buildActionButtons(medicine, isUpdating),

                  const SizedBox(height: 20), // Bottom padding
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatusCard(Medicine medicine) {
    final isExpired = medicine.expiration != null &&
        medicine.expiration!.isBefore(DateTime.now());
    final isExpiringSoon = medicine.expiration != null &&
        !isExpired &&
        medicine.expiration!
            .isBefore(DateTime.now().add(const Duration(days: 30)));
    final isLowStock = medicine.quantity <= medicine.lowStockThreshold;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.grey200.withValues(alpha: 0.5),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: AppColors.teal,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Statut du médicament',
                style: AppTextStyles.titleMedium.copyWith(
                  color: AppColors.navy,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Status indicators
          Row(
            children: [
              // Expiration status
              Expanded(
                child: _buildStatusIndicator(
                  icon: Icons.schedule,
                  label: 'Expiration',
                  value: medicine.expiration != null
                      ? _formatDate(medicine.expiration!)
                      : 'Non définie',
                  color: isExpired
                      ? AppColors.error
                      : isExpiringSoon
                          ? AppColors.warning
                          : AppColors.success,
                  isAlert: isExpired || isExpiringSoon,
                ),
              ),
              const SizedBox(width: 16),

              // Stock status
              Expanded(
                child: _buildStatusIndicator(
                  icon: Icons.inventory_2_outlined,
                  label: 'Stock',
                  value: '${medicine.quantity}',
                  color: isLowStock ? AppColors.error : AppColors.success,
                  isAlert: isLowStock,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusIndicator({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
    bool isAlert = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: isAlert ? Border.all(color: color, width: 1) : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 16),
              const SizedBox(width: 4),
              Text(
                label,
                style: AppTextStyles.labelSmall.copyWith(
                  color: color,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.navy,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = date.difference(now).inDays;

    if (difference < 0) {
      return 'Expiré depuis ${(-difference)} jour${(-difference) > 1 ? 's' : ''}';
    } else if (difference == 0) {
      return 'Expire aujourd\'hui';
    } else if (difference == 1) {
      return 'Expire demain';
    } else if (difference <= 30) {
      return 'Expire dans $difference jour${difference > 1 ? 's' : ''}';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  Widget _buildInformationCard(Medicine medicine) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.grey200.withValues(alpha: 0.5),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.medication_outlined,
                color: AppColors.teal,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Informations médicament',
                style: AppTextStyles.titleMedium.copyWith(
                  color: AppColors.navy,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Medicine details
          if (medicine.dosage != null) ...[
            _buildInfoRow('Dosage', medicine.dosage!),
            const SizedBox(height: 12),
          ],

          if (medicine.form != null) ...[
            _buildInfoRow('Forme', medicine.form!),
            const SizedBox(height: 12),
          ],

          if (medicine.laboratoire != null) ...[
            _buildInfoRow('Laboratoire', medicine.laboratoire!),
            const SizedBox(height: 12),
          ],

          if (medicine.dci != null) ...[
            _buildInfoRow('DCI', medicine.dci!),
            const SizedBox(height: 12),
          ],

          if (medicine.classe != null) ...[
            _buildInfoRow('Classe thérapeutique', medicine.classe!),
            const SizedBox(height: 12),
          ],

          if (medicine.sousClasse != null) ...[
            _buildInfoRow('Sous-classe', medicine.sousClasse!),
            const SizedBox(height: 12),
          ],

          _buildInfoRow('Seuil stock faible', '${medicine.lowStockThreshold}'),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 120,
          child: Text(
            label,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.grey600,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Text(
            value,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.navy,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLocationCard(Medicine medicine) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.grey200.withValues(alpha: 0.5),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.location_on_outlined,
                color: AppColors.teal,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Emplacement et attribution',
                style: AppTextStyles.titleMedium.copyWith(
                  color: AppColors.navy,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Location info
          if (medicine.locationName != null) ...[
            _buildInfoRow('Emplacement', medicine.locationName!),
            const SizedBox(height: 12),
          ],

          // Family member info
          if (medicine.familyMemberName != null) ...[
            _buildInfoRow('Assigné à', medicine.familyMemberName!),
            const SizedBox(height: 12),
          ],

          // Creation date
          _buildInfoRow('Ajouté le', _formatDate(medicine.createdAt)),
        ],
      ),
    );
  }

  Widget _buildTagsCard(Medicine medicine) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.grey200.withValues(alpha: 0.5),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.local_offer_outlined,
                color: AppColors.teal,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Étiquettes',
                style: AppTextStyles.titleMedium.copyWith(
                  color: AppColors.navy,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Tags
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: medicine.tags
                .map((tag) => Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: AppColors.teal.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                            color: AppColors.teal.withValues(alpha: 0.3)),
                      ),
                      child: Text(
                        tag,
                        style: AppTextStyles.labelSmall.copyWith(
                          color: AppColors.teal,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ))
                .toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildNotesCard(Medicine medicine) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.grey200.withValues(alpha: 0.5),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.note_outlined,
                color: AppColors.teal,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Notes',
                style: AppTextStyles.titleMedium.copyWith(
                  color: AppColors.navy,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Notes content
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.grey50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.grey200),
            ),
            child: Text(
              medicine.notes!,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.navy,
                height: 1.5,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(Medicine medicine, bool isUpdating) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.grey200.withValues(alpha: 0.5),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.settings_outlined,
                color: AppColors.teal,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Actions',
                style: AppTextStyles.titleMedium.copyWith(
                  color: AppColors.navy,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Quick actions row
          Row(
            children: [
              // Take medicine button
              Expanded(
                child: _buildActionButton(
                  icon: Icons.medication,
                  label: 'Prendre',
                  color: AppColors.success,
                  onPressed: isUpdating
                      ? null
                      : () => _showTakeMedicineDialog(medicine),
                ),
              ),
              const SizedBox(width: 12),

              // Update quantity button
              Expanded(
                child: _buildActionButton(
                  icon: Icons.edit,
                  label: 'Modifier quantité',
                  color: AppColors.teal,
                  onPressed: isUpdating
                      ? null
                      : () => _showUpdateQuantityDialog(medicine),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Secondary actions row
          Row(
            children: [
              // Edit medicine button
              Expanded(
                child: _buildActionButton(
                  icon: Icons.edit_outlined,
                  label: 'Modifier',
                  color: AppColors.navy,
                  onPressed: isUpdating
                      ? null
                      : () => context.push('/medicines/${medicine.id}/edit'),
                ),
              ),
              const SizedBox(width: 12),

              // Delete medicine button
              Expanded(
                child: _buildActionButton(
                  icon: Icons.delete_outline,
                  label: 'Supprimer',
                  color: AppColors.error,
                  onPressed: isUpdating
                      ? null
                      : () => _showDeleteConfirmationDialog(medicine),
                ),
              ),
            ],
          ),

          if (isUpdating) ...[
            const SizedBox(height: 16),
            Center(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.teal),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Mise à jour en cours...',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.grey600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback? onPressed,
  }) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: onPressed != null
            ? color.withValues(alpha: 0.1)
            : AppColors.grey100,
        foregroundColor: onPressed != null ? color : AppColors.grey400,
        elevation: 0,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(
            color: onPressed != null
                ? color.withValues(alpha: 0.3)
                : AppColors.grey300,
          ),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 20),
          const SizedBox(height: 4),
          Text(
            label,
            style: AppTextStyles.labelSmall.copyWith(
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showTakeMedicineDialog(Medicine medicine) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Prendre le médicament'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Combien d\'unités de ${medicine.name} voulez-vous prendre ?'),
            const SizedBox(height: 16),
            Text(
              'Stock actuel: ${medicine.quantity}',
              style: AppTextStyles.bodySmall.copyWith(color: AppColors.grey600),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<MedicineDetailBloc>().add(
                    MedicineTaken(
                      medicineId: medicine.id,
                      quantityTaken: 1,
                    ),
                  );
            },
            child: const Text('Prendre 1'),
          ),
        ],
      ),
    );
  }

  void _showUpdateQuantityDialog(Medicine medicine) {
    final controller =
        TextEditingController(text: medicine.quantity.toString());

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Modifier la quantité'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Nouvelle quantité pour ${medicine.name}:'),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Quantité',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              final newQuantity = int.tryParse(controller.text);
              if (newQuantity != null && newQuantity >= 0) {
                Navigator.of(context).pop();
                context.read<MedicineDetailBloc>().add(
                      MedicineQuantityUpdated(
                        medicineId: medicine.id,
                        newQuantity: newQuantity,
                      ),
                    );
              }
            },
            child: const Text('Modifier'),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmationDialog(Medicine medicine) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer le médicament'),
        content: Text(
          'Êtes-vous sûr de vouloir supprimer ${medicine.name} ? Cette action est irréversible.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<MedicineDetailBloc>().add(
                    MedicineDetailDeleteRequested(medicineId: medicine.id),
                  );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }
}
