import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../bloc/household/household_bloc.dart';
import '../../bloc/household/household_event.dart';
import '../../bloc/household/household_state.dart';

/// Page for joining an existing household
class JoinHouseholdPage extends StatefulWidget {
  const JoinHouseholdPage({super.key});

  @override
  State<JoinHouseholdPage> createState() => _JoinHouseholdPageState();
}

class _JoinHouseholdPageState extends State<JoinHouseholdPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _codeController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  MobileScannerController? _scannerController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _codeController.dispose();
    _scannerController?.dispose();
    super.dispose();
  }

  void _validateAndJoinHousehold() {
    if (_formKey.currentState!.validate()) {
      // TODO: Get current user from auth state
      // For now, use a mock user ID
      context.read<HouseholdBloc>().add(
            JoinHouseholdEvent(
              userId: 'mock-user-id',
              inviteCode: _codeController.text.trim().toUpperCase(),
            ),
          );
    }
  }

  void _onQRCodeDetected(BarcodeCapture capture) {
    final List<Barcode> barcodes = capture.barcodes;
    for (final barcode in barcodes) {
      if (barcode.rawValue != null) {
        final code = barcode.rawValue!;
        // Extract invite code from URL or use as is
        final inviteCode = _extractInviteCode(code);
        if (inviteCode.isNotEmpty) {
          _codeController.text = inviteCode;
          _tabController.animateTo(0); // Switch to manual entry tab
          _validateAndJoinHousehold();
          break;
        }
      }
    }
  }

  String _extractInviteCode(String input) {
    // If it's a URL, extract the invite code
    if (input.contains('medytrack.com/invite/')) {
      final uri = Uri.tryParse(input);
      if (uri != null) {
        return uri.pathSegments.last;
      }
    }
    // If it's just a code, return as is
    if (RegExp(r'^[A-Z0-9]{6,8}$').hasMatch(input.toUpperCase())) {
      return input.toUpperCase();
    }
    return '';
  }

  void _pasteFromClipboard() async {
    final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
    if (clipboardData?.text != null) {
      final inviteCode = _extractInviteCode(clipboardData!.text!);
      if (inviteCode.isNotEmpty) {
        _codeController.text = inviteCode;
      } else {
        _codeController.text = clipboardData.text!;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<HouseholdBloc, HouseholdState>(
      listener: (context, state) {
        if (state is HouseholdJoined) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Vous avez rejoint le foyer avec succès !'),
              backgroundColor: AppColors.success,
            ),
          );
          context.go('/dashboard');
        } else if (state is HouseholdError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: AppColors.error,
            ),
          );
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.grey50,
        appBar: AppBar(
          title: const Text('Rejoindre un foyer'),
          backgroundColor: AppColors.teal,
          foregroundColor: AppColors.white,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => context.go('/household-onboarding'),
          ),
        ),
        body: Column(
          children: [
            // Tab Bar
            Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.grey100,
                borderRadius: BorderRadius.circular(12),
              ),
              child: TabBar(
                controller: _tabController,
                indicator: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.shadowLight,
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                indicatorPadding: const EdgeInsets.all(4),
                labelColor: AppColors.navy,
                unselectedLabelColor: AppColors.grey600,
                labelStyle: AppTextStyles.titleMedium,
                unselectedLabelStyle: AppTextStyles.titleMedium,
                dividerColor: Colors.transparent,
                tabs: const [
                  Tab(text: 'Code manuel'),
                  Tab(text: 'Scanner QR'),
                ],
              ),
            ),

            // Tab Bar View
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildManualEntryTab(),
                  _buildQRScannerTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildManualEntryTab() {
    return BlocBuilder<HouseholdBloc, HouseholdState>(
      builder: (context, state) {
        final isLoading = state is HouseholdLoading;

        return Padding(
          padding: const EdgeInsets.all(24),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Text(
                  'Saisissez le code d\'invitation',
                  style: AppTextStyles.headlineSmall.copyWith(
                    color: AppColors.navy,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 16),

                Text(
                  'Demandez le code d\'invitation à un membre du foyer que vous souhaitez rejoindre.',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.grey600,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 32),

                // Code input field
                TextFormField(
                  controller: _codeController,
                  decoration: InputDecoration(
                    labelText: 'Code d\'invitation',
                    hintText: 'ABC123',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    suffixIcon: IconButton(
                      icon: const Icon(Icons.paste),
                      onPressed: _pasteFromClipboard,
                      tooltip: 'Coller depuis le presse-papiers',
                    ),
                  ),
                  textCapitalization: TextCapitalization.characters,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Veuillez saisir le code d\'invitation';
                    }
                    if (value.length < 6 || value.length > 8) {
                      return 'Le code doit contenir entre 6 et 8 caractères';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 24),

                // Join button
                SizedBox(
                  height: 48,
                  child: ElevatedButton(
                    onPressed: isLoading ? null : _validateAndJoinHousehold,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.teal,
                      foregroundColor: AppColors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Text('Rejoindre le foyer'),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildQRScannerTab() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(24),
          child: Text(
            'Scannez le QR code d\'invitation',
            style: AppTextStyles.headlineSmall.copyWith(
              color: AppColors.navy,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        Expanded(
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 24),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: MobileScanner(
                controller: _scannerController ??= MobileScannerController(),
                onDetect: _onQRCodeDetected,
              ),
            ),
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }
}
