# 🤖 MedyTrack Automated Database Migration System

## 🎯 Purpose
This automated migration system enforces all safety measures for database synchronization, eliminating human error and ensuring production data integrity.

## 🔒 Safety Features

### ✅ **Mandatory Production Backup**
- Automatic backup creation before any migration
- Timestamp verification (backups must be <1 hour old)
- Integrity validation of backup files
- Rollback capability with one command

### ✅ **Automated Cleanup & Validation**
- Removes NULL user_id records with validation reports
- Verifies data integrity before and after migration
- Generates comprehensive audit trails

### ✅ **Hard-coded Security Safeguards**
- Forbidden tables are automatically excluded:
  - `auth.users`, `auth.sessions`, `auth.refresh_tokens`
  - `app_errors`, `audit_logs`, `storage.*`
- Environment validation prevents dev/prod mixups
- Connectivity testing before operations

### ✅ **Comprehensive Reporting**
- Pre-sync validation reports
- Post-sync integrity verification
- Record count comparisons
- Audit trail logging

## 🚀 Quick Start

### 1. Setup Environment
```bash
# Copy configuration template
cp scripts/db_sync_config.template scripts/db_sync_config.env

# Edit configuration with your database credentials
# NEVER commit this file to version control!
nano scripts/db_sync_config.env
```

### 2. Load Environment Variables
```bash
# Linux/macOS
source scripts/db_sync_config.env

# Windows PowerShell
Get-Content scripts/db_sync_config.env | ForEach-Object {
    if ($_ -match '^([^=]+)=(.*)$') {
        [Environment]::SetEnvironmentVariable($matches[1], $matches[2], 'Process')
    }
}
```

### 3. Run Migration

#### **Safe Operations (Recommended)**
```bash
# Sync Production to Development (SAFE)
./scripts/db_sync.sh prod-to-dev

# Schema-only changes (SAFEST)
./scripts/db_sync.sh schema-only
```

#### **High-Risk Operations (Requires Approval)**
```bash
# Development to Production (DANGEROUS - requires explicit flag)
./scripts/db_sync.sh dev-to-prod --force-dev-to-prod
```

#### **Emergency Procedures**
```bash
# Emergency rollback to latest backup
./scripts/db_sync.sh rollback
```

## 📋 Migration Types

| Type | Risk Level | Description | Use Case |
|------|------------|-------------|----------|
| `prod-to-dev` | 🟢 **SAFE** | Copy Production data to Development | Regular development sync |
| `schema-only` | 🟢 **SAFEST** | Schema changes only, no data | Database structure updates |
| `dev-to-prod` | 🔴 **HIGH RISK** | Copy Development to Production | Major feature releases |
| `rollback` | 🟡 **EMERGENCY** | Restore from latest backup | Production issues |

## 🛡️ Safety Mechanisms

### **Pre-Migration Checks**
- ✅ Environment variable validation
- ✅ Database connectivity testing
- ✅ Production backup creation
- ✅ Backup integrity verification
- ✅ NULL record cleanup validation
- ✅ Forbidden table exclusion verification

### **During Migration**
- ✅ Real-time progress logging
- ✅ Error handling with immediate rollback
- ✅ Data integrity monitoring
- ✅ Timeout protection

### **Post-Migration Validation**
- ✅ Record count comparison
- ✅ Foreign key constraint verification
- ✅ Data integrity checks
- ✅ Application functionality testing

## 📊 Reports and Logging

### **Generated Reports**
- `presync_report_YYYYMMDD_HHMMSS.txt` - Pre-migration validation
- `postsync_report_YYYYMMDD_HHMMSS.txt` - Post-migration verification
- `migration_YYYYMMDD_HHMMSS.log` - Complete operation log

### **Backup Files**
- `prod_backup_YYYYMMDD_HHMMSS.sql` - Production database backup
- `latest_backup.txt` - Path to most recent backup

## 🚨 Emergency Procedures

### **Immediate Rollback**
```bash
# If issues detected after migration
./scripts/db_sync.sh rollback

# Manual rollback (if script fails)
psql -h [prod-host] -U postgres -d [database] < backups/prod_backup_YYYYMMDD_HHMMSS.sql
```

### **Rollback Decision Matrix**
| Issue Severity | Action | Timeline |
|----------------|--------|----------|
| **Critical** (Data loss, app crashes) | Immediate rollback | < 5 minutes |
| **High** (Major features broken) | Rollback within 1 hour | < 1 hour |
| **Medium** (Minor issues) | Fix forward or rollback | < 4 hours |
| **Low** (Cosmetic) | Fix in next release | Next cycle |

## 🔧 Configuration Options

### **Environment Variables**
```bash
# Required
PROD_SUPABASE_URL=https://your-prod-project.supabase.co
PROD_SUPABASE_ANON_KEY=your-prod-anon-key
DEV_SUPABASE_URL=https://your-dev-project.supabase.co
DEV_SUPABASE_ANON_KEY=your-dev-anon-key

# Optional
BACKUP_RETENTION_DAYS=30
MIGRATION_TIMEOUT_MINUTES=60
SLACK_WEBHOOK_URL=https://hooks.slack.com/...
```

### **Safety Settings**
```bash
REQUIRE_MANUAL_APPROVAL=true      # Require confirmation for risky operations
ENABLE_DRY_RUN=true              # Test migrations without actual changes
FORCE_BACKUP_VALIDATION=true     # Mandatory backup integrity checks
```

## 🧪 Testing the Migration System

### **Dry Run Mode**
```bash
# Test migration without making changes
ENABLE_DRY_RUN=true ./scripts/db_sync.sh prod-to-dev
```

### **Validation Tests**
```bash
# Test backup creation
./scripts/db_sync.sh schema-only

# Verify rollback capability
./scripts/db_sync.sh rollback
```

## 📞 Support and Troubleshooting

### **Common Issues**

#### **"Database URLs are identical"**
- **Cause**: PROD_SUPABASE_URL and DEV_SUPABASE_URL are the same
- **Solution**: Verify environment variables are correctly set

#### **"Cannot connect to database"**
- **Cause**: Invalid credentials or network issues
- **Solution**: Check API keys and network connectivity

#### **"Backup is too old"**
- **Cause**: Existing backup is >1 hour old
- **Solution**: Script will automatically create fresh backup

### **Getting Help**
1. Check the migration log file in `backups/migration_*.log`
2. Review the pre-sync and post-sync reports
3. Verify environment configuration
4. Contact development team with log files

## 🔄 Integration with CI/CD

### **GitHub Actions Integration**
```yaml
# Example workflow step
- name: Database Migration
  run: |
    source scripts/db_sync_config.env
    ./scripts/db_sync.sh schema-only
  env:
    PROD_SUPABASE_URL: ${{ secrets.PROD_SUPABASE_URL }}
    PROD_SUPABASE_ANON_KEY: ${{ secrets.PROD_SUPABASE_ANON_KEY }}
```

### **Pre-deployment Hooks**
```bash
# Add to deployment pipeline
./scripts/db_sync.sh prod-to-dev  # Sync latest prod data for testing
flutter test                      # Run tests against prod data
./scripts/db_sync.sh schema-only  # Apply schema changes
```

## 📋 Maintenance

### **Regular Tasks**
- **Weekly**: Review backup retention and cleanup old files
- **Monthly**: Test rollback procedures
- **Quarterly**: Update forbidden tables list if schema changes

### **Backup Cleanup**
```bash
# Remove backups older than 30 days
find backups/ -name "*.sql" -mtime +30 -delete
find backups/ -name "*.log" -mtime +90 -delete
```

---

**⚠️ CRITICAL REMINDER**: This system is designed to prevent data loss and production issues. Always follow the safety procedures and never bypass the built-in safeguards.
