import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../core/error/failures.dart';
import '../../../domain/repositories/household_repository.dart';
import '../../../domain/entities/household.dart';
import 'household_management_event.dart';
import 'household_management_state.dart';

/// BLoC for managing household operations
class HouseholdManagementBloc
    extends Bloc<HouseholdManagementEvent, HouseholdManagementState> {
  final HouseholdRepository _householdRepository;

  HouseholdManagementBloc({
    required HouseholdRepository householdRepository,
  })  : _householdRepository = householdRepository,
        super(const HouseholdManagementInitial()) {
    on<LoadUserHouseholds>(_onLoadUserHouseholds);
    on<CreateHousehold>(_onCreateHousehold);
    on<JoinHouseholdWithCode>(_onJoinHouseholdWithCode);
    on<GenerateInviteCode>(_onGenerateInviteCode);
    on<LeaveHousehold>(_onLeaveHousehold);
    on<RefreshHouseholds>(_onRefreshHouseholds);
    on<ValidateInviteCode>(_onValidateInviteCode);
    on<LoadActiveInvitations>(_onLoadActiveInvitations);
  }

  /// Handle loading user's households
  Future<void> _onLoadUserHouseholds(
    LoadUserHouseholds event,
    Emitter<HouseholdManagementState> emit,
  ) async {
    emit(const HouseholdManagementLoading());

    try {
      // Get current user ID
      final userId = Supabase.instance.client.auth.currentUser?.id;
      if (userId == null) {
        emit(const HouseholdManagementError(
          message: 'Utilisateur non authentifié',
          errorCode: 'AUTH_ERROR',
        ));
        return;
      }

      // Load user's households
      final householdsResult =
          await _householdRepository.getUserHouseholds(userId);

      if (householdsResult.isLeft()) {
        final failure = householdsResult.fold((l) => l, (r) => null)!;
        emit(HouseholdManagementError(
          message: _mapFailureToMessage(failure),
          errorCode: failure.runtimeType.toString(),
        ));
        return;
      }

      final households = householdsResult.fold((l) => null, (r) => r)!;

      // Load memberships for each household
      final List<HouseholdMember> allMemberships = [];

      // Use Future.wait to properly handle all async operations
      final membershipFutures = households.map((household) async {
        final membersResult =
            await _householdRepository.getHouseholdMembers(household.id);
        return membersResult.fold(
          (failure) {
            if (kDebugMode) {
              print(
                  'Failed to load members for household ${household.id}: $failure');
            }
            return <HouseholdMember>[];
          },
          (members) {
            // Find current user's membership
            return members.where((m) => m.userId == userId).toList();
          },
        );
      });

      final membershipResults = await Future.wait(membershipFutures);
      for (final userMemberships in membershipResults) {
        allMemberships.addAll(userMemberships);
      }

      if (emit.isDone) return;

      emit(HouseholdsLoaded(
        households: households,
        memberships: allMemberships,
      ));
    } catch (e) {
      if (kDebugMode) {
        print('Error loading households: $e');
      }
      emit(HouseholdManagementError(
        message: 'Erreur lors du chargement des foyers: $e',
      ));
    }
  }

  /// Handle creating a new household
  Future<void> _onCreateHousehold(
    CreateHousehold event,
    Emitter<HouseholdManagementState> emit,
  ) async {
    emit(const HouseholdManagementLoading());

    try {
      final userId = Supabase.instance.client.auth.currentUser?.id;
      if (userId == null) {
        emit(const HouseholdManagementError(
          message: 'Utilisateur non authentifié',
          errorCode: 'AUTH_ERROR',
        ));
        return;
      }

      final result = await _householdRepository.createHousehold(
        name: event.name,
        description: event.description,
        ownerId: userId,
      );

      result.fold(
        (failure) => emit(HouseholdManagementError(
          message: _mapFailureToMessage(failure),
          errorCode: failure.runtimeType.toString(),
        )),
        (household) {
          emit(HouseholdCreated(household: household));
          // Reload households after creation
          add(const LoadUserHouseholds());
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error creating household: $e');
      }
      emit(HouseholdManagementError(
        message: 'Erreur lors de la création du foyer: $e',
      ));
    }
  }

  /// Handle joining household with invite code
  Future<void> _onJoinHouseholdWithCode(
    JoinHouseholdWithCode event,
    Emitter<HouseholdManagementState> emit,
  ) async {
    emit(const HouseholdManagementLoading());

    try {
      final userId = Supabase.instance.client.auth.currentUser?.id;
      if (userId == null) {
        emit(const HouseholdManagementError(
          message: 'Utilisateur non authentifié',
          errorCode: 'AUTH_ERROR',
        ));
        return;
      }

      final result = await _householdRepository.joinHousehold(
        userId: userId,
        inviteCode: event.inviteCode,
      );

      result.fold(
        (failure) => emit(HouseholdManagementError(
          message: _mapFailureToMessage(failure),
          errorCode: failure.runtimeType.toString(),
        )),
        (membership) async {
          // Get household details
          final householdResult = await _householdRepository
              .getHouseholdById(membership.householdId);
          householdResult.fold(
            (failure) => emit(HouseholdManagementError(
              message: _mapFailureToMessage(failure),
              errorCode: failure.runtimeType.toString(),
            )),
            (household) {
              emit(HouseholdJoined(
                household: household,
                membership: membership,
              ));
              // Reload households after joining
              add(const LoadUserHouseholds());
            },
          );
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error joining household: $e');
      }
      emit(HouseholdManagementError(
        message: 'Erreur lors de l\'adhésion au foyer: $e',
      ));
    }
  }

  /// Handle generating invite code
  Future<void> _onGenerateInviteCode(
    GenerateInviteCode event,
    Emitter<HouseholdManagementState> emit,
  ) async {
    emit(const HouseholdManagementLoading());

    try {
      final result =
          await _householdRepository.generateInviteCode(event.householdId);

      result.fold(
        (failure) => emit(HouseholdManagementError(
          message: _mapFailureToMessage(failure),
          errorCode: failure.runtimeType.toString(),
        )),
        (inviteCode) => emit(InviteCodeGenerated(
          inviteCode: inviteCode,
          householdId: event.householdId,
        )),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error generating invite code: $e');
      }
      emit(HouseholdManagementError(
        message: 'Erreur lors de la génération du code d\'invitation: $e',
      ));
    }
  }

  /// Handle leaving household
  Future<void> _onLeaveHousehold(
    LeaveHousehold event,
    Emitter<HouseholdManagementState> emit,
  ) async {
    emit(const HouseholdManagementLoading());

    try {
      final userId = Supabase.instance.client.auth.currentUser?.id;
      if (userId == null) {
        emit(const HouseholdManagementError(
          message: 'Utilisateur non authentifié',
          errorCode: 'AUTH_ERROR',
        ));
        return;
      }

      final result = await _householdRepository.leaveHousehold(
        userId: userId,
        householdId: event.householdId,
      );

      result.fold(
        (failure) => emit(HouseholdManagementError(
          message: _mapFailureToMessage(failure),
          errorCode: failure.runtimeType.toString(),
        )),
        (_) {
          emit(HouseholdLeft(householdId: event.householdId));
          // Reload households after leaving
          add(const LoadUserHouseholds());
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error leaving household: $e');
      }
      emit(HouseholdManagementError(
        message: 'Erreur lors de la sortie du foyer: $e',
      ));
    }
  }

  /// Handle refreshing households
  Future<void> _onRefreshHouseholds(
    RefreshHouseholds event,
    Emitter<HouseholdManagementState> emit,
  ) async {
    add(const LoadUserHouseholds());
  }

  /// Handle validating invite code
  Future<void> _onValidateInviteCode(
    ValidateInviteCode event,
    Emitter<HouseholdManagementState> emit,
  ) async {
    emit(const HouseholdManagementLoading());

    try {
      final result =
          await _householdRepository.getHouseholdByInviteCode(event.inviteCode);

      result.fold(
        (failure) => emit(HouseholdManagementError(
          message: _mapFailureToMessage(failure),
          errorCode: failure.runtimeType.toString(),
        )),
        (household) => emit(InviteCodeValidated(
          household: household,
          inviteCode: event.inviteCode,
        )),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error validating invite code: $e');
      }
      emit(HouseholdManagementError(
        message: 'Erreur lors de la validation du code: $e',
      ));
    }
  }

  /// Handle loading active invitations for a household
  Future<void> _onLoadActiveInvitations(
    LoadActiveInvitations event,
    Emitter<HouseholdManagementState> emit,
  ) async {
    try {
      if (kDebugMode) {
        print('Loading active invitations for household: ${event.householdId}');
      }

      // Only proceed if current state is HouseholdsLoaded
      if (state is! HouseholdsLoaded) {
        if (kDebugMode) {
          print(
              'Cannot load invitations: current state is not HouseholdsLoaded');
        }
        return;
      }

      final currentState = state as HouseholdsLoaded;
      final result =
          await _householdRepository.getActiveInvitations(event.householdId);

      result.fold(
        (failure) {
          if (kDebugMode) {
            print('Failed to load active invitations: $failure');
          }
          // Don't emit error state, just log the error
          // The UI will continue to work without invitations
        },
        (invitations) {
          if (kDebugMode) {
            print('Loaded ${invitations.length} active invitations');
          }
          // Update the current HouseholdsLoaded state with invitation data
          emit(
              currentState.copyWithInvitations(event.householdId, invitations));
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error loading active invitations: $e');
      }
      // Don't emit error state, just log the error
    }
  }

  /// Map failure to user-friendly message
  String _mapFailureToMessage(Failure failure) {
    switch (failure.runtimeType) {
      case ServerFailure _:
        return 'Erreur du serveur. Veuillez réessayer.';
      case NetworkFailure _:
        return 'Erreur de connexion. Vérifiez votre connexion internet.';
      case CacheFailure _:
        return 'Erreur de cache. Veuillez réessayer.';
      default:
        return 'Une erreur inattendue s\'est produite.';
    }
  }
}
