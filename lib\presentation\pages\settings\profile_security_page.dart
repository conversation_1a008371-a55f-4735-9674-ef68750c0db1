import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../bloc/profile_security/profile_security_bloc.dart';
import '../../bloc/profile_security/profile_security_event.dart';
import '../../bloc/profile_security/profile_security_state.dart';
import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/auth/auth_state.dart' as auth_state;
import '../../widgets/settings/settings_section.dart';
import '../../widgets/settings/settings_tile.dart';
import '../../widgets/common/loading_overlay.dart';
import '../../../l10n/generated/app_localizations.dart';

class ProfileSecurityPage extends StatefulWidget {
  const ProfileSecurityPage({super.key});

  @override
  State<ProfileSecurityPage> createState() => _ProfileSecurityPageState();
}

class _ProfileSecurityPageState extends State<ProfileSecurityPage> {
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _pinController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Load profile security data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authState = context.read<AuthBloc>().state;
      if (authState is auth_state.AuthAuthenticated) {
        context.read<ProfileSecurityBloc>().add(
              ProfileSecurityLoadRequested(userId: authState.user.id),
            );
      }
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    _pinController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.teal,
      body: BlocConsumer<ProfileSecurityBloc, ProfileSecurityState>(
        listener: (context, state) {
          if (state is ProfileSecurityError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          } else if (state is ProfileSecurityOperationSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is AccountDeletionSuccess) {
            context.go('/auth');
          }
        },
        builder: (context, state) {
          if (state is ProfileSecurityLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is! ProfileSecurityLoaded) {
            return Center(
                child: Text(AppLocalizations.of(context).loadingError));
          }

          // Update controllers with current data
          if (_nameController.text.isEmpty) {
            _nameController.text = state.user.displayName;
          }
          if (_emailController.text.isEmpty) {
            _emailController.text = state.user.email;
          }

          return Stack(
            children: [
              Column(
                children: [
                  // Header with teal background
                  Container(
                    padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
                    child: Row(
                      children: [
                        GestureDetector(
                          onTap: () => context.pop(),
                          child: Container(
                            width: 48,
                            height: 48,
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Icon(
                              Icons.arrow_back,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Text(
                            'Profil et Sécurité',
                            style: AppTextStyles.headlineMedium.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Content container with white background
                  Expanded(
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(24),
                          topRight: Radius.circular(24),
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 8,
                            offset: const Offset(0, -4),
                          ),
                        ],
                      ),
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildProfileSection(state),
                            const SizedBox(height: 24),
                            _buildSecuritySection(state),
                            const SizedBox(height: 24),
                            _buildDangerZoneSection(state),
                            const SizedBox(height: 32),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),

              // Loading overlay for operations
              if (state is ProfileUpdating ||
                  state is SecuritySettingsUpdating ||
                  state is AvatarUploading)
                LoadingOverlay(
                  message: _getLoadingMessage(state),
                ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildProfileSection(ProfileSecurityLoaded state) {
    return SettingsSection(
      title: 'Profil',
      children: [
        // Avatar section
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              GestureDetector(
                onTap: () => _showAvatarOptions(context),
                child: Stack(
                  children: [
                    CircleAvatar(
                      radius: 40,
                      backgroundColor: AppColors.teal.withValues(alpha: 0.1),
                      backgroundImage: state.avatarUrl != null
                          ? NetworkImage(state.avatarUrl!)
                          : null,
                      child: state.avatarUrl == null
                          ? Text(
                              state.user.initials,
                              style: AppTextStyles.headlineMedium.copyWith(
                                color: AppColors.teal,
                              ),
                            )
                          : null,
                    ),
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: AppColors.teal,
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 2),
                        ),
                        child: const Icon(
                          Icons.camera_alt,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      state.user.displayName,
                      style: AppTextStyles.titleLarge,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      state.user.email,
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.grey600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        SettingsTile(
          icon: Icons.person_outline,
          title: 'Modifier le nom',
          subtitle: state.user.displayName,
          onTap: () => _showEditNameDialog(context),
        ),

        SettingsTile(
          icon: Icons.email_outlined,
          title: 'Modifier l\'email',
          subtitle: state.user.email,
          onTap: () => _showEditEmailDialog(context),
        ),
      ],
    );
  }

  Widget _buildSecuritySection(ProfileSecurityLoaded state) {
    return SettingsSection(
      title: 'Sécurité',
      children: [
        SettingsTile(
          icon: Icons.lock_outline,
          title: 'Changer le mot de passe',
          subtitle: 'Dernière modification: il y a 30 jours',
          onTap: () => _showChangePasswordDialog(context),
        ),
        SettingsTile(
          icon: Icons.fingerprint,
          title: 'Authentification biométrique',
          subtitle: state.isBiometricAvailable
              ? (state.securitySettings.biometricEnabled
                  ? 'Activée'
                  : 'Désactivée')
              : 'Non disponible',
          enabled: state.isBiometricAvailable,
          trailing: state.isBiometricAvailable
              ? Switch(
                  value: state.securitySettings.biometricEnabled,
                  onChanged: (value) {
                    context.read<ProfileSecurityBloc>().add(
                          BiometricToggleRequested(enabled: value),
                        );
                  },
                  activeThumbColor: AppColors.teal,
                )
              : null,
        ),
        SettingsTile(
          icon: Icons.pin_outlined,
          title: 'Code PIN',
          subtitle:
              state.securitySettings.pinEnabled ? 'Configuré' : 'Non configuré',
          onTap: () => state.securitySettings.pinEnabled
              ? _showRemovePinDialog(context)
              : _showSetupPinDialog(context),
          trailing: state.securitySettings.pinEnabled
              ? const Icon(Icons.check_circle, color: AppColors.success)
              : const Icon(Icons.add_circle_outline, color: AppColors.grey400),
        ),
        SettingsTile(
          icon: Icons.lock_clock_outlined,
          title: 'Verrouillage automatique',
          subtitle: state.securitySettings.autoLockEnabled
              ? 'Après ${state.securitySettings.autoLockMinutes} minutes'
              : 'Désactivé',
          trailing: Switch(
            value: state.securitySettings.autoLockEnabled,
            onChanged: (value) {
              context.read<ProfileSecurityBloc>().add(
                    AutoLockToggleRequested(
                      enabled: value,
                      minutes: state.securitySettings.autoLockMinutes,
                    ),
                  );
            },
            activeThumbColor: AppColors.teal,
          ),
        ),
        SettingsTile(
          icon: Icons.timer_outlined,
          title: 'Expiration de session',
          subtitle: state.securitySettings.sessionTimeout
              ? 'Après ${state.securitySettings.sessionTimeoutMinutes} minutes'
              : 'Désactivée',
          trailing: Switch(
            value: state.securitySettings.sessionTimeout,
            onChanged: (value) {
              context.read<ProfileSecurityBloc>().add(
                    SessionTimeoutToggleRequested(
                      enabled: value,
                      minutes: state.securitySettings.sessionTimeoutMinutes,
                    ),
                  );
            },
            activeThumbColor: AppColors.teal,
          ),
        ),
      ],
    );
  }

  Widget _buildDangerZoneSection(ProfileSecurityLoaded state) {
    return SettingsSection(
      title: 'Zone de danger',
      children: [
        SettingsTile(
          icon: Icons.delete_forever_outlined,
          title: 'Supprimer le compte',
          subtitle: 'Cette action est irréversible',
          iconColor: AppColors.error,
          onTap: () => _showDeleteAccountDialog(context),
        ),
      ],
    );
  }

  void _showAvatarOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('Prendre une photo'),
              onTap: () {
                Navigator.pop(context);
                _pickImage(ImageSource.camera);
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('Choisir depuis la galerie'),
              onTap: () {
                Navigator.pop(context);
                _pickImage(ImageSource.gallery);
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: AppColors.error),
              title: const Text('Supprimer l\'avatar'),
              onTap: () {
                Navigator.pop(context);
                context.read<ProfileSecurityBloc>().add(
                      const AvatarRemoveRequested(),
                    );
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: source);

    if (pickedFile != null) {
      context.read<ProfileSecurityBloc>().add(
            AvatarUploadRequested(imageFile: File(pickedFile.path)),
          );
    }
  }

  void _showEditNameDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Modifier le nom'),
        content: TextField(
          controller: _nameController,
          decoration: const InputDecoration(
            labelText: 'Nom complet',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              if (_nameController.text.trim().isNotEmpty) {
                context.read<ProfileSecurityBloc>().add(
                      ProfileNameUpdateRequested(
                          name: _nameController.text.trim()),
                    );
                Navigator.pop(context);
              }
            },
            child: const Text('Enregistrer'),
          ),
        ],
      ),
    );
  }

  void _showEditEmailDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Modifier l\'email'),
        content: TextField(
          controller: _emailController,
          decoration: const InputDecoration(
            labelText: 'Adresse email',
            border: OutlineInputBorder(),
          ),
          keyboardType: TextInputType.emailAddress,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              if (_emailController.text.trim().isNotEmpty) {
                context.read<ProfileSecurityBloc>().add(
                      EmailUpdateRequested(email: _emailController.text.trim()),
                    );
                Navigator.pop(context);
              }
            },
            child: const Text('Enregistrer'),
          ),
        ],
      ),
    );
  }

  void _showChangePasswordDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Changer le mot de passe'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _currentPasswordController,
              decoration: const InputDecoration(
                labelText: 'Mot de passe actuel',
                border: OutlineInputBorder(),
              ),
              obscureText: true,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _newPasswordController,
              decoration: const InputDecoration(
                labelText: 'Nouveau mot de passe',
                border: OutlineInputBorder(),
              ),
              obscureText: true,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _confirmPasswordController,
              decoration: const InputDecoration(
                labelText: 'Confirmer le mot de passe',
                border: OutlineInputBorder(),
              ),
              obscureText: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              _currentPasswordController.clear();
              _newPasswordController.clear();
              _confirmPasswordController.clear();
              Navigator.pop(context);
            },
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              if (_newPasswordController.text ==
                      _confirmPasswordController.text &&
                  _newPasswordController.text.length >= 6) {
                context.read<ProfileSecurityBloc>().add(
                      PasswordChangeRequested(
                        currentPassword: _currentPasswordController.text,
                        newPassword: _newPasswordController.text,
                      ),
                    );
                _currentPasswordController.clear();
                _newPasswordController.clear();
                _confirmPasswordController.clear();
                Navigator.pop(context);
              }
            },
            child: const Text('Changer'),
          ),
        ],
      ),
    );
  }

  void _showSetupPinDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Configurer le code PIN'),
        content: TextField(
          controller: _pinController,
          decoration: const InputDecoration(
            labelText: 'Code PIN (4-6 chiffres)',
            border: OutlineInputBorder(),
          ),
          keyboardType: TextInputType.number,
          maxLength: 6,
        ),
        actions: [
          TextButton(
            onPressed: () {
              _pinController.clear();
              Navigator.pop(context);
            },
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              if (_pinController.text.length >= 4) {
                context.read<ProfileSecurityBloc>().add(
                      PinSetupRequested(pinCode: _pinController.text),
                    );
                _pinController.clear();
                Navigator.pop(context);
              }
            },
            child: const Text('Configurer'),
          ),
        ],
      ),
    );
  }

  void _showRemovePinDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer le code PIN'),
        content:
            const Text('Êtes-vous sûr de vouloir supprimer votre code PIN ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              context.read<ProfileSecurityBloc>().add(
                    const PinRemoveRequested(),
                  );
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }

  void _showDeleteAccountDialog(BuildContext context) {
    final passwordController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer le compte'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Cette action est irréversible. Toutes vos données seront supprimées définitivement.',
            ),
            const SizedBox(height: 16),
            TextField(
              controller: passwordController,
              decoration: const InputDecoration(
                labelText: 'Mot de passe pour confirmer',
                border: OutlineInputBorder(),
              ),
              obscureText: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              passwordController.dispose();
              Navigator.pop(context);
            },
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              if (passwordController.text.isNotEmpty) {
                context.read<ProfileSecurityBloc>().add(
                      AccountDeleteRequested(password: passwordController.text),
                    );
                passwordController.dispose();
                Navigator.pop(context);
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('Supprimer définitivement'),
          ),
        ],
      ),
    );
  }

  String _getLoadingMessage(ProfileSecurityState state) {
    if (state is ProfileUpdating) {
      switch (state.section) {
        case 'name':
          return 'Mise à jour du nom...';
        case 'email':
          return 'Mise à jour de l\'email...';
        case 'avatar':
          return 'Suppression de l\'avatar...';
        case 'password':
          return 'Changement du mot de passe...';
        case 'delete':
          return 'Suppression du compte...';
        default:
          return 'Mise à jour...';
      }
    } else if (state is SecuritySettingsUpdating) {
      switch (state.section) {
        case 'biometric':
          return 'Configuration biométrique...';
        case 'pin':
          return 'Configuration du PIN...';
        case 'autolock':
          return 'Configuration du verrouillage...';
        case 'session':
          return 'Configuration de la session...';
        default:
          return 'Mise à jour de la sécurité...';
      }
    } else if (state is AvatarUploading) {
      return 'Téléchargement de l\'avatar...';
    }
    return 'Chargement...';
  }

  /// Gradient matching the white-blue background style
  LinearGradient _getHeaderGradient() {
    return const LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        Color(0xFFE3F0FF), // Light pastel blue
        Color(0xFFFFFFFF), // White bottom blend
      ],
    );
  }

  /// Integrated header card with title, subtitle, and back arrow
  Widget _buildIntegratedHeaderCard() {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Back arrow
          GestureDetector(
            onTap: () => context.pop(),
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.navy.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.arrow_back,
                color: AppColors.navy,
                size: 24,
              ),
            ),
          ),

          // Title and subtitle
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Profil & Sécurité',
                    style: AppTextStyles.titleLarge.copyWith(
                      color: AppColors.navy,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Gérer votre profil et paramètres de sécurité',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.grey600,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Action button (settings icon)
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.teal.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.security,
              color: AppColors.teal,
              size: 24,
            ),
          ),
        ],
      ),
    );
  }
}
