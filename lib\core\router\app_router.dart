import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../utils/supabase_utils.dart';
import '../../utils/logger.dart';
import '../../presentation/pages/settings/settings_page_redesign.dart';
import '../../presentation/bloc/auth/auth_bloc.dart';
import '../../presentation/bloc/auth/auth_state.dart' as auth_state;
import '../../presentation/pages/splash/splash_page.dart';
import '../../presentation/pages/auth/auth_page.dart';

import '../../presentation/pages/dashboard/new_dashboard_page.dart';
import '../../presentation/pages/medicine/medicine_list_page_redesign.dart';
import '../../presentation/pages/medicine/add_medicine_page_redesign.dart';
import '../../presentation/pages/medicine/edit_medicine_page.dart';
import '../../presentation/pages/medicine/medicine_detail_page.dart';
import '../../presentation/pages/medicine/my_medicines_page.dart';
import '../../presentation/bloc/medicine_detail/medicine_detail_bloc.dart';
import '../../presentation/bloc/add_medicine/add_medicine_bloc.dart';
import '../di/injection_container.dart';
import '../../presentation/pages/family/family_manager_page.dart';
import '../../presentation/pages/location/locations_page.dart';
import '../../presentation/pages/profile/profile_page.dart';

import '../../presentation/pages/settings/personalization_page.dart';
import '../../presentation/pages/settings/notification_settings_page.dart';
import '../../presentation/pages/settings/language_settings_page.dart';
import '../../presentation/pages/settings/data_management_page.dart';
import '../../presentation/pages/reminders/reminders_page.dart';
import '../../presentation/pages/reminders/add_reminder_page.dart';
import '../../presentation/pages/reminders/add_reminder_test_page.dart';
import '../../presentation/bloc/reminder/reminder_bloc.dart';
import '../../presentation/pages/debug/reminder_debug_page.dart';
import '../../presentation/pages/reminders/edit_reminder_page.dart';
import '../../presentation/pages/reminders/reminder_detail_page.dart';
import '../../features/debug/debug_page.dart';
import '../../presentation/pages/debug/medicine_card_debug_page.dart';
import '../../presentation/pages/debug/add_medicine_debug_page.dart';
import '../../presentation/pages/debug/add_medicine_full_debug_page.dart';
import '../../presentation/widgets/layout/main_layout.dart';
import '../../presentation/bloc/household/household_bloc.dart';
import '../../presentation/pages/household/household_onboarding_page.dart';
import '../../presentation/pages/household/join_household_page.dart';
import '../../presentation/pages/household/household_customize_page.dart';
import '../../presentation/pages/household/household_management_page.dart';
import '../../presentation/bloc/household_management/household_management_bloc.dart';
import '../../presentation/pages/onboarding/onboarding_page.dart';

/// Application router configuration that mirrors the web app's routing
class AppRouter {
  static final _rootNavigatorKey = GlobalKey<NavigatorState>();
  static final _shellNavigatorKey = GlobalKey<NavigatorState>();

  static GoRouter get router => _router;

  static final GoRouter _router = GoRouter(
    navigatorKey: _rootNavigatorKey,
    initialLocation: '/splash',
    debugLogDiagnostics: true,
    redirect: (context, state) {
      final authState = context.read<AuthBloc>().state;
      final isOnAuthPage = state.matchedLocation == '/auth';
      final isOnSplashPage = state.matchedLocation == '/splash';
      final isOnHouseholdOnboardingPage =
          state.matchedLocation == '/household-onboarding';
      final isOnOnboardingPage = state.matchedLocation == '/onboarding';

      // Always allow splash page
      if (isOnSplashPage) {
        return null;
      }

      // Handle authentication states
      if (authState is auth_state.AuthUnauthenticated) {
        final redirect = isOnAuthPage ? null : '/auth';
        if (redirect != null) {
          AppLogger.log(
              '🔄 Router: Redirecting unauthenticated user to $redirect');
        }
        return redirect;
      }

      if (authState is auth_state.AuthOnboardingRequired) {
        final redirect = isOnOnboardingPage ? null : '/onboarding';
        if (redirect != null) {
          AppLogger.log(
              '🔄 Router: Redirecting user needing onboarding to $redirect');
        }
        return redirect;
      }

      if (authState is auth_state.AuthAuthenticated ||
          authState is auth_state.AuthProfileRefreshed) {
        // Redirect away from auth/onboarding pages if already authenticated
        if (isOnAuthPage || isOnHouseholdOnboardingPage || isOnOnboardingPage) {
          AppLogger.log(
              '🔄 Router: Redirecting authenticated user to /dashboard');
          return '/dashboard';
        }
      }

      return null;
    },
    routes: [
      // Splash Route
      GoRoute(
        path: '/splash',
        name: 'splash',
        builder: (context, state) => const SplashPage(),
      ),

      // Auth Route
      GoRoute(
        path: '/auth',
        name: 'auth',
        builder: (context, state) => const AuthPage(),
      ),

      // Household Onboarding Routes
      GoRoute(
        path: '/household-onboarding',
        name: 'household-onboarding',
        builder: (context, state) => BlocProvider(
          create: (context) => getIt<HouseholdBloc>(),
          child: const HouseholdOnboardingPage(),
        ),
      ),

      GoRoute(
        path: '/join-household',
        name: 'join-household',
        builder: (context, state) => BlocProvider(
          create: (context) => getIt<HouseholdBloc>(),
          child: const JoinHouseholdPage(),
        ),
      ),

      GoRoute(
        path: '/household-customize',
        name: 'household-customize',
        builder: (context, state) => const HouseholdCustomizePage(),
      ),

      GoRoute(
        path: '/onboarding',
        name: 'onboarding',
        builder: (context, state) => const OnboardingPage(),
      ),

      // Main Shell Route with Bottom Navigation (for all pages)
      ShellRoute(
        navigatorKey: _shellNavigatorKey,
        builder: (context, state, child) {
          return MainLayout(child: child);
        },
        routes: [
          // Dashboard Route
          GoRoute(
            path: '/dashboard',
            name: 'dashboard',
            builder: (context, state) => NewDashboardPage(),
          ),
          // Medicine Routes
          GoRoute(
            path: '/medicines',
            name: 'medicines',
            builder: (context, state) {
              final categoryFilter = state.uri.queryParameters['category'];
              final searchQuery = state.uri.queryParameters['search'];
              return MedicineListPageRedesign(
                categoryFilter: categoryFilter,
                searchQuery: searchQuery,
              );
            },
            routes: [
              // My Medicines (Enhanced inventory)
              GoRoute(
                path: '/my',
                name: 'my-medicines',
                builder: (context, state) {
                  final householdId = SupabaseUtils.getHouseholdId(context);
                  final filterParam = state.uri.queryParameters['filter'];
                  return MyMedicinesPage(
                    householdId: householdId ?? '',
                    initialFilter: filterParam,
                  );
                },
              ),
              GoRoute(
                path: '/add',
                name: 'add-medicine',
                builder: (context, state) => BlocProvider(
                  create: (context) => getIt<AddMedicineBloc>(),
                  child: const AddMedicinePageRedesign(),
                ),
              ),
              GoRoute(
                path: '/:id',
                name: 'medicine-detail',
                builder: (context, state) {
                  final medicineId = state.pathParameters['id']!;
                  return BlocProvider(
                    create: (context) => getIt<MedicineDetailBloc>(),
                    child: MedicineDetailPage(medicineId: medicineId),
                  );
                },
                routes: [
                  GoRoute(
                    path: '/edit',
                    name: 'edit-medicine',
                    builder: (context, state) {
                      final medicineId = state.pathParameters['id']!;
                      return EditMedicinePage(medicineId: medicineId);
                    },
                  ),
                ],
              ),
            ],
          ),

          // Profile Route
          GoRoute(
            path: '/profile',
            name: 'profile',
            builder: (context, state) => const ProfilePage(),
          ),

          // Locations Route
          GoRoute(
            path: '/locations',
            name: 'locations',
            builder: (context, state) {
              final householdId = SupabaseUtils.getHouseholdId(context);
              return LocationsPage(householdId: householdId ?? '');
            },
          ),

          // Family Manager Route
          GoRoute(
            path: '/family',
            name: 'family-manager',
            builder: (context, state) {
              final householdId = SupabaseUtils.getHouseholdId(context);
              return FamilyManagerPage(householdId: householdId ?? '');
            },
          ),

          // Household Management Route
          GoRoute(
            path: '/household-management',
            name: 'household-management',
            builder: (context, state) => BlocProvider(
              create: (context) => getIt<HouseholdManagementBloc>(),
              child: const HouseholdManagementPage(),
            ),
          ),

          // Reminders Route
          GoRoute(
            path: '/reminders',
            name: 'reminders',
            builder: (context, state) => const RemindersPage(),
            routes: [
              GoRoute(
                path: '/add',
                name: 'add-reminder',
                builder: (context, state) {
                  return const AddReminderPage();
                },
              ),
              GoRoute(
                path: '/details/:id',
                name: 'reminder-detail',
                builder: (context, state) {
                  final reminderId = state.pathParameters['id']!;
                  final extra = state.extra as Map<String, dynamic>?;
                  return ReminderDetailPage(
                    reminderId: reminderId,
                    reminder: extra?['reminder'],
                    medicine: extra?['medicine'],
                  );
                },
              ),
              GoRoute(
                path: '/edit/:id',
                name: 'edit-reminder',
                builder: (context, state) {
                  final reminderId = state.pathParameters['id']!;
                  final extra = state.extra as Map<String, dynamic>?;
                  return EditReminderPage(
                    reminderId: reminderId,
                    reminder: extra?['reminder'],
                    medicine: extra?['medicine'],
                  );
                },
              ),
            ],
          ),

          // Settings Route
          GoRoute(
            path: '/settings',
            name: 'settings',
            builder: (context, state) => const SettingsPageRedesign(),
            routes: [
              // Personalization sub-route
              GoRoute(
                path: '/personalization',
                name: 'personalization',
                builder: (context, state) => const PersonalizationPage(),
              ),
              // Notification Settings sub-route
              GoRoute(
                path: '/notifications',
                name: 'notification-settings',
                builder: (context, state) => const NotificationSettingsPage(),
              ),
              // Language Settings sub-route
              GoRoute(
                path: '/language',
                name: 'language-settings',
                builder: (context, state) => const LanguageSettingsPage(),
              ),
              // Data Management sub-route
              GoRoute(
                path: '/data',
                name: 'data-management',
                builder: (context, state) => const DataManagementPage(),
              ),
            ],
          ),

          // Debug/Test Routes
          GoRoute(
            path: '/debug',
            name: 'debug',
            builder: (context, state) => const DebugPage(),
          ),
          GoRoute(
            path: '/debug/medicine-cards',
            name: 'medicine-card-debug',
            builder: (context, state) => const MedicineCardDebugPage(),
          ),
          GoRoute(
            path: '/debug/add-medicine',
            name: 'add-medicine-debug',
            builder: (context, state) => const AddMedicineDebugPage(),
          ),
          GoRoute(
            path: '/debug/reminders',
            name: 'reminder-debug',
            builder: (context, state) => BlocProvider(
              create: (context) => getIt<ReminderBloc>(),
              child: const ReminderDebugPage(),
            ),
          ),
          GoRoute(
            path: '/debug/add-medicine-full',
            name: 'add-medicine-full-debug',
            builder: (context, state) => const AddMedicineFullDebugPage(),
          ),
          // Temporary route to access the new AddReminder test flow
          GoRoute(
            path: '/debug/add-reminder-test',
            name: 'add-reminder-test',
            builder: (context, state) => BlocProvider(
              create: (context) => getIt<ReminderBloc>(),
              child: const AddReminderTestPage(),
            ),
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'Page not found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'The page "${state.matchedLocation}" does not exist.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go('/dashboard'),
              child: const Text('Return to Dashboard'),
            ),
          ],
        ),
      ),
    ),
  );
}
