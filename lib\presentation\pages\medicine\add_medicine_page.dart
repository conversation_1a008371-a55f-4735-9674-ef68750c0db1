import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/debug_logger.dart';
import '../../../core/di/injection_container.dart';
import '../../bloc/add_medicine/add_medicine_bloc.dart';
import '../../bloc/add_medicine/add_medicine_event.dart';
import '../../bloc/add_medicine/add_medicine_state.dart';
import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/auth/auth_state.dart' as auth_state;

import '../../widgets/medicine/medicine_search_widget.dart';
import '../../widgets/medicine/tag_selection_widget.dart';

class AddMedicinePage extends StatelessWidget {
  const AddMedicinePage({super.key});

  @override
  Widget build(BuildContext context) {
    DebugLogger.logUI(
        'AddMedicinePage', 'build() method called - creating BLoC provider');
    return BlocProvider(
      create: (context) {
        DebugLogger.startAudit('Production AddMedicineBloc Creation');

        // Comprehensive auth state debugging (same as debug page)
        DebugLogger.logAuth('Reading auth state from context');
        final authBloc = context.read<AuthBloc>();
        final authState = authBloc.state;

        DebugLogger.logAuth('Auth state type: ${authState.runtimeType}');
        DebugLogger.logAuth('Auth state details', data: {
          'isAuthenticated': authState is auth_state.AuthAuthenticated,
          'state': authState.toString(),
        });

        String householdId = '';
        if (authState is auth_state.AuthAuthenticated) {
          householdId = authState.householdId ?? '';
          DebugLogger.logAuth('Authenticated user found', data: {
            'userId': authState.user.id,
            'email': authState.user.email,
            'householdId': householdId,
            'householdName': authState.householdName,
            'householdIdIsEmpty': householdId.isEmpty,
            'householdIdLength': householdId.length,
          });
        } else {
          DebugLogger.logAuth('User not authenticated', data: {
            'stateType': authState.runtimeType.toString(),
          });
        }

        DebugLogger.logAuth(
            'Final household ID for production BLoC: "$householdId"');

        // Create BLoC instance exactly like debug page (working approach)
        final bloc = AddMedicineBloc(
          getIt(),
          getIt(),
          getIt(),
          getIt(),
          getIt(),
        );

        // Initialize with immediate dropdown loading (persistent approach)
        DebugLogger.logBloc(
            'Initializing production AddMedicineBloc with persistent dropdown loading for household ID: "$householdId"');
        bloc.add(AddMedicineInitialized(householdId: householdId));

        DebugLogger.endAudit('Production AddMedicineBloc Creation');
        return bloc;
      },
      child: const _AddMedicineView(),
    );
  }
}

class _AddMedicineView extends StatefulWidget {
  const _AddMedicineView();

  @override
  State<_AddMedicineView> createState() => _AddMedicineViewState();
}

class _AddMedicineViewState extends State<_AddMedicineView> {
  final _formKey = GlobalKey<FormState>();
  final _customNameController = TextEditingController();
  final _dosageController = TextEditingController();
  final _quantityController = TextEditingController();
  final _lowStockController = TextEditingController();
  final _notesController = TextEditingController();

  final List<String> _medicineForm = [
    'Comprimé',
    'Gélule',
    'Sirop',
    'Injection',
    'Pommade',
    'Gouttes',
    'Spray',
    'Suppositoire',
    'Autre',
  ];

  @override
  void initState() {
    super.initState();
    _quantityController.text = '1';
    _lowStockController.text = '0';
  }

  @override
  void dispose() {
    _customNameController.dispose();
    _dosageController.dispose();
    _quantityController.dispose();
    _lowStockController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _updateControllersFromState(AddMedicineFormState state) {
    DebugLogger.logUI('AddMedicinePage', 'Updating controllers from state',
        data: {
          'isCustomMode': state.isCustomMode,
          'customNameInState': state.getFormValue<String>('customName'),
          'currentControllerText': _customNameController.text,
          'allFormData': state.formData,
        });

    // Update dosage controller when medicine is selected
    if (!state.isCustomMode && state.selectedMedicine != null) {
      final dosage = state.selectedMedicine!.dosage ?? '';
      if (_dosageController.text != dosage) {
        _dosageController.text = dosage;
      }
    }

    // Update custom name controller in custom mode
    if (state.isCustomMode) {
      final customName = state.getFormValue<String>('customName') ?? '';
      DebugLogger.logUI(
          'AddMedicinePage', 'Custom mode controller update check',
          data: {
            'customNameFromState': customName,
            'currentControllerText': _customNameController.text,
            'shouldUpdate': _customNameController.text != customName,
            'customNameIsEmpty': customName.isEmpty,
            'controllerIsEmpty': _customNameController.text.isEmpty,
          });

      if (_customNameController.text != customName) {
        DebugLogger.logUI('AddMedicinePage', 'Updating custom name controller',
            data: {
              'oldValue': _customNameController.text,
              'newValue': customName,
              'isCustomMode': state.isCustomMode,
            });
        _customNameController.text = customName;
      }
    } else {
      // Clear custom name controller when not in custom mode
      if (_customNameController.text.isNotEmpty) {
        DebugLogger.logUI('AddMedicinePage',
            'Clearing custom name controller (not in custom mode)');
        _customNameController.clear();
      }
    }

    // Update quantity controller
    final quantity = state.getFormValue<int>('quantity')?.toString() ?? '1';
    if (_quantityController.text != quantity) {
      _quantityController.text = quantity;
    }

    // Update low stock threshold controller
    final lowStock =
        state.getFormValue<int>('lowStockThreshold')?.toString() ?? '5';
    if (_lowStockController.text != lowStock) {
      _lowStockController.text = lowStock;
    }

    // Update notes controller
    final notes = state.getFormValue<String>('notes') ?? '';
    if (_notesController.text != notes) {
      _notesController.text = notes;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocListener<AddMedicineBloc, AddMedicineState>(
        listener: (context, state) {
          DebugLogger.logUI(
              'ProductionPage', 'BLoC state changed: ${state.runtimeType}');

          if (state is AddMedicineFormState) {
            DebugLogger.logUI('ProductionPage', 'Form state updated', data: {
              'availableLocationsCount': state.availableLocations.length,
              'availableFamilyMembersCount':
                  state.availableFamilyMembers.length,
              'selectedLocationId': state.selectedLocation?.id,
              'selectedMemberId': state.selectedFamilyMember?.id,
              'householdId': state.householdId,
            });
            // Update controllers when medicine is selected
            _updateControllersFromState(state);
          } else if (state is AddMedicineSuccess) {
            DebugLogger.logUI('ProductionPage', 'Success state received');
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.success,
              ),
            );
            // Navigate back to dashboard instead of popping
            context.go('/dashboard');
          } else if (state is AddMedicineError) {
            DebugLogger.logUI('ProductionPage', 'Error state received',
                data: {'error': state.message});
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          }
        },
        child: Column(
          children: [
            // Integrated header with gradient background
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: _getHeaderGradient(),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(32),
                  bottomRight: Radius.circular(32),
                ),
              ),
              child: SafeArea(
                bottom: false,
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(20, 20, 20, 40),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Integrated header card
                      _buildIntegratedHeaderCard(),
                    ],
                  ),
                ),
              ),
            ),

            // Form content
            Expanded(
              child: BlocBuilder<AddMedicineBloc, AddMedicineState>(
                builder: (context, state) {
                  if (state is AddMedicineLoading) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }

                  if (state is! AddMedicineFormState) {
                    return const Center(
                      child: Text('Erreur lors du chargement'),
                    );
                  }

                  return Form(
                    key: _formKey,
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Medicine search or custom entry
                          _buildMedicineSelectionSection(state),
                          const SizedBox(height: 24),

                          // Custom name field (only in custom mode)
                          if (state.isCustomMode) ...[
                            _buildCustomNameField(state),
                            const SizedBox(height: 24),
                          ],

                          // Form fields section
                          _buildFormFieldsSection(state),
                          const SizedBox(height: 24),

                          // Tag selection
                          const TagSelectionWidget(),
                          const SizedBox(height: 24),

                          // Location and family member selection
                          _buildSelectionSection(state),
                          const SizedBox(height: 24),

                          // Notes section
                          _buildNotesSection(state),
                          const SizedBox(height: 32),

                          // Submit button
                          _buildSubmitButton(state),
                          const SizedBox(height: 16),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to get valid form value for dropdown
  String? _getValidFormValue(AddMedicineFormState state) {
    final formValue = state.getFormValue<String>('form');
    if (formValue == null) return null;

    // If the form value is in our predefined list, use it
    if (_medicineForm.contains(formValue)) {
      return formValue;
    }

    // If we have a selected medicine and its form is not in predefined list,
    // we need to include it in the dropdown items
    if (state.selectedMedicine != null && !state.isCustomMode) {
      return formValue; // This will be handled by _buildFormDropdownItems
    }

    // In custom mode, allow any value
    if (state.isCustomMode) {
      return formValue;
    }

    // Default to null to avoid assertion error
    return null;
  }

  // Helper method to build form dropdown items including dynamic values
  List<DropdownMenuItem<String>> _buildFormDropdownItems(
      AddMedicineFormState state) {
    final items = <DropdownMenuItem<String>>[];

    // Add predefined form types
    for (final form in _medicineForm) {
      items.add(DropdownMenuItem(
        value: form,
        child: SizedBox(
          width: double.infinity,
          child: Text(
            form,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
      ));
    }

    // Add selected medicine's form if it's not in predefined list
    final selectedForm = state.getFormValue<String>('form');
    if (selectedForm != null &&
        !_medicineForm.contains(selectedForm) &&
        !state.isCustomMode) {
      items.add(DropdownMenuItem(
        value: selectedForm,
        child: SizedBox(
          width: double.infinity,
          child: Text(
            '$selectedForm (sélectionné)',
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
      ));
    }

    return items;
  }

  // Helper method to build dosage dropdown from available dosage forms
  Widget _buildDosageDropdown(AddMedicineFormState state) {
    final uniqueDosages = state.availableDosageForms
        .map((medicine) => medicine.dosage)
        .where((dosage) => dosage != null && dosage.isNotEmpty)
        .toSet()
        .toList();

    return DropdownButtonFormField<String>(
      initialValue: state.getFormValue<String>('dosage'),
      decoration: const InputDecoration(
        labelText: 'Dosage *',
        prefixIcon: Icon(Icons.science),
      ),
      isExpanded: true,
      items: uniqueDosages.map((dosage) {
        return DropdownMenuItem(
          value: dosage,
          child: SizedBox(
            width: double.infinity,
            child: Text(
              dosage!,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          context.read<AddMedicineBloc>().add(
                FormFieldUpdated(field: 'dosage', value: value),
              );
        }
      },
    );
  }

  // Helper method to build form dropdown from available dosage forms
  Widget _buildFormDropdown(AddMedicineFormState state) {
    final uniqueForms = state.availableDosageForms
        .map((medicine) => medicine.forme)
        .where((form) => form != null && form.isNotEmpty)
        .toSet()
        .toList();

    return DropdownButtonFormField<String>(
      initialValue: state.getFormValue<String>('form'),
      decoration: const InputDecoration(
        labelText: 'Forme *',
        prefixIcon: Icon(Icons.category),
      ),
      isExpanded: true,
      items: uniqueForms.map((form) {
        return DropdownMenuItem(
          value: form,
          child: SizedBox(
            width: double.infinity,
            child: Text(
              form!,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          context.read<AddMedicineBloc>().add(
                FormFieldUpdated(field: 'form', value: value),
              );
          // When both dosage and form are selected, find the matching medicine and trigger selection
          final selectedDosage = state.getFormValue<String>('dosage');
          if (selectedDosage != null) {
            final matchingMedicine = state.availableDosageForms.firstWhere(
              (medicine) =>
                  medicine.dosage == selectedDosage && medicine.forme == value,
              orElse: () => state.availableDosageForms.first,
            );
            context.read<AddMedicineBloc>().add(
                  DosageFormSelected(medicine: matchingMedicine),
                );
          }
        }
      },
    );
  }

  Widget _buildMedicineSelectionSection(AddMedicineFormState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Sélection du médicament',
              style: AppTextStyles.titleMedium,
            ),
            if (!state.isCustomMode)
              TextButton.icon(
                onPressed: () {
                  context
                      .read<AddMedicineBloc>()
                      .add(const ModeToggled(isCustomMode: true));
                },
                style: TextButton.styleFrom(
                  foregroundColor: AppColors.teal,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: BorderSide(
                        color: AppColors.teal.withValues(alpha: 0.3)),
                  ),
                ),
                icon: const Icon(Icons.add_circle_outline, size: 18),
                label: Text(
                  'Personnalisé',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.teal,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 16),
        if (!state.isCustomMode) ...[
          const MedicineSearchWidget(),
        ] else ...[
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.teal.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.teal.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.edit,
                  color: AppColors.teal,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Mode personnalisé activé - Saisissez les informations manuellement',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.teal,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                TextButton.icon(
                  onPressed: () {
                    context
                        .read<AddMedicineBloc>()
                        .add(const ModeToggled(isCustomMode: false));
                  },
                  style: TextButton.styleFrom(
                    foregroundColor: AppColors.teal,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  ),
                  icon: const Icon(Icons.search, size: 16),
                  label: Text(
                    'Rechercher',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.teal,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildCustomNameField(AddMedicineFormState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Informations du médicament',
          style: AppTextStyles.titleMedium,
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _customNameController,
          decoration: InputDecoration(
            labelText: 'Nom du médicament *',
            hintText: 'Ex: Paracétamol',
            prefixIcon: const Icon(Icons.medication),
            errorText: state.getFormError('customName'),
          ),
          onChanged: (value) {
            context.read<AddMedicineBloc>().add(
                  FormFieldUpdated(field: 'customName', value: value),
                );
          },
          validator: (value) {
            if (state.isCustomMode && (value == null || value.trim().isEmpty)) {
              return 'Le nom du médicament est requis';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildFormFieldsSection(AddMedicineFormState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Détails et quantité',
          style: AppTextStyles.titleMedium,
        ),
        const SizedBox(height: 16),

        // Dosage field - dropdown when medicine selected, text field in custom mode
        if (state.selectedMedicineName != null &&
            state.availableDosageForms.isNotEmpty &&
            !state.isCustomMode) ...[
          _buildDosageDropdown(state),
        ] else ...[
          TextFormField(
            controller: _dosageController,
            decoration: InputDecoration(
              labelText: 'Dosage',
              hintText: 'Ex: 500mg',
              prefixIcon: const Icon(Icons.science),
              enabled: state.isCustomMode,
            ),
            onChanged: state.isCustomMode
                ? (value) {
                    context.read<AddMedicineBloc>().add(
                          FormFieldUpdated(field: 'dosage', value: value),
                        );
                  }
                : null,
          ),
        ],
        const SizedBox(height: 16),

        // Form field - dropdown when medicine selected, predefined dropdown in custom mode
        if (state.selectedMedicineName != null &&
            state.availableDosageForms.isNotEmpty &&
            !state.isCustomMode) ...[
          _buildFormDropdown(state),
        ] else ...[
          ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: double.infinity),
            child: DropdownButtonFormField<String>(
              initialValue: _getValidFormValue(state),
              decoration: const InputDecoration(
                labelText: 'Forme',
                prefixIcon: Icon(Icons.category),
              ),
              items: _buildFormDropdownItems(state),
              onChanged: state.isCustomMode
                  ? (value) {
                      context.read<AddMedicineBloc>().add(
                            FormFieldUpdated(field: 'form', value: value),
                          );
                    }
                  : null,
              isExpanded: true, // This helps prevent overflow
            ),
          ),
        ],
        const SizedBox(height: 16),

        // Expiration date
        InkWell(
          onTap: () => _selectExpirationDate(context, state),
          child: InputDecorator(
            decoration: InputDecoration(
              labelText: 'Date d\'expiration',
              prefixIcon: const Icon(Icons.calendar_today),
              suffixIcon: const Icon(Icons.arrow_drop_down),
              errorText: state.getFormError('expiration'),
            ),
            child: Text(
              state.getFormValue<DateTime>('expiration') != null
                  ? DateFormat('MMMM yyyy', 'fr')
                      .format(state.getFormValue<DateTime>('expiration')!)
                  : 'Sélectionner le mois d\'expiration',
              style: state.getFormValue<DateTime>('expiration') != null
                  ? null
                  : TextStyle(color: Colors.grey[600]),
            ),
          ),
        ),
        const SizedBox(height: 16),

        // Quantity and low stock threshold
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _quantityController,
                decoration: InputDecoration(
                  labelText: 'Quantité *',
                  prefixIcon: const Icon(Icons.inventory_2),
                  errorText: state.getFormError('quantity'),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                onChanged: (value) {
                  final quantity = int.tryParse(value);
                  context.read<AddMedicineBloc>().add(
                        FormFieldUpdated(field: 'quantity', value: quantity),
                      );
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Requis';
                  }
                  final quantity = int.tryParse(value);
                  if (quantity == null || quantity <= 0) {
                    return 'Invalide';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                controller: _lowStockController,
                decoration: InputDecoration(
                  labelText: 'Seuil stock faible',
                  prefixIcon: const Icon(Icons.warning),
                  errorText: state.getFormError('lowStockThreshold'),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                onChanged: (value) {
                  final threshold = int.tryParse(value);
                  context.read<AddMedicineBloc>().add(
                        FormFieldUpdated(
                            field: 'lowStockThreshold', value: threshold),
                      );
                },
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    final threshold = int.tryParse(value);
                    if (threshold == null || threshold < 0) {
                      return 'Invalide';
                    }
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSelectionSection(AddMedicineFormState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Emplacement et membre de la famille',
          style: AppTextStyles.titleMedium,
        ),
        const SizedBox(height: 16),

        // Location selection
        Builder(
          builder: (context) {
            DebugLogger.logUI('LocationDropdown', 'Rendering dropdown', data: {
              'availableLocationsCount': state.availableLocations.length,
              'selectedLocationId': state.selectedLocation?.id,
              'selectedLocationName': state.selectedLocation?.displayName,
              'locations': state.availableLocations
                  .map((l) => {'id': l.id, 'name': l.displayName})
                  .toList(),
            });

            return DropdownButtonFormField<String>(
              initialValue: state.selectedLocation?.id,
              decoration: InputDecoration(
                labelText: 'Emplacement',
                prefixIcon: const Icon(Icons.location_on),
                helperText: state.availableLocations.isEmpty
                    ? 'Aucun emplacement disponible'
                    : '${state.availableLocations.length} emplacements disponibles',
              ),
              isExpanded: true,
              items: state.availableLocations.map((location) {
                DebugLogger.logUI('LocationDropdown', 'Creating dropdown item',
                    data: {
                      'locationId': location.id,
                      'locationName': location.displayName,
                    });
                return DropdownMenuItem(
                  value: location.id,
                  child: SizedBox(
                    width: double.infinity,
                    child: Text(
                      location.displayName,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                );
              }).toList(),
              onChanged: (value) {
                DebugLogger.logUI('LocationDropdown', 'Selection changed',
                    data: {
                      'selectedValue': value,
                    });
                if (value != null) {
                  final location = state.availableLocations
                      .where((l) => l.id == value)
                      .firstOrNull;
                  DebugLogger.logUI(
                      'LocationDropdown', 'Triggering LocationSelected event',
                      data: {
                        'locationId': location?.id,
                        'locationName': location?.displayName,
                      });
                  context.read<AddMedicineBloc>().add(
                        LocationSelected(location: location),
                      );
                }
              },
            );
          },
        ),
        const SizedBox(height: 16),

        // Family member selection
        Builder(
          builder: (context) {
            DebugLogger.logUI('FamilyMemberDropdown', 'Rendering dropdown',
                data: {
                  'availableMembersCount': state.availableFamilyMembers.length,
                  'selectedMemberId': state.selectedFamilyMember?.id,
                  'selectedMemberName': state.selectedFamilyMember?.displayName,
                  'members': state.availableFamilyMembers
                      .map((m) => {'id': m.id, 'name': m.displayName})
                      .toList(),
                });

            return DropdownButtonFormField<String>(
              initialValue: state.selectedFamilyMember?.id,
              decoration: InputDecoration(
                labelText: 'Membre de la famille',
                prefixIcon: const Icon(Icons.person),
                helperText: state.availableFamilyMembers.isEmpty
                    ? 'Aucun membre disponible'
                    : '${state.availableFamilyMembers.length} membres disponibles',
              ),
              isExpanded: true,
              items: state.availableFamilyMembers.map((member) {
                DebugLogger.logUI(
                    'FamilyMemberDropdown', 'Creating dropdown item',
                    data: {
                      'memberId': member.id,
                      'memberName': member.displayName,
                    });
                return DropdownMenuItem(
                  value: member.id,
                  child: SizedBox(
                    width: double.infinity,
                    child: Text(
                      member.displayName,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                );
              }).toList(),
              onChanged: (value) {
                DebugLogger.logUI('FamilyMemberDropdown', 'Selection changed',
                    data: {
                      'selectedValue': value,
                    });
                if (value != null) {
                  final member = state.availableFamilyMembers
                      .where((m) => m.id == value)
                      .firstOrNull;
                  DebugLogger.logUI('FamilyMemberDropdown',
                      'Triggering FamilyMemberSelected event',
                      data: {
                        'memberId': member?.id,
                        'memberName': member?.displayName,
                      });
                  context.read<AddMedicineBloc>().add(
                        FamilyMemberSelected(member: member),
                      );
                }
              },
            );
          },
        ),
      ],
    );
  }

  Widget _buildNotesSection(AddMedicineFormState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Notes et commentaires',
          style: AppTextStyles.titleMedium,
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _notesController,
          decoration: const InputDecoration(
            labelText: 'Notes (optionnel)',
            hintText: 'Ajoutez des notes ou commentaires...',
            prefixIcon: Icon(Icons.note),
            alignLabelWithHint: true,
          ),
          maxLines: 3,
          onChanged: (value) {
            context.read<AddMedicineBloc>().add(
                  FormFieldUpdated(field: 'notes', value: value),
                );
          },
        ),
      ],
    );
  }

  Widget _buildSubmitButton(AddMedicineFormState state) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: state.isSubmitting || !state.isValid
            ? null
            : () {
                if (_formKey.currentState?.validate() ?? false) {
                  context
                      .read<AddMedicineBloc>()
                      .add(MedicineSubmitted(context: context));
                } else {
                  context.read<AddMedicineBloc>().add(const FormValidated());
                }
              },
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.teal,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: state.isSubmitting
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Text(
                'Ajouter le médicament',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }

  Future<void> _selectExpirationDate(
      BuildContext context, AddMedicineFormState state) async {
    final currentExpiration = state.getFormValue<DateTime>('expiration');
    final initialDate =
        currentExpiration ?? DateTime.now().add(const Duration(days: 365));

    final bloc = context.read<AddMedicineBloc>();

    // Show month/year picker dialog
    final selectedDate = await _showMonthYearPicker(context, initialDate);

    if (selectedDate != null && mounted) {
      // Store as first day of selected month (web app format)
      final firstDayOfMonth =
          DateTime(selectedDate.year, selectedDate.month, 1);
      bloc.add(FormFieldUpdated(field: 'expiration', value: firstDayOfMonth));
    }
  }

  Future<DateTime?> _showMonthYearPicker(
      BuildContext context, DateTime initialDate) async {
    DateTime selectedDate = initialDate;

    return showDialog<DateTime>(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Sélectionner le mois d\'expiration'),
              content: SizedBox(
                width: 300,
                height: 300,
                child: Column(
                  children: [
                    // Year selector
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        IconButton(
                          onPressed: () {
                            setState(() {
                              selectedDate = DateTime(
                                  selectedDate.year - 1, selectedDate.month);
                            });
                          },
                          icon: const Icon(Icons.chevron_left),
                        ),
                        Text(
                          '${selectedDate.year}',
                          style: AppTextStyles.titleMedium,
                        ),
                        IconButton(
                          onPressed: () {
                            setState(() {
                              selectedDate = DateTime(
                                  selectedDate.year + 1, selectedDate.month);
                            });
                          },
                          icon: const Icon(Icons.chevron_right),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    // Month grid
                    Expanded(
                      child: GridView.builder(
                        gridDelegate:
                            const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 3,
                          childAspectRatio: 2,
                          crossAxisSpacing: 8,
                          mainAxisSpacing: 8,
                        ),
                        itemCount: 12,
                        itemBuilder: (context, index) {
                          final month = index + 1;
                          final isSelected = selectedDate.month == month;
                          final monthName = DateFormat('MMM', 'fr')
                              .format(DateTime(2024, month));

                          return InkWell(
                            onTap: () {
                              setState(() {
                                selectedDate =
                                    DateTime(selectedDate.year, month);
                              });
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                color: isSelected
                                    ? AppColors.teal
                                    : Colors.transparent,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: isSelected
                                      ? AppColors.teal
                                      : AppColors.grey300,
                                ),
                              ),
                              child: Center(
                                child: Text(
                                  monthName,
                                  style: TextStyle(
                                    color: isSelected
                                        ? Colors.white
                                        : AppColors.grey700,
                                    fontWeight: isSelected
                                        ? FontWeight.w600
                                        : FontWeight.normal,
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Annuler'),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(selectedDate),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.teal,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Confirmer'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  /// Gradient matching the white-blue background style
  LinearGradient _getHeaderGradient() {
    return const LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        Color(0xFFE3F0FF), // Light pastel blue
        Color(0xFFFFFFFF), // White bottom blend
      ],
    );
  }

  /// Integrated header card with title and back arrow
  Widget _buildIntegratedHeaderCard() {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Row(
        children: [
          // Back arrow with proper styling
          IconButton(
            onPressed: () => context.go('/dashboard'),
            icon: Icon(
              Icons.arrow_back,
              color: AppColors.navy,
              size: 24,
            ),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(
              minWidth: 40,
              minHeight: 40,
            ),
          ),
          const SizedBox(width: 12),
          // Title
          Expanded(
            child: Text(
              'Ajouter un médicament',
              style: AppTextStyles.titleLarge.copyWith(
                color: AppColors.navy,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
