# 🔐 MedyTrack Mobile - Secrets and Environment Management

## 🚨 **CRITICAL SECURITY WARNING**

### **❌ NEVER COMMIT THESE FILES TO VERSION CONTROL:**
```
.env
.env.dev
.env.prod
.env.local
.env.*.local
db_sync_config.env
**/secrets/**
**/*.key
**/*.pem
```

### **✅ SAFE TO COMMIT:**
```
.env.template
.env.example
.env.*.template
.env.*.example
```

## 🔧 Environment Configuration System

### **1. Local Development Setup**

#### **Step 1: Create Environment Files**
```bash
# Copy templates to actual environment files
cp .env.dev.template .env.dev
cp .env.prod.template .env.prod

# Edit with your actual credentials (NEVER commit these!)
nano .env.dev
nano .env.prod
```

#### **Step 2: Configure Development Environment**
```bash
# .env.dev (for local development)
SUPABASE_URL=https://your-dev-project.supabase.co
SUPABASE_ANON_KEY=your-dev-anon-key-here
SUPABASE_SERVICE_KEY=your-dev-service-key-here
APP_NAME=MedyTrack Mobile (Dev)
APP_VERSION=0.5.1
APP_BUILD_NUMBER=1
ENABLE_DEBUG_PAGE=true
ENABLE_VERBOSE_LOGGING=true
ENABLE_NOTIFICATIONS=true
ENABLE_BIOMETRIC_AUTH=true
ENABLE_OFFLINE_MODE=true
ENABLE_ANALYTICS=false
API_TIMEOUT_SECONDS=30
MAX_RETRY_ATTEMPTS=3
```

#### **Step 3: Configure Production Environment**
```bash
# .env.prod (for production builds)
SUPABASE_URL=https://your-prod-project.supabase.co
SUPABASE_ANON_KEY=your-prod-anon-key-here
SUPABASE_SERVICE_KEY=your-prod-service-key-here
APP_NAME=MedyTrack Mobile
APP_VERSION=0.5.1
APP_BUILD_NUMBER=1
ENABLE_DEBUG_PAGE=false
ENABLE_VERBOSE_LOGGING=false
ENABLE_NOTIFICATIONS=true
ENABLE_BIOMETRIC_AUTH=true
ENABLE_OFFLINE_MODE=true
ENABLE_ANALYTICS=true
API_TIMEOUT_SECONDS=30
MAX_RETRY_ATTEMPTS=3
```

### **2. Runtime Environment Detection**

The application automatically detects the environment based on Flutter's build mode:

```dart
// lib/core/config/environment_config.dart
class EnvironmentConfig {
  static bool get isDebug => kDebugMode;
  static bool get isProduction => !kDebugMode;
  
  static String get environmentName => isDebug ? 'development' : 'production';
  
  static void initialize() {
    final envFile = isDebug ? '.env.dev' : '.env.prod';
    dotenv.load(fileName: envFile);
  }
}
```

### **3. Environment Validation**

Built-in validation prevents configuration mixups:

```dart
static void _validateEnvironment() {
  // Prevent dev/prod URL mixups
  if (isProduction && supabaseUrl.contains('dev')) {
    throw Exception('Production build cannot use development database!');
  }
  
  if (isDebug && supabaseUrl.contains('prod')) {
    throw Exception('Development build should not use production database!');
  }
  
  // Validate required variables
  if (supabaseUrl.isEmpty || supabaseAnonKey.isEmpty) {
    throw Exception('Missing required environment variables!');
  }
}
```

## 🔑 GitHub Secrets Configuration

### **Required Secrets**

Configure these secrets in your GitHub repository:
**Settings → Secrets and variables → Actions → New repository secret**

#### **Production Database Secrets**
```
PROD_SUPABASE_URL=https://your-prod-project.supabase.co
PROD_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
PROD_SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### **Development Database Secrets**
```
DEV_SUPABASE_URL=https://your-dev-project.supabase.co
DEV_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
DEV_SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### **Optional Notification Secrets**
```
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/...
EMAIL_SMTP_PASSWORD=your-smtp-password
```

### **Environment-Specific Secrets**

For environment-specific deployments, use GitHub Environments:

1. **Go to**: Settings → Environments
2. **Create environments**: `production`, `staging`, `development`
3. **Add environment-specific secrets**

#### **Production Environment Secrets**
```
SUPABASE_URL=https://prod-project.supabase.co
SUPABASE_ANON_KEY=prod-anon-key
DEPLOYMENT_URL=https://medytrack.app
```

#### **Staging Environment Secrets**
```
SUPABASE_URL=https://staging-project.supabase.co
SUPABASE_ANON_KEY=staging-anon-key
DEPLOYMENT_URL=https://staging.medytrack.app
```

## 🔄 Secure Key Rotation Procedures

### **1. Supabase Key Rotation**

#### **Step 1: Generate New Keys**
```bash
# In Supabase Dashboard:
# 1. Go to Settings → API
# 2. Click "Generate new anon key"
# 3. Click "Generate new service_role key"
# 4. Copy new keys (keep old ones active for now)
```

#### **Step 2: Update GitHub Secrets**
```bash
# Update secrets with new keys
# Keep old keys as backup (e.g., PROD_SUPABASE_ANON_KEY_OLD)
```

#### **Step 3: Deploy with New Keys**
```bash
# Deploy application with new keys
git tag -a v0.5.2 -m "Security: Rotate Supabase keys"
git push origin v0.5.2
```

#### **Step 4: Verify and Cleanup**
```bash
# After successful deployment:
# 1. Verify application works with new keys
# 2. Revoke old keys in Supabase Dashboard
# 3. Remove old backup secrets from GitHub
```

### **2. Emergency Key Revocation**

If keys are compromised:

```bash
# 1. IMMEDIATELY revoke compromised keys in Supabase
# 2. Generate new keys
# 3. Update GitHub secrets
# 4. Emergency deployment
./scripts/emergency_deploy.sh
```

## 🛡️ Security Best Practices

### **1. Local Development Security**

#### **Environment File Protection**
```bash
# Add to .gitignore (already included)
.env*
!.env*.template
!.env*.example

# Set restrictive permissions
chmod 600 .env.dev .env.prod
```

#### **IDE Configuration**
```json
// .vscode/settings.json
{
  "files.exclude": {
    "**/.env": true,
    "**/.env.dev": true,
    "**/.env.prod": true
  }
}
```

### **2. CI/CD Security**

#### **Secrets Validation in CI**
```yaml
# .github/workflows/security-check.yml
- name: Validate secrets format
  run: |
    # Check that secrets are properly formatted
    if [[ ! "${{ secrets.PROD_SUPABASE_URL }}" =~ ^https://[a-z0-9]+\.supabase\.co$ ]]; then
      echo "Invalid Supabase URL format"
      exit 1
    fi
```

#### **Environment Isolation**
```yaml
# Ensure production secrets are only used in production environment
environment: 
  name: production
  url: https://medytrack.app
```

### **3. Runtime Security**

#### **Secret Masking in Logs**
```dart
// All logging goes through AppLogger with automatic masking
AppLogger.log('Database URL: ${EnvironmentConfig.supabaseUrl}');
// Output: Database URL: https://*********.supabase.co
```

#### **Environment Validation**
```dart
// Automatic validation on app startup
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  try {
    await EnvironmentConfig.initialize();
    runApp(MyApp());
  } catch (e) {
    // Environment configuration error - app won't start
    runApp(ErrorApp(error: e.toString()));
  }
}
```

## 🔍 Security Monitoring

### **1. Secret Exposure Detection**

#### **Automated Scanning**
```bash
# Run in CI/CD pipeline
git secrets --scan
truffleHog --regex --entropy=False .
```

#### **Manual Verification**
```bash
# Check for accidentally committed secrets
grep -r "supabase" . --exclude-dir=.git --exclude="*.md" --exclude="*.template"
grep -r "eyJ" . --exclude-dir=.git --exclude="*.md"  # JWT tokens
```

### **2. Access Monitoring**

#### **GitHub Audit Log**
- Monitor secret access in GitHub audit log
- Set up alerts for secret modifications
- Regular review of secret usage

#### **Supabase Monitoring**
- Monitor API key usage in Supabase dashboard
- Set up alerts for unusual activity
- Regular review of database access logs

## 🚨 Incident Response

### **1. Secret Compromise Response**

#### **Immediate Actions (< 5 minutes)**
1. **Revoke compromised keys** in Supabase Dashboard
2. **Disable GitHub Actions** if needed
3. **Notify team** of security incident

#### **Short-term Actions (< 1 hour)**
1. **Generate new keys** in Supabase
2. **Update GitHub secrets** with new keys
3. **Emergency deployment** with new keys
4. **Verify application functionality**

#### **Long-term Actions (< 24 hours)**
1. **Investigate compromise source**
2. **Update security procedures**
3. **Document incident and lessons learned**
4. **Review and update access controls**

### **2. Emergency Contacts**

```
Security Team: <EMAIL>
DevOps Team: <EMAIL>
On-call Engineer: +1-xxx-xxx-xxxx
Supabase Support: <EMAIL>
```

## 📋 Security Checklist

### **Development Setup**
- [ ] Environment files created from templates
- [ ] Environment files added to .gitignore
- [ ] File permissions set to 600
- [ ] IDE configured to hide environment files
- [ ] Local environment validation working

### **GitHub Configuration**
- [ ] All required secrets configured
- [ ] Environment-specific secrets set up
- [ ] Secret access restricted to necessary workflows
- [ ] Audit logging enabled

### **CI/CD Security**
- [ ] Secrets validation in workflows
- [ ] Environment isolation configured
- [ ] No secrets in workflow logs
- [ ] Emergency deployment procedures tested

### **Monitoring**
- [ ] Secret scanning enabled
- [ ] Access monitoring configured
- [ ] Incident response procedures documented
- [ ] Team trained on security procedures

---

**🔐 Remember: Security is everyone's responsibility. When in doubt, ask the security team!**
