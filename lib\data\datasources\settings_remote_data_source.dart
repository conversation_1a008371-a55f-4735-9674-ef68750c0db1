import 'dart:convert';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../core/services/supabase_service.dart';
import '../models/settings_model.dart';

/// Abstract class for settings remote data source
abstract class SettingsRemoteDataSource {
  /// Get user settings from remote storage
  Future<SettingsModel?> getUserSettings(String userId);

  /// Save user settings to remote storage
  Future<SettingsModel> saveUserSettings(SettingsModel settings);

  /// Get notification settings from remote storage
  Future<NotificationSettingsModel?> getNotificationSettings(String userId);

  /// Save notification settings to remote storage
  Future<NotificationSettingsModel> saveNotificationSettings(
      String userId, NotificationSettingsModel settings);

  /// Get app settings from remote storage
  Future<AppSettingsModel?> getAppSettings(String userId);

  /// Save app settings to remote storage
  Future<AppSettingsModel> saveAppSettings(
      String userId, AppSettingsModel settings);

  /// Get security settings from remote storage
  Future<SecuritySettingsModel?> getSecuritySettings(String userId);

  /// Save security settings to remote storage
  Future<SecuritySettingsModel> saveSecuritySettings(
      String userId, SecuritySettingsModel settings);

  /// Get personalization settings from remote storage
  Future<PersonalizationSettingsModel?> getPersonalizationSettings(
      String userId);

  /// Save personalization settings to remote storage
  Future<PersonalizationSettingsModel> savePersonalizationSettings(
      String userId, PersonalizationSettingsModel settings);

  /// Delete user settings from remote storage
  Future<void> deleteUserSettings(String userId);

  /// Export user settings as JSON
  Future<String> exportSettings(String userId);

  /// Import user settings from JSON
  Future<SettingsModel> importSettings(String userId, String jsonData);

  /// Update user's expiry warning threshold in users table
  Future<void> updateExpiryWarningThreshold(String userId, int days);
}

/// Implementation of settings remote data source using Supabase
class SettingsRemoteDataSourceImpl implements SettingsRemoteDataSource {
  final SupabaseService supabaseService;

  SettingsRemoteDataSourceImpl({required this.supabaseService});

  SupabaseClient get _supabaseClient => supabaseService.client;

  @override
  Future<SettingsModel?> getUserSettings(String userId) async {
    return await supabaseService.executeWithRetry(
      () async {
        final response = await _supabaseClient
            .from('user_settings')
            .select()
            .eq('user_id', userId)
            .maybeSingle();

        if (response == null) return null;

        return SettingsModel.fromJson(response);
      },
      operationName: 'getUserSettings',
    );
  }

  @override
  Future<SettingsModel> saveUserSettings(SettingsModel settings) async {
    return await supabaseService.executeWithRetry(
      () async {
        final data = settings.toJson();
        data['updated_at'] = DateTime.now().toIso8601String();

        final response = await _supabaseClient
            .from('user_settings')
            .upsert(data)
            .select()
            .single();

        return SettingsModel.fromJson(response);
      },
      operationName: 'saveUserSettings',
    );
  }

  @override
  Future<NotificationSettingsModel?> getNotificationSettings(
      String userId) async {
    return await supabaseService.executeWithRetry(
      () async {
        final response = await _supabaseClient
            .from('user_settings')
            .select('notifications')
            .eq('user_id', userId)
            .maybeSingle();

        if (response == null || response['notifications'] == null) return null;

        return NotificationSettingsModel.fromJson(response['notifications']);
      },
      operationName: 'getNotificationSettings',
    );
  }

  @override
  Future<NotificationSettingsModel> saveNotificationSettings(
      String userId, NotificationSettingsModel settings) async {
    return await supabaseService.executeWithRetry(
      () async {
        final data = {
          'user_id': userId,
          'notifications': settings.toJson(),
          'updated_at': DateTime.now().toIso8601String(),
        };

        await _supabaseClient.from('user_settings').upsert(data);

        return settings;
      },
      operationName: 'saveNotificationSettings',
    );
  }

  @override
  Future<AppSettingsModel?> getAppSettings(String userId) async {
    return await supabaseService.executeWithRetry(
      () async {
        final response = await _supabaseClient
            .from('user_settings')
            .select('app')
            .eq('user_id', userId)
            .maybeSingle();

        if (response == null || response['app'] == null) return null;

        return AppSettingsModel.fromJson(response['app']);
      },
      operationName: 'getAppSettings',
    );
  }

  @override
  Future<AppSettingsModel> saveAppSettings(
      String userId, AppSettingsModel settings) async {
    return await supabaseService.executeWithRetry(
      () async {
        final data = {
          'user_id': userId,
          'app': settings.toJson(),
          'updated_at': DateTime.now().toIso8601String(),
        };

        await _supabaseClient.from('user_settings').upsert(data);

        return settings;
      },
      operationName: 'saveAppSettings',
    );
  }

  @override
  Future<SecuritySettingsModel?> getSecuritySettings(String userId) async {
    return await supabaseService.executeWithRetry(
      () async {
        final response = await _supabaseClient
            .from('user_settings')
            .select('security')
            .eq('user_id', userId)
            .maybeSingle();

        if (response == null || response['security'] == null) return null;

        return SecuritySettingsModel.fromJson(response['security']);
      },
      operationName: 'getSecuritySettings',
    );
  }

  @override
  Future<SecuritySettingsModel> saveSecuritySettings(
      String userId, SecuritySettingsModel settings) async {
    return await supabaseService.executeWithRetry(
      () async {
        final data = {
          'user_id': userId,
          'security': settings.toJson(),
          'updated_at': DateTime.now().toIso8601String(),
        };

        await _supabaseClient.from('user_settings').upsert(data);

        return settings;
      },
      operationName: 'saveSecuritySettings',
    );
  }

  @override
  Future<PersonalizationSettingsModel?> getPersonalizationSettings(
      String userId) async {
    return await supabaseService.executeWithRetry(
      () async {
        final response = await _supabaseClient
            .from('user_settings')
            .select('personalization')
            .eq('user_id', userId)
            .maybeSingle();

        if (response == null || response['personalization'] == null) {
          return null;
        }

        return PersonalizationSettingsModel.fromJson(
            response['personalization']);
      },
      operationName: 'getPersonalizationSettings',
    );
  }

  @override
  Future<PersonalizationSettingsModel> savePersonalizationSettings(
      String userId, PersonalizationSettingsModel settings) async {
    return await supabaseService.executeWithRetry(
      () async {
        final data = {
          'user_id': userId,
          'personalization': settings.toJson(),
          'updated_at': DateTime.now().toIso8601String(),
        };

        await _supabaseClient.from('user_settings').upsert(data);

        return settings;
      },
      operationName: 'savePersonalizationSettings',
    );
  }

  @override
  Future<void> deleteUserSettings(String userId) async {
    return await supabaseService.executeWithRetry(
      () async {
        await _supabaseClient
            .from('user_settings')
            .delete()
            .eq('user_id', userId);
      },
      operationName: 'deleteUserSettings',
    );
  }

  @override
  Future<String> exportSettings(String userId) async {
    return await supabaseService.executeWithRetry(
      () async {
        final response = await _supabaseClient
            .from('user_settings')
            .select()
            .eq('user_id', userId)
            .single();

        return jsonEncode(response);
      },
      operationName: 'exportSettings',
    );
  }

  @override
  Future<SettingsModel> importSettings(String userId, String jsonData) async {
    return await supabaseService.executeWithRetry(
      () async {
        final data = jsonDecode(jsonData);
        data['user_id'] = userId;
        data['updated_at'] = DateTime.now().toIso8601String();

        final response = await _supabaseClient
            .from('user_settings')
            .upsert(data)
            .select()
            .single();

        return SettingsModel.fromJson(response);
      },
      operationName: 'importSettings',
    );
  }

  @override
  Future<void> updateExpiryWarningThreshold(String userId, int days) async {
    return await supabaseService.executeWithRetry(
      () async {
        await _supabaseClient
            .from('users')
            .update({'expiry_warning_days': days}).eq('id', userId);
      },
      operationName: 'updateExpiryWarningThreshold',
    );
  }
}
