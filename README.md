# MedyTrack Mobile 📱💊

> Your smart daily companion for managing household medications, tracking expiry dates, and supporting your family's health and wellbeing — all in one place.

[![Flutter](https://img.shields.io/badge/Flutter-3.24.0-02569B?logo=flutter)](https://flutter.dev)
[![Dart](https://img.shields.io/badge/Dart-3.5.0-0175C2?logo=dart)](https://dart.dev)
[![Supabase](https://img.shields.io/badge/Supabase-Backend-3ECF8E?logo=supabase)](https://supabase.com)
[![License](https://img.shields.io/badge/License-MIT-blue.svg)](LICENSE)

## 🌟 Overview

MedyTrack Mobile is a comprehensive Flutter application designed to simplify household medication management. Built with modern architecture patterns and a beautiful, intuitive interface, it helps families track medicines, monitor expiry dates, manage stock levels, and maintain their health routines effortlessly.

### ✨ Key Features

- **📊 Smart Dashboard**: Real-time overview with statistics cards showing total medicines, expired items, low stock alerts, and expiring soon notifications
- **🔍 Intelligent Search**: Real-time medicine search with dynamic filtering and household-scoped results
- **💊 Medicine Management**: Complete CRUD operations for adding, editing, viewing, and organizing medicines
- **📅 Expiry Tracking**: Automated monitoring of expiration dates with smart notifications
- **📍 Location Management**: Organize medicines by storage locations (kitchen, bathroom, bedroom, etc.)
- **👥 Multi-User Support**: Household-based medicine sharing and management
- **🎨 Modern UI/UX**: Layered design with gradient headers, floating elements, and smooth animations
- **📱 Cross-Platform**: Native performance on both iOS and Android devices

## 🏗️ Architecture

MedyTrack follows clean architecture principles with the BLoC pattern for state management:

```
lib/
├── core/                   # Core utilities and configurations
│   ├── config/            # App configuration and constants
│   ├── di/                # Dependency injection setup
│   ├── router/            # Navigation and routing
│   └── theme/             # UI theme and styling
├── data/                  # Data layer
│   ├── datasources/       # Remote and local data sources
│   ├── models/            # Data models and DTOs
│   └── repositories/      # Repository implementations
├── domain/                # Business logic layer
│   ├── entities/          # Domain entities
│   ├── repositories/      # Repository interfaces
│   └── usecases/          # Business use cases
└── presentation/          # UI layer
    ├── bloc/              # BLoC state management
    ├── pages/             # Screen widgets
    └── widgets/           # Reusable UI components
```

## 🚀 Technology Stack

- **Frontend**: Flutter 3.24.0 with Dart 3.5.0
- **State Management**: BLoC Pattern with flutter_bloc
- **Backend**: Supabase (PostgreSQL database, real-time subscriptions, authentication)
- **Navigation**: GoRouter for declarative routing
- **Dependency Injection**: GetIt with Injectable
- **Local Storage**: Shared Preferences
- **UI Components**: Material Design 3 with custom theming
- **Architecture**: Clean Architecture with Repository Pattern
- **Environment Management**: flutter_dotenv for Dev/Prod configuration

## 🌍 Environment Configuration

MedyTrack uses environment-aware configuration to separate development and production environments:

### Environment Files
- **`.env.dev`**: Development environment (MedyTrack Dev Supabase)
- **`.env.prod`**: Production environment (MedyTrack Prod Supabase)

### Automatic Environment Detection
- **Debug Mode**: Automatically loads `.env.dev` configuration
- **Release Mode**: Automatically loads `.env.prod` configuration

### Environment Variables
```bash
# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key

# App Configuration
APP_NAME=MedyTrack Mobile (Dev/Prod)
APP_VERSION=0.5.0
ENVIRONMENT=development/production

# Feature Flags
ENABLE_DEBUG_PAGE=true/false
ENABLE_VERBOSE_LOGGING=true/false
ENABLE_ANALYTICS=true/false
```

### Environment Setup
1. Environment files are included as templates
2. Configuration is loaded automatically based on build mode
3. Fallback values ensure app stability if environment files are missing
4. All sensitive data is managed through environment variables

## 📱 Screenshots

### Dashboard & Navigation
![Dashboard](docs/screenshots/dashboard.png)
*Modern layered dashboard with gradient header and floating navigation*

### Medicine Management
![Medicine List](docs/screenshots/medicine-list.png)
*Comprehensive medicine list with search and filtering*

### Statistics & Insights
![Statistics](docs/screenshots/statistics.png)
*Real-time statistics cards with color-coded alerts*

## 🛠️ Installation & Setup

### Prerequisites

- Flutter SDK 3.24.0 or higher
- Dart SDK 3.5.0 or higher
- Android Studio / VS Code with Flutter extensions
- Git for version control

### Development Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/BeeGaat/MedyTrack-Mobile.git
   cd MedyTrack-Mobile
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Configure Supabase**
   - Create a new project at [supabase.com](https://supabase.com)
   - Copy your project URL and anon key
   - Update `lib/core/config/app_config.dart` with your credentials:
   ```dart
   class AppConfig {
     static const String supabaseUrl = 'YOUR_SUPABASE_URL';
     static const String supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';
   }
   ```

4. **Set up database schema**
   - Run the SQL scripts in `database/` directory in your Supabase SQL editor
   - Enable Row Level Security (RLS) policies

5. **Run the application**
   ```bash
   flutter run
   ```

### Building for Production

```bash
# Android
flutter build apk --release
flutter build appbundle --release

# iOS
flutter build ios --release
```

## 🗃️ Database Schema

The application uses Supabase PostgreSQL with the following main tables:

- `users` - User authentication and profiles
- `households` - Family/household groupings
- `medicines` - Medicine information and details
- `medicine_locations` - Storage location management
- `medicine_tags` - Categorization and labeling
- `notifications` - Alert and reminder system

Detailed schema documentation is available in [docs/database.md](docs/database.md).

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details on:

- Code style and standards
- Development workflow
- Pull request process
- Issue reporting guidelines

### Development Guidelines

1. Follow Flutter/Dart style guidelines
2. Write comprehensive tests for new features
3. Update documentation for API changes
4. Use conventional commit messages
5. Ensure all CI checks pass

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support & Contact

- **Issues**: [GitHub Issues](https://github.com/BeeGaat/MedyTrack-Mobile/issues)
- **Discussions**: [GitHub Discussions](https://github.com/BeeGaat/MedyTrack-Mobile/discussions)
- **Email**: <EMAIL>

## 🗺️ Roadmap

- [ ] Push notifications for medicine reminders
- [ ] Barcode scanning for easy medicine entry
- [ ] Integration with pharmacy APIs
- [ ] Medicine interaction warnings
- [ ] Export/import functionality
- [ ] Dark mode support
- [ ] Offline mode capabilities

## 🙏 Acknowledgments

- Flutter team for the amazing framework
- Supabase for the backend infrastructure
- Material Design team for UI guidelines
- Open source community for inspiration and tools

---

**Made with ❤️ for families who care about health and wellbeing**
