name: 📊 Production Monitoring

on:
  schedule:
    # Run every 15 minutes during business hours (9 AM - 6 PM UTC)
    - cron: '*/15 9-18 * * 1-5'
    # Run every hour during off-hours
    - cron: '0 * * * *'
  workflow_dispatch:  # Allow manual monitoring
    inputs:
      monitoring_type:
        description: 'Type of monitoring to perform'
        required: true
        default: 'health-check'
        type: choice
        options:
          - health-check
          - full-monitoring
          - rollback-check
          - generate-report
      alert_level:
        description: 'Alert sensitivity level'
        required: false
        default: 'normal'
        type: choice
        options:
          - low
          - normal
          - high

# Prevent concurrent monitoring runs
concurrency:
  group: production-monitoring
  cancel-in-progress: false

env:
  PRODUCTION_URL: https://medytrack.app
  MONITORING_TIMEOUT: 300  # 5 minutes

jobs:
  # ============================================================================
  # HEALTH CHECK MONITORING
  # ============================================================================
  health-check:
    name: 🏥 Health Check
    runs-on: ubuntu-latest
    outputs:
      health_status: ${{ steps.health.outputs.status }}
      response_time: ${{ steps.health.outputs.response_time }}
      needs_rollback: ${{ steps.health.outputs.needs_rollback }}
    steps:
      - name: 📥 Checkout monitoring scripts
        uses: actions/checkout@v4

      - name: 🔧 Setup monitoring environment
        run: |
          chmod +x scripts/monitor_production.sh
          mkdir -p logs

      - name: 🏥 Perform health check
        id: health
        run: |
          echo "🏥 Checking application health..."
          
          # Test main application endpoint
          START_TIME=$(date +%s%3N)
          HTTP_CODE=$(curl -s -w "%{http_code}" -o /dev/null --max-time 30 "$PRODUCTION_URL" || echo "000")
          END_TIME=$(date +%s%3N)
          RESPONSE_TIME=$((END_TIME - START_TIME))
          
          echo "HTTP Code: $HTTP_CODE"
          echo "Response Time: ${RESPONSE_TIME}ms"
          
          # Set outputs
          echo "response_time=$RESPONSE_TIME" >> $GITHUB_OUTPUT
          
          if [[ "$HTTP_CODE" == "200" ]]; then
            echo "status=healthy" >> $GITHUB_OUTPUT
            echo "✅ Health check passed (${RESPONSE_TIME}ms)"
            
            # Check if response time is concerning
            if [[ $RESPONSE_TIME -gt 5000 ]]; then
              echo "needs_rollback=true" >> $GITHUB_OUTPUT
              echo "🚨 CRITICAL: Response time ${RESPONSE_TIME}ms exceeds 5 second threshold"
            elif [[ $RESPONSE_TIME -gt 3000 ]]; then
              echo "needs_rollback=false" >> $GITHUB_OUTPUT
              echo "⚠️ WARNING: Response time ${RESPONSE_TIME}ms exceeds 3 second threshold"
            else
              echo "needs_rollback=false" >> $GITHUB_OUTPUT
            fi
          else
            echo "status=unhealthy" >> $GITHUB_OUTPUT
            echo "needs_rollback=true" >> $GITHUB_OUTPUT
            echo "❌ Health check failed with HTTP code: $HTTP_CODE"
          fi

      - name: 🔍 Test critical endpoints
        run: |
          echo "🔍 Testing critical application endpoints..."
          
          # Test API endpoints (if available)
          ENDPOINTS=(
            "$PRODUCTION_URL/api/health"
            "$PRODUCTION_URL/api/medicines"
            "$PRODUCTION_URL/api/reminders"
          )
          
          for endpoint in "${ENDPOINTS[@]}"; do
            echo "Testing: $endpoint"
            HTTP_CODE=$(curl -s -w "%{http_code}" -o /dev/null --max-time 10 "$endpoint" || echo "000")
            
            if [[ "$HTTP_CODE" =~ ^[23] ]]; then
              echo "✅ $endpoint: $HTTP_CODE"
            else
              echo "⚠️ $endpoint: $HTTP_CODE"
            fi
          done

      - name: 📊 Generate health report
        run: |
          cat > health_report.md << EOF
          # MedyTrack Health Check Report
          **Generated:** $(date)
          **Status:** ${{ steps.health.outputs.status }}
          **Response Time:** ${{ steps.health.outputs.response_time }}ms
          **Needs Rollback:** ${{ steps.health.outputs.needs_rollback }}
          
          ## Thresholds
          - ✅ Good: < 2000ms
          - ⚠️ Warning: 2000-3000ms  
          - 🔶 High: 3000-5000ms
          - 🚨 Critical: > 5000ms
          
          ## Next Steps
          $(if [[ "${{ steps.health.outputs.needs_rollback }}" == "true" ]]; then
            echo "🚨 **IMMEDIATE ACTION REQUIRED** - Consider emergency rollback"
          else
            echo "✅ Continue monitoring - no immediate action required"
          fi)
          EOF
          
          cat health_report.md

      - name: 📤 Upload health report
        uses: actions/upload-artifact@v4
        with:
          name: health-report-${{ github.run_number }}
          path: health_report.md
          retention-days: 7

  # ============================================================================
  # ERROR MONITORING
  # ============================================================================
  error-monitoring:
    name: 🚨 Error Monitoring
    runs-on: ubuntu-latest
    needs: health-check
    if: ${{ !cancelled() }}
    outputs:
      error_rate: ${{ steps.errors.outputs.error_rate }}
      critical_errors: ${{ steps.errors.outputs.critical_errors }}
    steps:
      - name: 📊 Monitor error rates
        id: errors
        run: |
          echo "📊 Monitoring application error rates..."
          
          # Placeholder for actual error monitoring
          # In real implementation, this would integrate with:
          # - Sentry API
          # - Application logs
          # - Custom error tracking
          
          # Mock error rate for demonstration
          ERROR_RATE=0.5
          CRITICAL_ERRORS=0
          
          echo "error_rate=$ERROR_RATE" >> $GITHUB_OUTPUT
          echo "critical_errors=$CRITICAL_ERRORS" >> $GITHUB_OUTPUT
          
          echo "Current error rate: ${ERROR_RATE}%"
          echo "Critical errors: $CRITICAL_ERRORS"
          
          # Check thresholds
          if (( $(echo "$ERROR_RATE > 5.0" | bc -l) )); then
            echo "🚨 CRITICAL: Error rate ${ERROR_RATE}% exceeds 5% threshold"
            exit 1
          elif (( $(echo "$ERROR_RATE > 2.0" | bc -l) )); then
            echo "⚠️ WARNING: Error rate ${ERROR_RATE}% exceeds 2% threshold"
          else
            echo "✅ Error rate within acceptable limits"
          fi

      - name: 🔍 Analyze error patterns
        run: |
          echo "🔍 Analyzing error patterns..."
          
          # This would analyze:
          # - Error frequency trends
          # - Error type distribution
          # - User impact assessment
          # - Geographic error patterns
          
          echo "✅ Error pattern analysis completed"

  # ============================================================================
  # PERFORMANCE MONITORING
  # ============================================================================
  performance-monitoring:
    name: ⚡ Performance Monitoring
    runs-on: ubuntu-latest
    needs: health-check
    if: ${{ !cancelled() }}
    steps:
      - name: ⚡ Monitor performance metrics
        run: |
          echo "⚡ Monitoring application performance..."
          
          # Test multiple endpoints for performance
          ENDPOINTS=(
            "$PRODUCTION_URL"
            "$PRODUCTION_URL/medicines"
            "$PRODUCTION_URL/dashboard"
          )
          
          TOTAL_TIME=0
          ENDPOINT_COUNT=0
          
          for endpoint in "${ENDPOINTS[@]}"; do
            echo "Testing performance: $endpoint"
            
            START_TIME=$(date +%s%3N)
            HTTP_CODE=$(curl -s -w "%{http_code}" -o /dev/null --max-time 30 "$endpoint" || echo "000")
            END_TIME=$(date +%s%3N)
            RESPONSE_TIME=$((END_TIME - START_TIME))
            
            if [[ "$HTTP_CODE" =~ ^[23] ]]; then
              TOTAL_TIME=$((TOTAL_TIME + RESPONSE_TIME))
              ENDPOINT_COUNT=$((ENDPOINT_COUNT + 1))
              echo "✅ $endpoint: ${RESPONSE_TIME}ms"
            else
              echo "❌ $endpoint: Failed ($HTTP_CODE)"
            fi
          done
          
          if [[ $ENDPOINT_COUNT -gt 0 ]]; then
            AVERAGE_TIME=$((TOTAL_TIME / ENDPOINT_COUNT))
            echo "📊 Average response time: ${AVERAGE_TIME}ms"
            
            if [[ $AVERAGE_TIME -gt 3000 ]]; then
              echo "⚠️ WARNING: Average response time exceeds 3 seconds"
            else
              echo "✅ Performance within acceptable limits"
            fi
          fi

      - name: 💾 Monitor resource usage
        run: |
          echo "💾 Monitoring resource usage patterns..."
          
          # This would monitor:
          # - Memory usage trends
          # - CPU utilization
          # - Database connection pools
          # - Cache hit rates
          
          echo "✅ Resource monitoring completed"

  # ============================================================================
  # ROLLBACK DECISION
  # ============================================================================
  rollback-decision:
    name: 🔄 Rollback Decision
    runs-on: ubuntu-latest
    needs: [health-check, error-monitoring, performance-monitoring]
    if: ${{ !cancelled() }}
    steps:
      - name: 🔄 Evaluate rollback criteria
        run: |
          echo "🔄 Evaluating rollback criteria..."
          
          HEALTH_STATUS="${{ needs.health-check.outputs.health_status }}"
          NEEDS_ROLLBACK="${{ needs.health-check.outputs.needs_rollback }}"
          RESPONSE_TIME="${{ needs.health-check.outputs.response_time }}"
          ERROR_RATE="${{ needs.error-monitoring.outputs.error_rate }}"
          
          echo "Health Status: $HEALTH_STATUS"
          echo "Response Time: ${RESPONSE_TIME}ms"
          echo "Error Rate: ${ERROR_RATE}%"
          echo "Needs Rollback: $NEEDS_ROLLBACK"
          
          # Decision matrix
          if [[ "$NEEDS_ROLLBACK" == "true" ]] || [[ "$HEALTH_STATUS" == "unhealthy" ]]; then
            echo "🚨 ROLLBACK RECOMMENDED"
            echo "Criteria met for emergency rollback"
            
            # In a real implementation, this would:
            # 1. Create an incident
            # 2. Notify on-call team
            # 3. Prepare rollback procedures
            # 4. Wait for manual approval or auto-execute based on severity
            
          else
            echo "✅ CONTINUE MONITORING"
            echo "No rollback criteria met - continue normal operations"
          fi

      - name: 📢 Send monitoring summary
        run: |
          echo "📢 Sending monitoring summary..."
          
          # Create monitoring summary
          cat > monitoring_summary.json << EOF
          {
            "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "health_status": "${{ needs.health-check.outputs.health_status }}",
            "response_time_ms": ${{ needs.health-check.outputs.response_time }},
            "error_rate_percent": ${{ needs.error-monitoring.outputs.error_rate }},
            "needs_rollback": ${{ needs.health-check.outputs.needs_rollback }},
            "monitoring_run": "${{ github.run_number }}"
          }
          EOF
          
          echo "Monitoring summary:"
          cat monitoring_summary.json

  # ============================================================================
  # ALERT NOTIFICATIONS
  # ============================================================================
  send-alerts:
    name: 📢 Send Alerts
    runs-on: ubuntu-latest
    needs: [health-check, error-monitoring, rollback-decision]
    if: ${{ needs.health-check.outputs.needs_rollback == 'true' || failure() }}
    steps:
      - name: 📢 Send Slack notification
        if: ${{ needs.health-check.outputs.needs_rollback == 'true' }}
        run: |
          if [[ -n "${{ secrets.SLACK_WEBHOOK_URL }}" ]]; then
            curl -X POST -H 'Content-type: application/json' \
              --data '{
                "text": "🚨 MedyTrack Production Alert",
                "attachments": [
                  {
                    "color": "danger",
                    "fields": [
                      {
                        "title": "Health Status",
                        "value": "${{ needs.health-check.outputs.health_status }}",
                        "short": true
                      },
                      {
                        "title": "Response Time",
                        "value": "${{ needs.health-check.outputs.response_time }}ms",
                        "short": true
                      },
                      {
                        "title": "Error Rate",
                        "value": "${{ needs.error-monitoring.outputs.error_rate }}%",
                        "short": true
                      },
                      {
                        "title": "Action Required",
                        "value": "Consider emergency rollback",
                        "short": false
                      }
                    ]
                  }
                ]
              }' \
              "${{ secrets.SLACK_WEBHOOK_URL }}"
          else
            echo "⚠️ Slack webhook not configured"
          fi

      - name: 📧 Send email alert
        if: ${{ failure() }}
        run: |
          echo "📧 Critical monitoring failure detected"
          echo "This would send email alerts to the on-call team"
          
          # In real implementation, this would send actual emails
          # using a service like SendGrid, AWS SES, or similar

  # ============================================================================
  # CLEANUP AND REPORTING
  # ============================================================================
  cleanup:
    name: 🧹 Cleanup & Report
    runs-on: ubuntu-latest
    needs: [health-check, error-monitoring, performance-monitoring, rollback-decision]
    if: ${{ always() }}
    steps:
      - name: 📊 Generate final report
        run: |
          echo "📊 Generating final monitoring report..."
          
          cat > final_report.md << EOF
          # MedyTrack Production Monitoring Report
          
          **Run ID:** ${{ github.run_number }}
          **Timestamp:** $(date)
          **Trigger:** ${{ github.event_name }}
          
          ## Results Summary
          - **Health Status:** ${{ needs.health-check.outputs.health_status || 'unknown' }}
          - **Response Time:** ${{ needs.health-check.outputs.response_time || 'unknown' }}ms
          - **Error Rate:** ${{ needs.error-monitoring.outputs.error_rate || 'unknown' }}%
          - **Rollback Needed:** ${{ needs.health-check.outputs.needs_rollback || 'unknown' }}
          
          ## Status
          $(if [[ "${{ needs.health-check.outputs.needs_rollback }}" == "true" ]]; then
            echo "🚨 **ALERT CONDITION** - Immediate attention required"
          else
            echo "✅ **NORMAL OPERATION** - Continue monitoring"
          fi)
          
          ## Next Monitoring
          Next scheduled check: $(date -d '+15 minutes' '+%Y-%m-%d %H:%M UTC')
          EOF
          
          cat final_report.md

      - name: 📤 Archive monitoring data
        uses: actions/upload-artifact@v4
        with:
          name: monitoring-report-${{ github.run_number }}
          path: final_report.md
          retention-days: 30
