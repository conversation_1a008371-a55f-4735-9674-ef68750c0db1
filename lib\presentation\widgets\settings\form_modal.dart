import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';

/// Form field configuration
class FormFieldConfig {
  final String key;
  final String label;
  final String? initialValue;
  final TextInputType keyboardType;
  final bool obscureText;
  final int maxLines;
  final String? Function(String?)? validator;
  final List<String>? options; // For dropdown fields
  final Widget? suffix;

  const FormFieldConfig({
    required this.key,
    required this.label,
    this.initialValue,
    this.keyboardType = TextInputType.text,
    this.obscureText = false,
    this.maxLines = 1,
    this.validator,
    this.options,
    this.suffix,
  });
}

/// Reusable form modal for settings
class FormModal extends StatefulWidget {
  final String title;
  final String? subtitle;
  final List<FormFieldConfig> fields;
  final String confirmButtonText;
  final String cancelButtonText;
  final Function(Map<String, String>) onConfirm;
  final VoidCallback? onCancel;
  final bool isLoading;

  const FormModal({
    super.key,
    required this.title,
    this.subtitle,
    required this.fields,
    this.confirmButtonText = 'Confirmer',
    this.cancelButtonText = 'Annuler',
    required this.onConfirm,
    this.onCancel,
    this.isLoading = false,
  });

  @override
  State<FormModal> createState() => _FormModalState();
}

class _FormModalState extends State<FormModal> {
  final _formKey = GlobalKey<FormState>();
  late Map<String, TextEditingController> _controllers;
  late Map<String, String> _dropdownValues;

  @override
  void initState() {
    super.initState();
    _controllers = {};
    _dropdownValues = {};

    for (final field in widget.fields) {
      if (field.options == null) {
        _controllers[field.key] = TextEditingController(text: field.initialValue);
      } else {
        _dropdownValues[field.key] = field.initialValue ?? field.options!.first;
      }
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.title,
            style: AppTextStyles.titleLarge.copyWith(
              color: AppColors.navy,
              fontWeight: FontWeight.w600,
            ),
          ),
          if (widget.subtitle != null) ...[
            const SizedBox(height: 4),
            Text(
              widget.subtitle!,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.grey600,
              ),
            ),
          ],
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: widget.fields.map((field) => _buildFormField(field)).toList(),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: widget.isLoading ? null : () {
            widget.onCancel?.call();
            Navigator.of(context).pop();
          },
          child: Text(widget.cancelButtonText),
        ),
        ElevatedButton(
          onPressed: widget.isLoading ? null : _handleConfirm,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.teal,
            foregroundColor: Colors.white,
          ),
          child: widget.isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Colors.white,
                  ),
                )
              : Text(widget.confirmButtonText),
        ),
      ],
    );
  }

  Widget _buildFormField(FormFieldConfig field) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: field.options != null
          ? _buildDropdownField(field)
          : _buildTextFormField(field),
    );
  }

  Widget _buildTextFormField(FormFieldConfig field) {
    return TextFormField(
      controller: _controllers[field.key],
      decoration: InputDecoration(
        labelText: field.label,
        border: const OutlineInputBorder(),
        suffix: field.suffix,
        filled: true,
        fillColor: AppColors.grey50,
      ),
      keyboardType: field.keyboardType,
      obscureText: field.obscureText,
      maxLines: field.maxLines,
      validator: field.validator,
    );
  }

  Widget _buildDropdownField(FormFieldConfig field) {
    return DropdownButtonFormField<String>(
      initialValue: _dropdownValues[field.key],
      decoration: InputDecoration(
        labelText: field.label,
        border: const OutlineInputBorder(),
        filled: true,
        fillColor: AppColors.grey50,
      ),
      items: field.options!.map((option) {
        return DropdownMenuItem(
          value: option,
          child: Text(option),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _dropdownValues[field.key] = value!;
        });
      },
      validator: field.validator,
    );
  }

  void _handleConfirm() {
    if (_formKey.currentState!.validate()) {
      final values = <String, String>{};
      
      for (final field in widget.fields) {
        if (field.options == null) {
          values[field.key] = _controllers[field.key]!.text;
        } else {
          values[field.key] = _dropdownValues[field.key]!;
        }
      }

      widget.onConfirm(values);
    }
  }
}

/// Confirmation dialog for destructive actions
class ConfirmationModal extends StatelessWidget {
  final String title;
  final String content;
  final String confirmButtonText;
  final String cancelButtonText;
  final VoidCallback onConfirm;
  final VoidCallback? onCancel;
  final bool isDestructive;
  final bool isLoading;

  const ConfirmationModal({
    super.key,
    required this.title,
    required this.content,
    this.confirmButtonText = 'Confirmer',
    this.cancelButtonText = 'Annuler',
    required this.onConfirm,
    this.onCancel,
    this.isDestructive = false,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        title,
        style: AppTextStyles.titleLarge.copyWith(
          color: isDestructive ? AppColors.error : AppColors.navy,
          fontWeight: FontWeight.w600,
        ),
      ),
      content: Text(
        content,
        style: AppTextStyles.bodyMedium.copyWith(
          color: AppColors.grey700,
        ),
      ),
      actions: [
        TextButton(
          onPressed: isLoading ? null : () {
            onCancel?.call();
            Navigator.of(context).pop();
          },
          child: Text(cancelButtonText),
        ),
        ElevatedButton(
          onPressed: isLoading ? null : () {
            onConfirm();
            Navigator.of(context).pop();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: isDestructive ? AppColors.error : AppColors.teal,
            foregroundColor: Colors.white,
          ),
          child: isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Colors.white,
                  ),
                )
              : Text(confirmButtonText),
        ),
      ],
    );
  }
}

/// Selection modal for choosing from a list of options
class SelectionModal<T> extends StatefulWidget {
  final String title;
  final String? subtitle;
  final List<T> options;
  final T? selectedValue;
  final String Function(T) getDisplayText;
  final String Function(T) getSubtitle;
  final Function(T) onSelected;
  final bool allowNone;
  final String noneText;

  const SelectionModal({
    super.key,
    required this.title,
    this.subtitle,
    required this.options,
    this.selectedValue,
    required this.getDisplayText,
    this.getSubtitle = _defaultGetSubtitle,
    required this.onSelected,
    this.allowNone = false,
    this.noneText = 'Aucun',
  });

  static String _defaultGetSubtitle(dynamic item) => '';

  @override
  State<SelectionModal<T>> createState() => _SelectionModalState<T>();
}

class _SelectionModalState<T> extends State<SelectionModal<T>> {
  T? _selectedValue;

  @override
  void initState() {
    super.initState();
    _selectedValue = widget.selectedValue;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.title,
            style: AppTextStyles.titleLarge.copyWith(
              color: AppColors.navy,
              fontWeight: FontWeight.w600,
            ),
          ),
          if (widget.subtitle != null) ...[
            const SizedBox(height: 4),
            Text(
              widget.subtitle!,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.grey600,
              ),
            ),
          ],
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (widget.allowNone)
              _buildOptionTile(
                title: widget.noneText,
                subtitle: '',
                value: null,
                isSelected: _selectedValue == null,
              ),
            ...widget.options.map((option) => _buildOptionTile(
                  title: widget.getDisplayText(option),
                  subtitle: widget.getSubtitle(option),
                  value: option,
                  isSelected: _selectedValue == option,
                )),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Fermer'),
        ),
      ],
    );
  }

  Widget _buildOptionTile({
    required String title,
    required String subtitle,
    required T? value,
    required bool isSelected,
  }) {
    return ListTile(
      title: Text(title),
      subtitle: subtitle.isNotEmpty ? Text(subtitle) : null,
      leading: Radio<T?>(
        value: value,
        groupValue: _selectedValue,
        onChanged: (newValue) {
          setState(() {
            _selectedValue = newValue;
          });
          widget.onSelected(newValue as T);
          Navigator.of(context).pop();
        },
        activeColor: AppColors.teal,
      ),
      onTap: () {
        setState(() {
          _selectedValue = value;
        });
        widget.onSelected(value as T);
        Navigator.of(context).pop();
      },
    );
  }
}

/// Bottom sheet modal for actions
class ActionBottomSheet extends StatelessWidget {
  final String? title;
  final List<ActionItem> actions;

  const ActionBottomSheet({
    super.key,
    this.title,
    required this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (title != null) ...[
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                title!,
                style: AppTextStyles.titleMedium.copyWith(
                  color: AppColors.navy,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const Divider(height: 1),
          ],
          ...actions.map((action) => ListTile(
                leading: Icon(
                  action.icon,
                  color: action.isDestructive ? AppColors.error : AppColors.grey700,
                ),
                title: Text(
                  action.title,
                  style: TextStyle(
                    color: action.isDestructive ? AppColors.error : AppColors.grey900,
                  ),
                ),
                subtitle: action.subtitle != null ? Text(action.subtitle!) : null,
                onTap: () {
                  Navigator.of(context).pop();
                  action.onTap();
                },
              )),
        ],
      ),
    );
  }
}

/// Action item for bottom sheet
class ActionItem {
  final IconData icon;
  final String title;
  final String? subtitle;
  final VoidCallback onTap;
  final bool isDestructive;

  const ActionItem({
    required this.icon,
    required this.title,
    this.subtitle,
    required this.onTap,
    this.isDestructive = false,
  });
}
