import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../domain/entities/medicine.dart';
import '../../bloc/medicine/medicine_bloc.dart';
import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/auth/auth_state.dart' as auth_state;

import '../../widgets/medicine/enhanced_medicine_list_card.dart';

class MedicineCardDebugPage extends StatefulWidget {
  const MedicineCardDebugPage({super.key});

  @override
  State<MedicineCardDebugPage> createState() => _MedicineCardDebugPageState();
}

class _MedicineCardDebugPageState extends State<MedicineCardDebugPage> {
  Medicine? selectedMedicine;
  List<Medicine> medicines = [];

  @override
  void initState() {
    super.initState();
    _loadMedicines();
  }

  void _loadMedicines() {
    // Get the current user's household ID from auth state
    final authState = context.read<AuthBloc>().state;
    if (authState is auth_state.AuthAuthenticated &&
        authState.householdId != null) {
      context
          .read<MedicineBloc>()
          .add(MedicineLoadRequested(householdId: authState.householdId!));
    } else {
      // If no household ID available, show error
      setState(() {
        medicines = [];
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.teal.withValues(alpha: 0.1),
              AppColors.navy.withValues(alpha: 0.05),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.arrow_back, color: AppColors.navy),
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Medicine Card Debug',
                            style: AppTextStyles.titleLarge.copyWith(
                              color: AppColors.navy,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            'Debug status-based theming',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.grey600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(top: 24),
                  decoration: const BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(24),
                      topRight: Radius.circular(24),
                    ),
                  ),
                  child: BlocListener<MedicineBloc, MedicineState>(
                    listener: (context, state) {
                      if (state is MedicineLoaded) {
                        setState(() {
                          medicines = state.medicines;
                          if (medicines.isNotEmpty &&
                              selectedMedicine == null) {
                            selectedMedicine = medicines.first;
                          }
                        });
                      }
                    },
                    child: BlocBuilder<MedicineBloc, MedicineState>(
                      builder: (context, state) {
                        if (state is MedicineLoading) {
                          return const Center(
                            child: CircularProgressIndicator(),
                          );
                        }

                        if (state is MedicineError) {
                          return Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.error_outline,
                                  size: 64,
                                  color: AppColors.error,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'Error loading medicines',
                                  style: AppTextStyles.titleMedium,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  state.message,
                                  style: AppTextStyles.bodyMedium.copyWith(
                                    color: AppColors.grey600,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 16),
                                ElevatedButton(
                                  onPressed: _loadMedicines,
                                  child: const Text('Retry'),
                                ),
                              ],
                            ),
                          );
                        }

                        if (medicines.isEmpty) {
                          return const Center(
                            child: Text('No medicines found'),
                          );
                        }

                        return _buildDebugContent();
                      },
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDebugContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMedicineSelector(),
          const SizedBox(height: 24),
          if (selectedMedicine != null) ...[
            _buildMedicineDetails(),
            const SizedBox(height: 24),
            _buildStatusColorAnalysis(),
            const SizedBox(height: 24),
            _buildCardComparison(),
          ],
        ],
      ),
    );
  }

  Widget _buildMedicineSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Select Medicine',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            DropdownButtonFormField<Medicine>(
              initialValue: selectedMedicine,
              decoration: const InputDecoration(
                labelText: 'Medicine',
                border: OutlineInputBorder(),
              ),
              items: medicines.map((medicine) {
                return DropdownMenuItem(
                  value: medicine,
                  child: Text(
                    medicine.name,
                    overflow: TextOverflow.ellipsis,
                  ),
                );
              }).toList(),
              onChanged: (medicine) {
                setState(() {
                  selectedMedicine = medicine;
                });
              },
            ),
            const SizedBox(height: 8),
            Text(
              'Total medicines: ${medicines.length}',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.grey600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMedicineDetails() {
    final medicine = selectedMedicine!;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Medicine Details',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            _buildDetailRow('ID', medicine.id),
            _buildDetailRow('Name', medicine.name ?? 'N/A'),
            _buildDetailRow('Display Name', medicine.name),
            _buildDetailRow('Dosage', medicine.dosage ?? 'N/A'),
            _buildDetailRow('Form', medicine.form ?? 'N/A'),
            _buildDetailRow('Quantity', medicine.quantity.toString()),
            _buildDetailRow(
                'Low Stock Threshold', medicine.lowStockThreshold.toString()),
            _buildDetailRow(
                'Expiration',
                medicine.expiration != null
                    ? DateFormat('dd/MM/yyyy').format(medicine.expiration!)
                    : 'N/A'),
            _buildDetailRow('Location', medicine.locationName ?? 'N/A'),
            _buildDetailRow(
                'Family Member', medicine.familyMemberName ?? 'N/A'),
            _buildDetailRow('Status', medicine.status.name),
            _buildDetailRow('Is Custom', medicine.isCustom.toString()),
            _buildDetailRow('Created At',
                DateFormat('dd/MM/yyyy HH:mm').format(medicine.createdAt)),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusColorAnalysis() {
    final medicine = selectedMedicine!;
    final statusColor = _getStatusColor(medicine);
    final backgroundColor = statusColor.withValues(alpha: 0.05);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Status Color Analysis',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            _buildColorRow(
                'Medicine Status', medicine.status.name, statusColor),
            _buildColorRow(
                'Background Color (5% opacity)',
                'Alpha: ${backgroundColor.alpha.toStringAsFixed(3)}',
                backgroundColor),
            const SizedBox(height: 16),
            Text(
              'Status Calculation Logic:',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            _buildStatusLogic(medicine),
            const SizedBox(height: 16),
            Text(
              'Background Color Preview:',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 60,
              width: double.infinity,
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.border),
              ),
              child: Center(
                child: Text(
                  'Background Color Preview',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColorRow(String label, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 140,
            child: Text(
              '$label:',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: AppColors.border),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '$value (${color.toString()})',
              style: AppTextStyles.bodySmall,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusLogic(Medicine medicine) {
    final now = DateTime.now();
    final expiration = medicine.expiration;
    final quantity = medicine.quantity;
    final lowStockThreshold = medicine.lowStockThreshold;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('• Quantity: $quantity', style: AppTextStyles.bodySmall),
        Text('• Low Stock Threshold: $lowStockThreshold',
            style: AppTextStyles.bodySmall),
        if (expiration != null) ...[
          Text('• Expiration: ${DateFormat('dd/MM/yyyy').format(expiration)}',
              style: AppTextStyles.bodySmall),
          Text('• Days until expiration: ${expiration.difference(now).inDays}',
              style: AppTextStyles.bodySmall),
        ] else
          Text('• No expiration date', style: AppTextStyles.bodySmall),
        const SizedBox(height: 8),
        Text(
          'Status Logic Result: ${_getStatusLogicExplanation(medicine)}',
          style: AppTextStyles.bodySmall.copyWith(
            fontWeight: FontWeight.w500,
            color: _getStatusColor(medicine),
          ),
        ),
      ],
    );
  }

  String _getStatusLogicExplanation(Medicine medicine) {
    final now = DateTime.now();
    final expiration = medicine.expiration;
    final quantity = medicine.quantity;
    final lowStockThreshold = medicine.lowStockThreshold;

    if (expiration != null && expiration.isBefore(now)) {
      return 'EXPIRED (expiration date passed)';
    }

    if (lowStockThreshold > 0 && quantity <= lowStockThreshold) {
      return 'LOW_STOCK (threshold > 0 AND quantity ≤ threshold)';
    }

    if (expiration != null) {
      final daysUntilExpiration = expiration.difference(now).inDays;
      if (daysUntilExpiration <= 30) {
        return 'EXPIRING_SOON (≤ 30 days)';
      }
    }

    return 'VALID (normal status)';
  }

  Color _getStatusColor(Medicine medicine) {
    switch (medicine.status) {
      case MedicineStatus.expired:
        return AppColors.expired;
      case MedicineStatus.expiringSoon:
        return AppColors.expiringSoon;
      case MedicineStatus.lowStock:
        return AppColors.lowStock;
      case MedicineStatus.outOfStock:
        return AppColors.outOfStock;
      case MedicineStatus.normal:
        return AppColors.adequate;
    }
  }

  Widget _buildCardComparison() {
    final medicine = selectedMedicine!;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Card Comparison',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Enhanced Medicine Card (with background color):',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            EnhancedMedicineListCard(
              medicine: medicine,
              onTap: () {},
              onEdit: () {},
              onDelete: () {},
            ),
            const SizedBox(height: 16),
            Text(
              'Background Color Analysis:',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.grey50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.border),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Expected Background Color:',
                    style: AppTextStyles.bodySmall.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          color:
                              _getStatusColor(medicine).withValues(alpha: 0.05),
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(color: AppColors.border),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '${_getStatusColor(medicine).withValues(alpha: 0.05)}',
                        style: AppTextStyles.bodySmall,
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'If the enhanced card above doesn\'t show the background color, there may be an issue with the card implementation.',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.grey600,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
