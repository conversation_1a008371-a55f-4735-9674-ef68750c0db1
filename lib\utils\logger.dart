import 'package:flutter/foundation.dart';

/// Centralized logging utility for MedyTrack Mobile
///
/// Provides consistent logging behavior across the application:
/// - Prints logs in debug builds only
/// - Completely silent in release builds
/// - Supports optional tags, errors, and stack traces
/// - Automatically masks sensitive data (UUIDs, emails, tokens, passwords)
class AppLogger {
  static const String _appTag = 'MedyTrack';

  // Sensitive data patterns for masking
  static final RegExp _uuidPattern = RegExp(
      r'\b[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\b');
  static final RegExp _emailPattern =
      RegExp(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b');
  static final RegExp _tokenPattern = RegExp(
    r'(?:token|bearer|jwt|key|secret|password|pwd|pass)\s*[:=]\s*\S+',
    caseSensitive: false,
  );
  static final RegExp _supabaseKeyPattern =
      RegExp(r'\beyJ[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+\b');

  /// Mask sensitive data in strings to prevent data leakage
  static String _maskSensitiveData(String input) {
    if (!kDebugMode) {
      // In release builds, be extra cautious and mask everything
      return input
          .replaceAll(_uuidPattern, '********-****-****-****-************')
          .replaceAll(_emailPattern, '****@****.***')
          .replaceAll(_tokenPattern, '[TOKEN_MASKED]')
          .replaceAll(_supabaseKeyPattern, 'eyJ************************')
          .replaceAll(RegExp(r'\b\d{4,}\b'), '****'); // Mask long numbers
    } else {
      // In debug builds, mask only critical sensitive data
      return input
          .replaceAll(_tokenPattern, '[TOKEN_MASKED]')
          .replaceAll(_supabaseKeyPattern, 'eyJ************************');
    }
  }

  /// Log a message with optional tag, error, and stack trace
  ///
  /// [message] - The log message to display
  /// [tag] - Optional tag to categorize the log (defaults to 'INFO')
  /// [error] - Optional error object to include
  /// [stackTrace] - Optional stack trace to include
  static void log(
    String message, {
    String? tag,
    Object? error,
    StackTrace? stackTrace,
  }) {
    // Only log in debug builds - completely silent in release
    if (kDebugMode) {
      final timestamp = DateTime.now().toIso8601String().substring(11, 23);
      final logTag = tag ?? 'INFO';

      // Mask sensitive data in the message
      final maskedMessage = _maskSensitiveData(message);
      final logMessage = '[$timestamp] [$_appTag:$logTag] $maskedMessage';

      print(logMessage);

      if (error != null) {
        // Mask sensitive data in error messages too
        final maskedError = _maskSensitiveData(error.toString());
        print('  Error: $maskedError');
      }

      if (stackTrace != null) {
        // Stack traces are generally safe, but mask just in case
        final maskedStack = _maskSensitiveData(
            stackTrace.toString().split('\n').take(5).join('\n'));
        print('  Stack: $maskedStack');
      }
    }
  }

  /// Log an authentication-related message
  static void auth(String message, {Object? error, StackTrace? stackTrace}) {
    log(message, tag: 'AUTH', error: error, stackTrace: stackTrace);
  }

  /// Log a BLoC-related message
  static void bloc(String message, {Object? error, StackTrace? stackTrace}) {
    log(message, tag: 'BLOC', error: error, stackTrace: stackTrace);
  }

  /// Log a repository operation
  static void repository(String message,
      {Object? error, StackTrace? stackTrace}) {
    log(message, tag: 'REPO', error: error, stackTrace: stackTrace);
  }

  /// Log a database operation
  static void database(String message,
      {Object? error, StackTrace? stackTrace}) {
    log(message, tag: 'DB', error: error, stackTrace: stackTrace);
  }

  /// Log a UI-related message
  static void ui(String message, {Object? error, StackTrace? stackTrace}) {
    log(message, tag: 'UI', error: error, stackTrace: stackTrace);
  }

  /// Log an error with full context
  static void error(String message, {Object? error, StackTrace? stackTrace}) {
    log(message, tag: 'ERROR', error: error, stackTrace: stackTrace);
  }

  /// Log a warning message
  static void warning(String message, {Object? error, StackTrace? stackTrace}) {
    log(message, tag: 'WARNING', error: error, stackTrace: stackTrace);
  }

  /// Log a network-related message
  static void network(String message, {Object? error, StackTrace? stackTrace}) {
    log(message, tag: 'NETWORK', error: error, stackTrace: stackTrace);
  }

  /// Log sensitive data with extra masking (for debugging purposes only)
  /// This method applies aggressive masking even in debug mode
  static void sensitive(String message,
      {String? tag, Object? error, StackTrace? stackTrace}) {
    if (kDebugMode) {
      final timestamp = DateTime.now().toIso8601String().substring(11, 23);
      final logTag = tag ?? 'SENSITIVE';

      // Apply aggressive masking for sensitive data
      final aggressivelyMasked = message
          .replaceAll(_uuidPattern, '********-****-****-****-************')
          .replaceAll(_emailPattern, '****@****.***')
          .replaceAll(_tokenPattern, '[TOKEN_MASKED]')
          .replaceAll(_supabaseKeyPattern, 'eyJ************************')
          .replaceAll(RegExp(r'\b\d{4,}\b'), '****'); // Mask long numbers

      final logMessage = '[$timestamp] [$_appTag:$logTag] $aggressivelyMasked';
      print(logMessage);

      if (error != null) {
        final maskedError = _maskSensitiveData(error.toString());
        print('  Error: $maskedError');
      }
    }
  }

  /// Validate that a string doesn't contain sensitive data patterns
  /// Returns true if the string appears safe to log
  static bool isSafeToLog(String input) {
    return !_uuidPattern.hasMatch(input) &&
        !_emailPattern.hasMatch(input) &&
        !_tokenPattern.hasMatch(input) &&
        !_supabaseKeyPattern.hasMatch(input);
  }

  /// Get a completely sanitized version of a string for logging
  /// This is the most aggressive masking for production safety
  static String sanitizeForProduction(String input) {
    return input
        .replaceAll(_uuidPattern, '[UUID]')
        .replaceAll(_emailPattern, '[EMAIL]')
        .replaceAll(_tokenPattern, '[TOKEN]')
        .replaceAll(_supabaseKeyPattern, '[JWT]')
        .replaceAll(RegExp(r'\b\d{4,}\b'), '[NUMBER]')
        .replaceAll(
            RegExp(r'[A-Za-z0-9+/]{20,}={0,2}'), '[BASE64]'); // Base64 patterns
  }
}

/// Extension for easy logging in any class
extension AppLogging on Object {
  void logInfo(String message, {Object? error, StackTrace? stackTrace}) {
    AppLogger.log('$runtimeType: $message',
        error: error, stackTrace: stackTrace);
  }

  void logError(String message, {Object? error, StackTrace? stackTrace}) {
    AppLogger.error('$runtimeType: $message',
        error: error, stackTrace: stackTrace);
  }

  void logWarning(String message, {Object? error, StackTrace? stackTrace}) {
    AppLogger.warning('$runtimeType: $message',
        error: error, stackTrace: stackTrace);
  }
}
