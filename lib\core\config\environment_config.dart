import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../../utils/logger.dart';

/// Environment-aware configuration that loads from .env files
/// Uses .env.dev for development and .env.prod for production
class EnvironmentConfig {
  static bool _initialized = false;

  /// Initialize the environment configuration
  /// This should be called before runApp() in main.dart
  static Future<void> initialize({String? forceEnvironment}) async {
    if (_initialized) return;

    try {
      // Determine which environment file to load
      String envFile;

      if (forceEnvironment != null) {
        // Use forced environment (for testing or specific builds)
        envFile = '.env.$forceEnvironment';
      } else {
        // Default behavior: use build mode to determine environment
        envFile = kDebugMode ? '.env.dev' : '.env.prod';
      }

      await dotenv.load(fileName: envFile);

      // Validate environment configuration
      _validateEnvironment();

      _initialized = true;

      if (kDebugMode) {
        AppLogger.log('✅ Environment loaded: $envFile');
        AppLogger.log('🔧 Environment: $environment');
        AppLogger.log('🏥 App Name: $appName');
        AppLogger.log('🔗 Supabase URL: $supabaseUrl');
      }
    } catch (e) {
      if (kDebugMode) {
        AppLogger.error('❌ Failed to load environment configuration', error: e);
      }
      // Fallback to hardcoded values if env files fail to load
      _initialized = true;
    }
  }

  /// Ensure environment is initialized
  static void _ensureInitialized() {
    if (!_initialized) {
      throw StateError(
          'EnvironmentConfig not initialized. Call EnvironmentConfig.initialize() first.');
    }
  }

  /// Validate environment configuration to prevent misconfigurations
  static void _validateEnvironment() {
    // Get values directly from dotenv since _ensureInitialized would fail
    final supabaseUrl = dotenv.env['SUPABASE_URL'] ?? '';
    final supabaseAnonKey = dotenv.env['SUPABASE_ANON_KEY'] ?? '';
    final enableDebugPage =
        dotenv.env['ENABLE_DEBUG_PAGE']?.toLowerCase() == 'true';
    final enableVerboseLogging =
        dotenv.env['ENABLE_VERBOSE_LOGGING']?.toLowerCase() == 'true';

    // Validate required variables exist
    if (supabaseUrl.isEmpty) {
      throw Exception(
          '🚨 CONFIGURATION ERROR: SUPABASE_URL is required but not set');
    }

    if (supabaseAnonKey.isEmpty) {
      throw Exception(
          '🚨 CONFIGURATION ERROR: SUPABASE_ANON_KEY is required but not set');
    }

    // Prevent dev/prod database mixups
    if (!kDebugMode && supabaseUrl.contains('dev')) {
      throw Exception(
          '🚨 CRITICAL SECURITY ERROR: Production build cannot use development database!\nFound: $supabaseUrl');
    }

    if (kDebugMode && supabaseUrl.contains('prod') && !_isTestEnvironment()) {
      if (kDebugMode) {
        AppLogger.log(
            '⚠️ WARNING: Development build is using production database: $supabaseUrl');
      }
    }

    // Validate URL format
    if (!supabaseUrl.startsWith('https://') ||
        !supabaseUrl.contains('.supabase.co')) {
      throw Exception(
          '🚨 CONFIGURATION ERROR: Invalid Supabase URL format: $supabaseUrl');
    }

    // Validate key format (basic JWT structure check)
    if (!supabaseAnonKey.startsWith('eyJ')) {
      throw Exception(
          '🚨 CONFIGURATION ERROR: Invalid Supabase anon key format');
    }

    // Production-specific security validations
    if (!kDebugMode) {
      if (enableDebugPage) {
        throw Exception(
            '🚨 SECURITY ERROR: Debug page must be disabled in production! Set ENABLE_DEBUG_PAGE=false');
      }

      if (enableVerboseLogging) {
        throw Exception(
            '🚨 SECURITY ERROR: Verbose logging must be disabled in production! Set ENABLE_VERBOSE_LOGGING=false');
      }
    }

    if (kDebugMode) {
      AppLogger.log('✅ Environment validation passed');
    }
  }

  /// Check if running in test environment
  static bool _isTestEnvironment() {
    return Platform.environment.containsKey('FLUTTER_TEST') ||
        Platform.environment.containsKey('CI') ||
        Platform.environment.containsKey('GITHUB_ACTIONS');
  }

  // Environment Detection
  static String get environment {
    _ensureInitialized();
    return dotenv.env['ENVIRONMENT'] ??
        (kDebugMode ? 'development' : 'production');
  }

  static bool get isDebug {
    _ensureInitialized();
    return dotenv.env['DEBUG_MODE']?.toLowerCase() == 'true' || kDebugMode;
  }

  static bool get isProduction => !isDebug;

  // Supabase Configuration
  static String get supabaseUrl {
    _ensureInitialized();
    return dotenv.env['SUPABASE_URL'] ??
        (kDebugMode
            ? 'https://sppqqjqbvlsbjovsvdgb.supabase.co' // Dev fallback
            : 'https://wzzykbnebhyvdoagpwvk.supabase.co'); // Prod fallback
  }

  static String get supabaseAnonKey {
    _ensureInitialized();
    return dotenv.env['SUPABASE_ANON_KEY'] ??
        (kDebugMode
            ? 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNwcHFxanFidmxzYmpvdnN2ZGdiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE2MjYwNDMsImV4cCI6MjA2NzIwMjA0M30.kMBIy4oy13LQQ-U9cYi7hXcoxkE27aP9ptfspsO9Vp8'
            : 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Ind6enlrYm5lYmh5dmRvYWdwd3ZrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUwMTMzNjMsImV4cCI6MjA2MDU4OTM2M30.LYLDFRLZB4qtuzxTjRuZqnepRwrg3BNGLwdnwU4D0Es');
  }

  // App Configuration
  static String get appName {
    _ensureInitialized();
    return dotenv.env['APP_NAME'] ??
        (kDebugMode ? 'MedyTrack Mobile (Dev)' : 'MedyTrack Mobile');
  }

  static String get appVersion {
    _ensureInitialized();
    return dotenv.env['APP_VERSION'] ?? '0.6.0';
  }

  static String get appBuildNumber {
    _ensureInitialized();
    return dotenv.env['APP_BUILD_NUMBER'] ?? '1';
  }

  // API Configuration
  static int get apiTimeoutSeconds {
    _ensureInitialized();
    return int.tryParse(dotenv.env['API_TIMEOUT_SECONDS'] ?? '30') ?? 30;
  }

  static int get maxRetryAttempts {
    _ensureInitialized();
    return int.tryParse(dotenv.env['MAX_RETRY_ATTEMPTS'] ?? '3') ?? 3;
  }

  // Feature Flags
  static bool get enableNotifications {
    _ensureInitialized();
    return dotenv.env['ENABLE_NOTIFICATIONS']?.toLowerCase() == 'true';
  }

  static bool get enableBiometricAuth {
    _ensureInitialized();
    return dotenv.env['ENABLE_BIOMETRIC_AUTH']?.toLowerCase() == 'true';
  }

  static bool get enableOfflineMode {
    _ensureInitialized();
    return dotenv.env['ENABLE_OFFLINE_MODE']?.toLowerCase() == 'true';
  }

  static bool get enableAnalytics {
    _ensureInitialized();
    return dotenv.env['ENABLE_ANALYTICS']?.toLowerCase() == 'true';
  }

  static bool get enableDebugPage {
    _ensureInitialized();
    return dotenv.env['ENABLE_DEBUG_PAGE']?.toLowerCase() == 'true' &&
        kDebugMode;
  }

  static bool get enableVerboseLogging {
    _ensureInitialized();
    return dotenv.env['ENABLE_VERBOSE_LOGGING']?.toLowerCase() == 'true' &&
        kDebugMode;
  }

  // Development Tools (only available in debug mode)
  static bool get enableFlutterInspector {
    _ensureInitialized();
    return dotenv.env['ENABLE_FLUTTER_INSPECTOR']?.toLowerCase() == 'true' &&
        kDebugMode;
  }

  static bool get enablePerformanceOverlay {
    _ensureInitialized();
    return dotenv.env['ENABLE_PERFORMANCE_OVERLAY']?.toLowerCase() == 'true' &&
        kDebugMode;
  }

  static bool get showSemanticDebugger {
    _ensureInitialized();
    return dotenv.env['SHOW_SEMANTIC_DEBUGGER']?.toLowerCase() == 'true' &&
        kDebugMode;
  }

  // Logging Configuration
  static bool get enableLogging => isDebug || enableVerboseLogging;

  /// Get environment info for debugging
  static Map<String, dynamic> getEnvironmentInfo() {
    _ensureInitialized();
    return {
      'environment': environment,
      'isDebug': isDebug,
      'isProduction': isProduction,
      'appName': appName,
      'appVersion': appVersion,
      'supabaseUrl': supabaseUrl,
      'enableDebugPage': enableDebugPage,
      'enableVerboseLogging': enableVerboseLogging,
    };
  }
}
