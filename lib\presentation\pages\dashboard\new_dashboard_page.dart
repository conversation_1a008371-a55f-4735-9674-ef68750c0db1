import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';

import '../../../domain/entities/reminder.dart';
import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/auth/auth_event.dart';
import '../../bloc/auth/auth_state.dart' as auth_state;
import '../../bloc/dashboard/dashboard_bloc.dart';
import '../../bloc/medicine/medicine_bloc.dart';
import '../../bloc/reminder/reminder_bloc.dart';
import '../../bloc/reminder/reminder_event.dart';
import '../../bloc/reminder/reminder_state.dart';

import '../../widgets/dashboard/todays_medicines.dart';

import '../../widgets/common/hamburger_menu.dart';

/// New dashboard page matching the mockup design
class NewDashboardPage extends StatefulWidget {
  const NewDashboardPage({super.key});

  @override
  State<NewDashboardPage> createState() => _NewDashboardPageState();
}

class _NewDashboardPageState extends State<NewDashboardPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final TextEditingController _searchController = TextEditingController();
  final PageController _statisticsPageController = PageController();
  int _currentStatisticsPage = 0;

  @override
  void initState() {
    super.initState();
    // Ensure data is loaded when the page is initialized
    _loadDashboardData();
  }

  @override
  void dispose() {
    _statisticsPageController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _loadDashboardData() {
    final householdId =
        context.read<AuthBloc>().state is auth_state.AuthAuthenticated
            ? (context.read<AuthBloc>().state as auth_state.AuthAuthenticated)
                .householdId
            : null;

    if (householdId != null) {
      context
          .read<DashboardBloc>()
          .add(DashboardLoadRequested(householdId: householdId));
      context
          .read<MedicineBloc>()
          .stream
          .firstWhere((state) => state is MedicineLoaded)
          .then((_) {
        final medicineState =
            context.read<MedicineBloc>().state as MedicineLoaded;
        final medicineIds = medicineState.medicines.map((m) => m.id).toList();
        if (medicineIds.isNotEmpty) {
          context.read<ReminderBloc>().add(LoadBatchReminders(medicineIds));
        }
      }).catchError((error) {
        // Handle error if medicine loading fails
        if (kDebugMode) {
          print('[NewDashboardPage] Error loading medicines: $error');
        }
      });
      // Also listen for reminder updates to ensure dashboard stays in sync
      context.read<ReminderBloc>().stream.listen((state) {
        // This will trigger rebuilds when reminder state changes
        if (kDebugMode) {
          print(
              '[NewDashboardPage] Reminder state updated: ${state.runtimeType}');
        }
      });
      context
          .read<MedicineBloc>()
          .add(MedicineLoadRequested(householdId: householdId));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      drawer: _buildDrawer(),
      backgroundColor: AppColors.teal,
      body: Column(
        children: [
          _buildDashboardHeader(),
          Expanded(
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, -4),
                  ),
                ],
              ),
              child: RefreshIndicator(
                onRefresh: () async {
                  _loadDashboardData();
                },
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 16),
                      _buildStatisticsCarousel(),
                      const SizedBox(height: 24),
                      BlocBuilder<ReminderBloc, ReminderState>(
                        buildWhen: (previous, current) {
                          // Rebuild when reminder state changes, especially when we have actual reminders
                          final shouldRebuild =
                              previous != current || current is RemindersLoaded;
                          if (kDebugMode) {
                            print(
                                '[NewDashboardPage] buildWhen - previous: ${previous.runtimeType}, current: ${current.runtimeType}, shouldRebuild: $shouldRebuild');
                          }
                          return shouldRebuild;
                        },
                        builder: (context, reminderState) {
                          if (kDebugMode) {
                            print(
                                '[NewDashboardPage] Building reminder count with state: ${reminderState.runtimeType}');
                          }
                          int todaysReminderCount = 0;
                          if (reminderState is RemindersLoaded) {
                            final todaysReminders =
                                _getTodaysReminders(reminderState.reminders);
                            todaysReminderCount = todaysReminders.length;
                            if (kDebugMode) {
                              print(
                                  '[NewDashboardPage] Today\'s reminders count: $todaysReminderCount');
                            }
                          }
                          return _buildSectionHeader(
                              'Les prises d\'aujourd\'hui ($todaysReminderCount)');
                        },
                      ),
                      const SizedBox(height: 16),
                      const TodaysMedicines(),
                      const SizedBox(height: 32),
                      const SizedBox(height: 100), // Bottom padding
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<_TodayReminder> _getTodaysReminders(List<Reminder> reminders) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final todaysReminders = <_TodayReminder>[];

    for (final reminder in reminders.where((r) => r.isCurrentlyActive)) {
      bool isScheduledToday = false;

      switch (reminder.frequencyType) {
        case 'DAILY':
          isScheduledToday = true;
          break;
        case 'WEEKLY':
          if (reminder.frequencyDays.isNotEmpty) {
            final todayWeekday = now.weekday;
            isScheduledToday = reminder.frequencyDays.contains(todayWeekday);
          }
          break;
        case 'HOURLY_INTERVAL':
          isScheduledToday =
              reminder.startDate.isBefore(now.add(const Duration(days: 1)));
          break;
        case 'SPECIFIC_DATES':
          final today = DateTime(now.year, now.month, now.day);
          isScheduledToday = reminder.specificDates.any((date) {
            final dateOnly = DateTime(date.year, date.month, date.day);
            return dateOnly.isAtSameMomentAs(today);
          });
          break;
      }

      if (isScheduledToday) {
        for (final timeString in reminder.times) {
          final timeParts = timeString.split(':');
          final hour = int.parse(timeParts[0]);
          final minute = int.parse(timeParts[1]);
          final reminderDateTime =
              DateTime(today.year, today.month, today.day, hour, minute);

          todaysReminders.add(_TodayReminder(
            reminder: reminder,
            scheduledTime: reminderDateTime,
            timeString: timeString,
          ));
        }
      }
    }

    todaysReminders.sort((a, b) => a.scheduledTime.compareTo(b.scheduledTime));
    return todaysReminders;
  }

  Widget _buildDashboardHeader() {
    return BlocBuilder<AuthBloc, auth_state.AuthState>(
      builder: (context, authState) {
        String? userName;

        if (authState is auth_state.AuthAuthenticated) {
          userName = authState.user.displayName ??
              authState.user.email.split('@').first;
        }

        return Container(
          padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        Text(
                          userName != null ? 'Bonjour, $userName' : 'Bonjour',
                          style: AppTextStyles.headlineSmall.copyWith(
                            color: AppColors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 24,
                          ),
                        ),
                      ],
                    ),
                  ),
                  GestureDetector(
                    onTap: () => _scaffoldKey.currentState?.openDrawer(),
                    child: Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.menu,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              BlocBuilder<MedicineBloc, MedicineState>(
                builder: (context, medicineState) {
                  final medicineCount = medicineState is MedicineLoaded
                      ? medicineState.medicines.length
                      : 0;

                  return Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: medicineCount > 0
                            ? 'Rechercher parmi vos $medicineCount médicament${medicineCount > 1 ? 's' : ''}...'
                            : 'Chargement des médicaments...',
                        hintStyle: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.grey500,
                        ),
                        prefixIcon: Icon(
                          Icons.search,
                          color: AppColors.grey400,
                        ),
                        suffixIcon: medicineCount > 0
                            ? Padding(
                                padding: const EdgeInsets.all(12),
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color:
                                        AppColors.teal.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    '$medicineCount',
                                    style: AppTextStyles.labelSmall.copyWith(
                                      color: AppColors.teal,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              )
                            : null,
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 16,
                        ),
                      ),
                      style: AppTextStyles.bodyMedium,
                      onChanged: _onSearchChanged,
                    ),
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDrawer() {
    return BlocBuilder<AuthBloc, auth_state.AuthState>(
      builder: (context, authState) {
        String? userName;
        String? userEmail;

        if (authState is auth_state.AuthAuthenticated) {
          userName = authState.user.displayName ??
              authState.user.email.split('@').first;
          userEmail = authState.user.email;
        }

        return HamburgerMenu(
          userName: userName,
          userEmail: userEmail,
          onProfileTap: () {
            Navigator.pop(context);
            context.go('/settings');
          },
          onLogoutTap: () {
            Navigator.pop(context);
            context.read<AuthBloc>().add(AuthSignOutRequested());
          },
        );
      },
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: AppTextStyles.headlineMedium.copyWith(
        color: AppColors.navy,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildStatisticsCarousel() {
    return BlocBuilder<MedicineBloc, MedicineState>(
      builder: (context, medicineState) {
        if (medicineState is MedicineLoaded) {
          final medicines = medicineState.medicines;
          final totalMedicines = medicines.length;

          final now = DateTime.now();
          final expiryMedicines = medicines.where((m) {
            if (m.expiration != null) {
              return m.expiration!.isBefore(now);
            }
            return false;
          }).length;

          final lowStockMedicines = medicines.where((m) {
            final threshold = m.lowStockThreshold;
            final quantity = m.quantity;
            return quantity <= threshold;
          }).length;

          final expiringSoonMedicines = medicines.where((m) {
            if (m.expiration != null) {
              final daysUntilExpiry = m.expiration!.difference(now).inDays;
              return daysUntilExpiry > 0 && daysUntilExpiry <= 30;
            }
            return false;
          }).length;

          return Column(
            children: [
              SizedBox(
                height: 120,
                child: PageView(
                  controller: _statisticsPageController,
                  onPageChanged: (index) {
                    setState(() {
                      _currentStatisticsPage = index;
                    });
                  },
                  children: [
                    Row(
                      children: [
                        Expanded(
                            child: _buildStatCard(
                                'Total medicines',
                                totalMedicines,
                                AppColors.navy,
                                Icons.medication)),
                        const SizedBox(width: 12),
                        Expanded(
                            child: _buildStatCard(
                                'Expiry medicines',
                                expiryMedicines,
                                AppColors.error,
                                Icons.warning)),
                      ],
                    ),
                    Row(
                      children: [
                        Expanded(
                            child: _buildStatCard(
                                'Low stocks',
                                lowStockMedicines,
                                AppColors.warning,
                                Icons.inventory_2)),
                        const SizedBox(width: 12),
                        Expanded(
                            child: _buildStatCard(
                                'Expiring soon',
                                expiringSoonMedicines,
                                AppColors.info,
                                Icons.schedule)),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(2, (index) {
                  return Container(
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _currentStatisticsPage == index
                          ? AppColors.teal
                          : AppColors.grey300,
                    ),
                  );
                }),
              ),
            ],
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildStatCard(String title, int count, Color color, IconData icon) {
    return GestureDetector(
      onTap: () {
        String filter = '';
        switch (title) {
          case 'Total medicines':
            filter = 'all';
            break;
          case 'Expiry medicines':
            filter = 'expired';
            break;
          case 'Low stocks':
            filter = 'low_stock';
            break;
          case 'Expiring soon':
            filter = 'expiring_soon';
            break;
        }
        context.go('/medicines/my?filter=$filter');
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.grey200),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  count.toString(),
                  style: AppTextStyles.headlineLarge.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                    fontSize: 32,
                  ),
                ),
                Icon(
                  icon,
                  color: color.withValues(alpha: 0.3),
                  size: 32,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.grey600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppColors.grey400,
                  size: 12,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _onSearchChanged(String query) {
    if (query.isNotEmpty) {
      context.go('/medicines?search=$query');
    }
  }
}

class _TodayReminder {
  final Reminder reminder;
  final DateTime scheduledTime;
  final String timeString;

  _TodayReminder({
    required this.reminder,
    required this.scheduledTime,
    required this.timeString,
  });
}
