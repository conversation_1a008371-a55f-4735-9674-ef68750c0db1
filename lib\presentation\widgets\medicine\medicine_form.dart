import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../domain/entities/medicine.dart';

class MedicineForm extends StatefulWidget {
  final Medicine? initialMedicine;
  final Function(Medicine) onSubmit;
  final bool isLoading;

  const MedicineForm({
    super.key,
    this.initialMedicine,
    required this.onSubmit,
    this.isLoading = false,
  });

  @override
  State<MedicineForm> createState() => _MedicineFormState();
}

class _MedicineFormState extends State<MedicineForm> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _dosageController = TextEditingController();
  final _quantityController = TextEditingController();
  final _notesController = TextEditingController();
  final _lowStockController = TextEditingController();

  DateTime? _expirationDate;
  String? _selectedForm;
  String? _selectedLocation;
  List<String> _tags = [];
  bool _isCustom = true;

  final List<String> _medicineForm = [
    'Comprimé',
    'Gélule',
    'Sirop',
    'Injection',
    'Pommade',
    'Gouttes',
    'Spray',
    'Suppositoire',
    'Autre',
  ];

  final List<String> _locations = [
    'Armoire à pharmacie',
    'Réfrigérateur',
    'Chambre',
    'Cuisine',
    'Salle de bain',
    'Bureau',
    'Autre',
  ];

  @override
  void initState() {
    super.initState();
    if (widget.initialMedicine != null) {
      _initializeForm(widget.initialMedicine!);
    } else {
      _quantityController.text = '1';
      _lowStockController.text = '5';
    }
  }

  void _initializeForm(Medicine medicine) {
    _nameController.text = medicine.customName ?? medicine.medicineName ?? '';
    _dosageController.text = medicine.dosage ?? '';
    _quantityController.text = medicine.quantity.toString();
    _notesController.text = medicine.notes ?? '';
    _lowStockController.text = medicine.lowStockThreshold.toString();
    _expirationDate = medicine.expiration;
    _selectedForm = medicine.form;
    _selectedLocation = medicine.location;
    _tags = List.from(medicine.tags);
    _isCustom = medicine.isCustom;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _dosageController.dispose();
    _quantityController.dispose();
    _notesController.dispose();
    _lowStockController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildBasicInfoSection(),
            const SizedBox(height: 24),
            _buildQuantitySection(),
            const SizedBox(height: 24),
            _buildLocationSection(),
            const SizedBox(height: 24),
            _buildNotesSection(),
            const SizedBox(height: 32),
            _buildSubmitButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Informations de base',
          style: AppTextStyles.titleMedium,
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _nameController,
          decoration: const InputDecoration(
            labelText: 'Nom du médicament *',
            hintText: 'Ex: Paracétamol',
            prefixIcon: Icon(Icons.medication),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Le nom du médicament est requis';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _dosageController,
          decoration: const InputDecoration(
            labelText: 'Dosage',
            hintText: 'Ex: 500mg',
            prefixIcon: Icon(Icons.science),
          ),
        ),
        const SizedBox(height: 16),
        DropdownButtonFormField<String>(
          initialValue: _selectedForm,
          decoration: const InputDecoration(
            labelText: 'Forme',
            prefixIcon: Icon(Icons.category),
          ),
          items: _medicineForm.map((form) {
            return DropdownMenuItem(
              value: form,
              child: Text(form),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedForm = value;
            });
          },
        ),
        const SizedBox(height: 16),
        InkWell(
          onTap: () => _selectExpirationDate(context),
          child: InputDecorator(
            decoration: const InputDecoration(
              labelText: 'Date d\'expiration',
              prefixIcon: Icon(Icons.calendar_today),
              suffixIcon: Icon(Icons.arrow_drop_down),
            ),
            child: Text(
              _expirationDate != null
                  ? DateFormat('dd/MM/yyyy').format(_expirationDate!)
                  : 'Sélectionner une date',
              style: _expirationDate != null
                  ? null
                  : TextStyle(color: Colors.grey[600]),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildQuantitySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quantité et stock',
          style: AppTextStyles.titleMedium,
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _quantityController,
                decoration: const InputDecoration(
                  labelText: 'Quantité *',
                  prefixIcon: Icon(Icons.inventory_2),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'La quantité est requise';
                  }
                  final quantity = int.tryParse(value);
                  if (quantity == null || quantity <= 0) {
                    return 'Quantité invalide';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                controller: _lowStockController,
                decoration: const InputDecoration(
                  labelText: 'Seuil stock faible',
                  prefixIcon: Icon(Icons.warning),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    final threshold = int.tryParse(value);
                    if (threshold == null || threshold < 0) {
                      return 'Seuil invalide';
                    }
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLocationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Localisation',
          style: AppTextStyles.titleMedium,
        ),
        const SizedBox(height: 16),
        DropdownButtonFormField<String>(
          initialValue: _selectedLocation,
          decoration: const InputDecoration(
            labelText: 'Emplacement',
            prefixIcon: Icon(Icons.location_on),
          ),
          items: _locations.map((location) {
            return DropdownMenuItem(
              value: location,
              child: Text(location),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedLocation = value;
            });
          },
        ),
      ],
    );
  }

  Widget _buildNotesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Notes',
          style: AppTextStyles.titleMedium,
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _notesController,
          decoration: const InputDecoration(
            labelText: 'Notes additionnelles',
            hintText: 'Instructions, effets secondaires, etc.',
            prefixIcon: Icon(Icons.note),
          ),
          maxLines: 3,
        ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: widget.isLoading ? null : _submitForm,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.teal,
          foregroundColor: AppColors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          disabledBackgroundColor: AppColors.grey300,
        ),
        child: widget.isLoading
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor:
                          AlwaysStoppedAnimation<Color>(AppColors.white),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    widget.initialMedicine != null
                        ? 'Modification en cours...'
                        : 'Ajout en cours...',
                    style: AppTextStyles.titleMedium.copyWith(
                      color: AppColors.white,
                    ),
                  ),
                ],
              )
            : Text(
                widget.initialMedicine != null
                    ? 'Modifier le médicament'
                    : 'Ajouter le médicament',
                style: AppTextStyles.titleMedium.copyWith(
                  color: AppColors.white,
                ),
              ),
      ),
    );
  }

  Future<void> _selectExpirationDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          _expirationDate ?? DateTime.now().add(const Duration(days: 365)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 3650)), // 10 years
    );

    if (picked != null && picked != _expirationDate) {
      setState(() {
        _expirationDate = picked;
      });
    }
  }

  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      final medicine = Medicine(
        id: widget.initialMedicine?.id ?? '',
        householdId: widget.initialMedicine?.householdId ?? '',
        customName: _nameController.text.trim(),
        isCustom: _isCustom,
        dosage: _dosageController.text.trim().isEmpty
            ? null
            : _dosageController.text.trim(),
        form: _selectedForm,
        expiration: _expirationDate,
        quantity: int.parse(_quantityController.text),
        tags: _tags,
        location: _selectedLocation,
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        lowStockThreshold: int.tryParse(_lowStockController.text) ?? 5,
        createdAt: widget.initialMedicine?.createdAt ?? DateTime.now(),
        updatedAt: widget.initialMedicine != null ? DateTime.now() : null,
      );

      widget.onSubmit(medicine);
    }
  }
}
