import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/auth/auth_state.dart' as auth_state;
import '../../bloc/settings/settings_bloc.dart';
import '../../bloc/settings/settings_event.dart';
import '../../bloc/settings/settings_state.dart';
import '../../widgets/settings/settings_section.dart';
import '../../widgets/settings/settings_tile.dart';

class DataManagementPage extends StatefulWidget {
  const DataManagementPage({super.key});

  @override
  State<DataManagementPage> createState() => _DataManagementPageState();
}

class _DataManagementPageState extends State<DataManagementPage> {
  @override
  void initState() {
    super.initState();
    // Load settings when page initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authState = context.read<AuthBloc>().state;
      if (authState is auth_state.AuthAuthenticated) {
        context.read<SettingsBloc>().add(
              SettingsLoadRequested(userId: authState.user.id),
            );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.teal,
      body: BlocBuilder<SettingsBloc, SettingsState>(
        builder: (context, state) {
          if (state is SettingsLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is! SettingsLoaded) {
            return const Center(child: Text('Erreur de chargement'));
          }

          return Column(
            children: [
              // Header with teal background
              Container(
                padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
                child: Row(
                  children: [
                    GestureDetector(
                      onTap: () => context.pop(),
                      child: Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.arrow_back,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        'Gestion des Données',
                        style: AppTextStyles.headlineMedium.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Content container with white background
              Expanded(
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(24),
                      topRight: Radius.circular(24),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 8,
                        offset: const Offset(0, -4),
                      ),
                    ],
                  ),
                  child: Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildExportSection(state),
                          const SizedBox(height: 24),
                          _buildImportSection(state),
                          const SizedBox(height: 24),
                          _buildBackupSection(state),
                          const SizedBox(height: 24),
                          _buildSyncSection(state),
                          const SizedBox(height: 32),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  LinearGradient _getHeaderGradient() {
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        AppColors.teal,
        AppColors.navy,
      ],
    );
  }

  Widget _buildIntegratedHeaderCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              GestureDetector(
                onTap: () => Navigator.pop(context),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppColors.navy.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.arrow_back,
                    color: AppColors.navy,
                    size: 24,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Données & Sauvegarde',
                    style: AppTextStyles.headlineSmall.copyWith(
                      color: AppColors.navy,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'Gérer vos données médicales',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.grey600,
                    ),
                  ),
                ],
              ),
            ],
          ),
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: AppColors.teal.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(
              Icons.cloud_outlined,
              color: AppColors.teal,
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExportSection(SettingsLoaded state) {
    return SettingsSection(
      title: '📤 Exporter les données',
      children: [
        SettingsTile(
          icon: Icons.download_outlined,
          title: 'Exporter en JSON',
          subtitle: 'Télécharger toutes vos données au format JSON',
          onTap: () => _showExportDialog(context, 'json'),
        ),
        SettingsTile(
          icon: Icons.table_chart_outlined,
          title: 'Exporter en CSV',
          subtitle: 'Télécharger vos médicaments au format CSV',
          onTap: () => _showExportDialog(context, 'csv'),
        ),
        SettingsTile(
          icon: Icons.picture_as_pdf_outlined,
          title: 'Exporter en PDF',
          subtitle: 'Générer un rapport PDF de vos médicaments',
          onTap: () => _showExportDialog(context, 'pdf'),
        ),
      ],
    );
  }

  Widget _buildImportSection(SettingsLoaded state) {
    return SettingsSection(
      title: '📥 Importer les données',
      children: [
        SettingsTile(
          icon: Icons.upload_outlined,
          title: 'Importer depuis JSON',
          subtitle: 'Restaurer vos données depuis un fichier JSON',
          onTap: () => _showImportDialog(context, 'json'),
        ),
        SettingsTile(
          icon: Icons.table_view_outlined,
          title: 'Importer depuis CSV',
          subtitle: 'Ajouter des médicaments depuis un fichier CSV',
          onTap: () => _showImportDialog(context, 'csv'),
        ),
      ],
    );
  }

  Widget _buildBackupSection(SettingsLoaded state) {
    return SettingsSection(
      title: '💾 Sauvegarde et synchronisation',
      children: [
        SettingsTile(
          icon: Icons.sync_outlined,
          title: 'Synchronisation automatique',
          subtitle: state.settings.app.autoSync ? 'Activée' : 'Désactivée',
          trailing: Switch(
            value: state.settings.app.autoSync,
            onChanged: (value) {
              context.read<SettingsBloc>().add(
                    AppSettingsUpdateRequested(
                      userId: state.settings.userId,
                      settings: state.settings.app.copyWith(autoSync: value),
                    ),
                  );
            },
          ),
        ),
        SettingsTile(
          icon: Icons.dark_mode_outlined,
          title: 'Mode sombre',
          subtitle: state.settings.app.isDarkMode ? 'Activé' : 'Désactivé',
          trailing: Switch(
            value: state.settings.app.isDarkMode,
            onChanged: (value) {
              context.read<SettingsBloc>().add(
                    ThemeUpdateRequested(
                      userId: state.settings.userId,
                      isDarkMode: value,
                    ),
                  );
            },
          ),
        ),
        SettingsTile(
          icon: Icons.language_outlined,
          title: 'Langue de l\'application',
          subtitle: state.settings.app.language.toUpperCase(),
          onTap: () => Navigator.pop(context),
        ),
      ],
    );
  }

  Widget _buildSyncSection(SettingsLoaded state) {
    return SettingsSection(
      title: '🔄 Synchronisation',
      children: [
        SettingsTile(
          icon: Icons.sync_outlined,
          title: 'Synchronisation automatique',
          subtitle: state.settings.app.autoSync ? 'Activée' : 'Désactivée',
          trailing: Switch(
            value: state.settings.app.autoSync,
            onChanged: (value) {
              context.read<SettingsBloc>().add(
                    AppSettingsUpdateRequested(
                      userId: state.settings.userId,
                      settings: state.settings.app.copyWith(autoSync: value),
                    ),
                  );
            },
          ),
        ),
        SettingsTile(
          icon: Icons.sync_problem_outlined,
          title: 'Résoudre les conflits',
          subtitle: 'Gérer les conflits de synchronisation',
          onTap: () => _showConflictResolutionDialog(context, state),
        ),
        SettingsTile(
          icon: Icons.cloud_sync_outlined,
          title: 'Synchroniser maintenant',
          subtitle: 'Forcer la synchronisation avec le cloud',
          onTap: () => _performManualSync(context, state),
        ),
      ],
    );
  }

  String _getBackupFrequencyText(String frequency) {
    switch (frequency) {
      case 'daily':
        return 'Quotidienne';
      case 'weekly':
        return 'Hebdomadaire';
      case 'monthly':
        return 'Mensuelle';
      default:
        return 'Hebdomadaire';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} à ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _showExportDialog(BuildContext context, String format) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Exporter en ${format.toUpperCase()}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.download_outlined,
              size: 48,
              color: AppColors.teal,
            ),
            const SizedBox(height: 16),
            Text(
              'Voulez-vous exporter toutes vos données au format ${format.toUpperCase()} ?',
              textAlign: TextAlign.center,
              style: AppTextStyles.bodyMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Le fichier sera téléchargé dans votre dossier de téléchargements.',
              textAlign: TextAlign.center,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.grey600,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Export ${format.toUpperCase()} en cours...'),
                  backgroundColor: AppColors.teal,
                ),
              );
              // TODO: Implement actual export functionality
            },
            child: const Text('Exporter'),
          ),
        ],
      ),
    );
  }

  void _showImportDialog(BuildContext context, String format) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Importer depuis ${format.toUpperCase()}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.upload_outlined,
              size: 48,
              color: AppColors.teal,
            ),
            const SizedBox(height: 16),
            Text(
              'Sélectionnez un fichier ${format.toUpperCase()} à importer.',
              textAlign: TextAlign.center,
              style: AppTextStyles.bodyMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Attention: Cette action peut remplacer vos données existantes.',
              textAlign: TextAlign.center,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.error,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content:
                      Text('Sélection de fichier ${format.toUpperCase()}...'),
                  backgroundColor: AppColors.teal,
                ),
              );
              // TODO: Implement actual import functionality
            },
            child: const Text('Sélectionner'),
          ),
        ],
      ),
    );
  }

  void _showBackupFrequencyDialog(BuildContext context, SettingsLoaded state) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Fréquence de sauvegarde'),
        content: const Text(
            'Cette fonctionnalité sera disponible dans une prochaine version.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showBackupHistoryDialog(BuildContext context, SettingsLoaded state) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Historique des sauvegardes'),
        content: const Text(
            'Cette fonctionnalité sera disponible dans une prochaine version.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showConflictResolutionDialog(
      BuildContext context, SettingsLoaded state) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Résolution des conflits'),
        content: const Text(
            'Cette fonctionnalité sera disponible dans une prochaine version.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _performManualSync(BuildContext context, SettingsLoaded state) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Synchronisation en cours...'),
        backgroundColor: AppColors.teal,
        duration: const Duration(seconds: 2),
      ),
    );

    // Simulate sync process
    Future.delayed(const Duration(seconds: 2), () {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Synchronisation terminée avec succès!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    });
  }
}
