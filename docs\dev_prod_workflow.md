# MedyTrack Mobile - Development to Production Workflow

## 🎯 Overview

This document outlines the complete workflow for managing MedyTrack Mobile development from feature creation to production deployment, ensuring proper environment separation and data security.

## 🌿 Branch Strategy

### Branch Structure
```
main (Production)
├── develop (Development)
│   ├── feature/feature-name
│   ├── bugfix/bug-description
│   └── release/v0.5.1
└── hotfix/critical-fix (Emergency only)
```

### Branch Naming Conventions
- **Features**: `feature/add-medicine-location-display`
- **Bug Fixes**: `bugfix/fix-location-uuid-display`
- **Releases**: `release/v0.5.1`
- **Hotfixes**: `hotfix/critical-auth-fix`

### Workflow Steps

#### 1. Feature Development
```bash
# Start from develop branch
git checkout develop
git pull origin develop

# Create feature branch
git checkout -b feature/your-feature-name

# Development work...
git add .
git commit -m "feat: implement feature description"

# Push and create PR to develop
git push origin feature/your-feature-name
```

#### 2. Release Preparation
```bash
# Create release branch from develop
git checkout develop
git pull origin develop
git checkout -b release/v0.5.1

# Update version numbers, run tests, final fixes
# Commit release preparation changes
git add .
git commit -m "chore: prepare release v0.5.1"

# Push release branch
git push origin release/v0.5.1
```

#### 3. Production Deployment
```bash
# Merge release to main
git checkout main
git pull origin main
git merge release/v0.5.1

# Tag the release
git tag -a v0.5.1 -m "Release v0.5.1: Production hardening & workflow"
git push origin main --tags

# Merge back to develop
git checkout develop
git merge main
git push origin develop
```

## 🔧 Environment Configuration

### Development Environment
- **Branch**: `develop`
- **Environment File**: `.env.dev`
- **Supabase Project**: `sppqqjqbvlsbjovsvdgb` (MedyTrack Dev)
- **Database**: Development data with test records
- **Logging**: Full debug logging enabled
- **URL**: `https://sppqqjqbvlsbjovsvdgb.supabase.co`

### Production Environment
- **Branch**: `main`
- **Environment File**: `.env.prod`
- **Supabase Project**: `wzzykbnebhyvdoagpwvk` (MedyTrack Prod)
- **Database**: Production data with real user records
- **Logging**: Critical errors only, no sensitive data
- **URL**: `https://wzzykbnebhyvdoagpwvk.supabase.co`

### Environment Switching
```bash
# For Development (develop branch)
cp .env.dev .env

# For Production (main branch)
cp .env.prod .env
```

## 🧪 Testing Requirements

### Development Testing Checklist
- [ ] All new features tested in Dev environment
- [ ] Medicine CRUD operations working
- [ ] Reminder system functional
- [ ] Location names display correctly (not UUIDs)
- [ ] Authentication flows working
- [ ] No console errors or warnings
- [ ] Debug logs visible in development

### Pre-Production Testing Checklist
- [ ] All Dev tests passing
- [ ] Production build compiles successfully
- [ ] No debug output in production build
- [ ] No sensitive data in logs
- [ ] Database connections working
- [ ] Performance acceptable
- [ ] Cross-platform compatibility verified

## 🚀 Deployment Process

### Step 1: Environment Preparation
```bash
# Ensure you're on the correct branch
git branch --show-current

# For Production deployment
git checkout main
git pull origin main

# Verify environment configuration
cat .env.prod
```

### Step 2: Build Verification
```bash
# Clean previous builds
flutter clean
flutter pub get

# Build for production
flutter build web --release
flutter build apk --release
flutter build ios --release
```

### Step 3: Database Synchronization
1. **Export Production Data** (if needed)
2. **Backup Current State**
3. **Verify Data Integrity**
4. **Test Database Connections**

### Step 4: Deployment Validation
- [ ] Application starts successfully
- [ ] User authentication working
- [ ] Medicine data displays correctly
- [ ] No debug output in console
- [ ] Performance metrics acceptable

## 📊 Database Management

### Synchronization Process
```bash
# Development to Production sync (when needed)
# 1. Export Dev database
pg_dump -h dev-host -U user -d medytrack_dev > dev_backup.sql

# 2. Import to Production (CAUTION!)
psql -h prod-host -U user -d medytrack_prod < dev_backup.sql
```

### Data Validation
- **Medicine Count Verification**
- **User Data Integrity**
- **Location Names Mapping**
- **Reminder System Functionality**

## 🔒 Security Guidelines

### Development Environment
- Debug logging allowed
- Test data acceptable
- Console output permitted
- Detailed error messages

### Production Environment
- **ZERO debug logging**
- **NO sensitive data in logs**
- **NO UUID exposure**
- **NO user data in console**
- **Critical errors only**

## 📋 Version Management

### Files to Update for Each Release
1. **`pubspec.yaml`**: Update version number
2. **`lib/core/config/app_config.dart`**: Update version constant
3. **`CHANGELOG.md`**: Add release notes
4. **Git Tags**: Create annotated release tags

### Version Format
- **Format**: `MAJOR.MINOR.PATCH`
- **Example**: `0.5.1`
- **Increment Rules**:
  - MAJOR: Breaking changes
  - MINOR: New features
  - PATCH: Bug fixes

## 🔄 Workflow Diagram

```
Development Cycle:
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│   Feature   │───▶│   Develop    │───▶│   Release   │
│   Branch    │    │   Branch     │    │   Branch    │
└─────────────┘    └──────────────┘    └─────────────┘
                           │                    │
                           ▼                    ▼
                   ┌──────────────┐    ┌─────────────┐
                   │ Dev Testing  │    │ Production  │
                   │ Environment  │    │   Branch    │
                   └──────────────┘    └─────────────┘
```

## 🚨 Emergency Procedures

### Hotfix Process
```bash
# Create hotfix from main
git checkout main
git checkout -b hotfix/critical-issue

# Fix the issue
git add .
git commit -m "hotfix: resolve critical issue"

# Deploy immediately to main
git checkout main
git merge hotfix/critical-issue
git tag -a v0.5.2 -m "Hotfix v0.5.2"
git push origin main --tags

# Merge back to develop
git checkout develop
git merge main
git push origin develop
```

## 🔄 Database Synchronization

### 🚨 **DATABASE SYNC RISK MANAGEMENT FRAMEWORK**

#### **CRITICAL HIERARCHY: NEVER → PREFERRED → LAST RESORT**

##### **❌ NEVER (Forbidden Operations)**
- **Production NEVER overwritten by Development data** except in planned migrations
- **User authentication data NEVER synchronized** (auth.users, auth.sessions, etc.)
- **Production user data NEVER replaced** without explicit stakeholder approval
- **System logs and errors NEVER synchronized** (app_errors, audit_logs)
- **Storage objects NEVER synchronized** (storage.objects, storage.buckets)

##### **✅ PREFERRED (Safe Operations)**
- **Schema-only synchronization** with structure changes but no data
- **Reference data updates** (tunisia_medicines, presentations, specialties)
- **Production → Development sync** for testing with real data
- **Selective data updates** using targeted SQL queries with WHERE clauses

##### **⚠️ LAST RESORT (High-Risk Operations)**
- **Full data replacement** requires mandatory stakeholder approval
- **Development → Production sync** only for major feature releases
- **User data migration** requires comprehensive backup and validation
- **Cross-environment user data transfer** with explicit consent

#### **📊 Risk Assessment Matrix**

| Operation Type | Risk Level | Approval Required | Backup Required | Rollback Time | Stakeholders |
|----------------|------------|-------------------|-----------------|---------------|--------------|
| Schema Only | 🟢 **LOW** | Developer + Peer Review | Optional | < 5 minutes | Dev Team |
| Reference Data | 🟡 **MEDIUM** | Tech Lead + QA Lead | Mandatory | < 15 minutes | Tech + QA |
| Prod → Dev | 🟡 **MEDIUM** | Tech Lead | Recommended | < 30 minutes | Tech Lead |
| Dev → Prod | 🔴 **HIGH** | Product Owner + CTO | Mandatory | < 1 hour | Executive |
| Full Replace | 🔴 **CRITICAL** | Executive + Legal | Mandatory | < 2 hours | C-Suite |

#### **🔒 Mandatory Approval Workflow for High-Risk Operations**

##### **Pre-Approval Checklist**
- [ ] **Business Justification**: Clear documented reason for production database changes
- [ ] **Impact Assessment**: Analysis of affected users, features, and potential downtime
- [ ] **Rollback Plan**: Detailed step-by-step procedure for emergency rollback
- [ ] **Stakeholder Approval**: Required signatures based on risk level matrix
- [ ] **Backup Verification**: Confirmed recent backup with integrity validation
- [ ] **Testing Validation**: Comprehensive testing in staging environment
- [ ] **Communication Plan**: User notification strategy and support team briefing
- [ ] **Monitoring Plan**: Post-change monitoring and success criteria

##### **Approval Hierarchy & Timeline**
```yaml
🟢 LOW Risk (Schema Only):
  Required: Developer + Peer Review
  Timeline: Immediate (same day)
  Documentation: Git commit message

🟡 MEDIUM Risk (Reference Data/Prod→Dev):
  Required: Tech Lead + QA Lead
  Timeline: Same day approval
  Documentation: Change request ticket

🔴 HIGH Risk (Dev→Prod):
  Required: Product Owner + CTO + Security Lead
  Timeline: 24-48 hours approval process
  Documentation: Formal change management document

🔴 CRITICAL Risk (Full Replacement):
  Required: Executive Team + Legal + Compliance
  Timeline: 1-2 weeks approval process
  Documentation: Executive briefing + legal review
```

### Current Database Status (as of 2025-09-12)

#### Production Database (wzzykbnebhyvdoagpwvk)
- **Total Medicines**: 48 records (46 for main user)
- **Schema Version**: Base schema without recent enhancements
- **Missing Columns**: `form`, `presentation`, `barcode`, `updated_at`
- **Data Integrity**: ✅ Clean (no null user_id records)

#### Development Database (sppqqjqbvlsbjovsvdgb)
- **Total Medicines**: 52 records (50 for main user + 2 with null user_id)
- **Schema Version**: Enhanced schema with latest columns
- **Additional Columns**: `form`, `presentation`, `barcode`, `updated_at`
- **Data Integrity**: ⚠️ Has 2 records with null user_id (needs cleanup)

### Schema Differences Analysis
```sql
-- Development has these additional columns in user_medicines:
ALTER TABLE user_medicines ADD COLUMN form text;
ALTER TABLE user_medicines ADD COLUMN presentation text;
ALTER TABLE user_medicines ADD COLUMN barcode text;
ALTER TABLE user_medicines ADD COLUMN updated_at timestamp with time zone;
```

### 🔒 **CRITICAL SECURITY SAFEGUARDS**

#### **Tables NEVER to Synchronize**
```sql
-- ⚠️ FORBIDDEN: These tables must NEVER be synchronized between environments
auth.users              -- User authentication data (security risk)
auth.sessions           -- User session data (security risk)
auth.refresh_tokens     -- Authentication tokens (security risk)
app_errors              -- Error logs with sensitive debug data
audit_logs              -- System audit trails (if exists)
storage.objects         -- File storage metadata
storage.buckets         -- Storage bucket configuration
```

#### **Pre-Sync Mandatory Cleanup**
```sql
-- 🧹 CRITICAL: Execute these commands before ANY synchronization
-- 1. Remove invalid records with null user_id
DELETE FROM dose_history WHERE user_id IS NULL;
DELETE FROM reminders WHERE user_id IS NULL;
DELETE FROM user_medicines WHERE user_id IS NULL;
DELETE FROM locations WHERE user_id IS NULL;
DELETE FROM family_members WHERE user_id IS NULL;

-- 2. Verify cleanup success (all should return 0)
SELECT 'dose_history' as table_name, COUNT(*) as null_user_records FROM dose_history WHERE user_id IS NULL
UNION ALL
SELECT 'reminders', COUNT(*) FROM reminders WHERE user_id IS NULL
UNION ALL
SELECT 'user_medicines', COUNT(*) FROM user_medicines WHERE user_id IS NULL
UNION ALL
SELECT 'locations', COUNT(*) FROM locations WHERE user_id IS NULL
UNION ALL
SELECT 'family_members', COUNT(*) FROM family_members WHERE user_id IS NULL;
```

#### **Truncation Strategy (Prevent Duplicates)**
```sql
-- 🗑️ CRITICAL: Truncate target tables before import to prevent duplicates
-- Execute in this EXACT order to respect foreign key constraints:

-- Step 1: Truncate dependent tables first
TRUNCATE TABLE dose_history CASCADE;
TRUNCATE TABLE reminders CASCADE;

-- Step 2: Truncate main tables
TRUNCATE TABLE user_medicines CASCADE;
TRUNCATE TABLE locations CASCADE;
TRUNCATE TABLE family_members CASCADE;

-- Step 3: Reset sequences (if using SERIAL columns)
ALTER SEQUENCE dose_history_id_seq RESTART WITH 1;
-- Add other sequences as needed based on your schema
```

### Development to Production Migration Plan

#### Phase 1: Schema Synchronization
1. **Backup Production Database**
   ```bash
   # Create full backup before any changes
   pg_dump -h [prod-host] -U postgres [prod-db] > prod_backup_$(date +%Y%m%d).sql
   ```

2. **Apply Schema Updates to Production**
   ```sql
   -- Add missing columns to Production
   ALTER TABLE user_medicines ADD COLUMN form text;
   ALTER TABLE user_medicines ADD COLUMN presentation text;
   ALTER TABLE user_medicines ADD COLUMN barcode text;
   ALTER TABLE user_medicines ADD COLUMN updated_at timestamp with time zone DEFAULT now();
   ```

#### Phase 2: Data Cleanup & Validation
1. **Clean Development Database**
   ```sql
   -- Execute mandatory cleanup commands from Security Safeguards section above
   DELETE FROM user_medicines WHERE user_id IS NULL;
   DELETE FROM reminders WHERE user_id IS NULL;
   DELETE FROM dose_history WHERE user_id IS NULL;
   ```

2. **Validate Data Integrity**
   ```sql
   -- Check for data consistency
   SELECT COUNT(*) FROM user_medicines WHERE user_id = '04be91d5-d668-437c-b90b-2a6834becc8c';
   -- Verify no null user_id records remain
   SELECT COUNT(*) FROM user_medicines WHERE user_id IS NULL; -- Must be 0
   ```

#### Phase 3: Data Migration Strategy
**Option A: Full Replacement (Recommended)**
- Export all Development data for main user
- Clear Production data for main user
- Import Development data to Production
- Ensures complete synchronization

**Option B: Incremental Sync**
- Identify new records in Development (created after last sync)
- Export only new/modified records
- Import to Production with conflict resolution

### Synchronization Checklist
- [ ] **Pre-Migration**
  - [ ] Backup Production database
  - [ ] Document current record counts
  - [ ] Verify Development data integrity
  - [ ] Test application on both environments

- [ ] **Schema Migration**
  - [ ] Apply schema updates to Production
  - [ ] Verify column additions successful
  - [ ] Test basic queries on updated schema

- [ ] **Data Migration**
  - [ ] Clean null user_id records from Development
  - [ ] Export Development data (main user only)
  - [ ] Import to Production with validation
  - [ ] Verify record counts match

- [ ] **Post-Migration Validation**
  - [ ] Test application functionality on Production
  - [ ] Verify location names display correctly
  - [ ] Check reminder system functionality
  - [ ] Validate all CRUD operations
  - [ ] Update RLS policies if needed

- [ ] **Documentation**
  - [ ] Document migration results
  - [ ] Update CHANGELOG.md
  - [ ] Record new baseline counts
  - [ ] Update workflow documentation

## 🔄 **Rollback Procedures**

### Emergency Rollback Process
If issues are discovered after deployment, follow these steps immediately:

#### **Step 1: Immediate Assessment**
```bash
# Check application status
flutter run -d chrome --web-port=8083 --release
# Monitor for errors, crashes, or data issues
```

#### **Step 2: Database Rollback**
```bash
# If database migration caused issues:
# 1. Stop all application instances
# 2. Restore from backup
pg_restore -h [prod-host] -U postgres -d [prod-db] prod_backup_$(date +%Y%m%d).sql

# 3. Verify restoration
psql -h [prod-host] -U postgres -d [prod-db] -c "SELECT COUNT(*) FROM user_medicines;"
```

#### **Step 3: Code Rollback**
```bash
# Rollback to previous stable version
git checkout main
git reset --hard [previous-stable-commit]
git push --force-with-lease origin main

# Update version tags
git tag -d v0.5.1  # Delete problematic tag
git push origin :refs/tags/v0.5.1  # Remove from remote
```

#### **Step 4: Environment Restoration**
```bash
# Ensure environment configuration is correct
cp .env.prod .env  # Restore production environment
flutter clean
flutter pub get
flutter build web --release
```

### Rollback Decision Matrix
| **Issue Severity** | **Action** | **Timeline** |
|-------------------|------------|--------------|
| **Critical** (App crashes, data loss) | Immediate rollback | < 5 minutes |
| **High** (Major features broken) | Rollback within 1 hour | < 1 hour |
| **Medium** (Minor UI issues) | Fix forward or rollback | < 4 hours |
| **Low** (Cosmetic issues) | Fix forward | Next release |

## 🏷️ **GitHub Release & Tagging Requirements**

### **MANDATORY: All Production Releases Must Be Tagged**

#### **Pre-Release Checklist**
- [ ] All tests passing on develop branch
- [ ] Production build successful with zero debug output
- [ ] Database migration (if any) tested and documented
- [ ] CHANGELOG.md updated with release notes
- [ ] Version numbers updated in all configuration files

#### **Tagging Process**
```bash
# 1. Ensure you're on the main branch
git checkout main
git pull origin main

# 2. Create annotated tag with detailed message
git tag -a v0.5.1 -m "Release v0.5.1: Production Hardening & Development Workflow

🎯 Major Features:
- Production security hardening with zero debug output
- Medicine location display bug fix
- Comprehensive development workflow documentation
- Advanced logging with sensitive data masking
- Database synchronization procedures

🔧 Technical Improvements:
- Enhanced AppLogger with production-safe masking
- Comprehensive database sync safeguards
- Rollback procedures and emergency protocols
- GitHub release management requirements

🧪 Testing:
- All functionality verified on Dev environment
- Production build tested with zero console output
- Database operations validated with proper RLS policies

📊 Database Status:
- Production: 46 medicines (clean, stable)
- Development: 50 medicines (enhanced schema)
- Both environments functional and secure"

# 3. Push tag to GitHub
git push origin v0.5.1

# 4. Create GitHub Release
# Go to GitHub → Releases → Create new release
# - Tag: v0.5.1
# - Title: "MedyTrack Mobile v0.5.1 - Production Hardening & Development Workflow"
# - Description: Copy from CHANGELOG.md
# - Attach build artifacts if applicable
```

#### **Release Naming Convention**
- **Format**: `v{MAJOR}.{MINOR}.{PATCH}`
- **Examples**: `v0.5.1`, `v1.0.0`, `v1.2.3`
- **Pre-releases**: `v0.5.1-beta.1`, `v0.5.1-rc.1`

#### **GitHub Release Requirements**
1. **Tag Creation**: Must be annotated tag with detailed message
2. **Release Notes**: Must include comprehensive changelog
3. **Build Artifacts**: Attach production builds when applicable
4. **Documentation**: Link to relevant documentation updates
5. **Migration Notes**: Include database migration instructions if applicable

#### **Post-Release Actions**
```bash
# 1. Update develop branch with main
git checkout develop
git merge main
git push origin develop

# 2. Update project board/issues
# - Close completed issues
# - Update project milestones
# - Plan next release cycle

# 3. Notify team
# - Send release announcement
# - Update deployment documentation
# - Schedule post-release monitoring
```

## 🧪 **Testing Integration**

### **Automated Testing Requirements**
Before any production deployment, the following tests must pass:

#### **Unit Tests**
```bash
# Run all unit tests
flutter test

# Run specific test suites
flutter test test/medicine_location_rendering_test.dart
flutter test test/widget/medicine_location_widget_test.dart
```

#### **Widget Tests**
```bash
# Test medicine location rendering specifically
flutter test test/widget/medicine_location_widget_test.dart

# Verify no UUID display in medicine cards
flutter test --name "should NOT display UUID-like strings"
```

#### **Integration Tests**
```bash
# Run integration tests on connected device
flutter drive --target=test_driver/app.dart
```

### **Manual Testing Checklist**
- [ ] **Device Smoke Tests**: Complete `test/smoke_tests/device_smoke_test_checklist.md`
- [ ] **Medicine Location Verification**: Ensure no UUIDs displayed in medicine cards
- [ ] **Production Build Testing**: Zero debug output verification
- [ ] **Cross-Platform Testing**: Android, iOS, Web functionality

### **Testing Documentation**
- **Smoke Test Checklist**: `test/smoke_tests/device_smoke_test_checklist.md`
- **Medicine Location Tests**: `test/medicine_location_rendering_test.dart`
- **Widget Tests**: `test/widget/medicine_location_widget_test.dart`

### **Test Coverage Requirements**
- **Minimum Coverage**: 80% for critical paths
- **Medicine Location Bug**: 100% coverage for location rendering
- **Production Security**: All logging and masking functions tested

## 📞 Support Contacts

- **Development Issues**: Check develop branch logs
- **Production Issues**: Check main branch, no debug logs
- **Database Issues**: Verify environment configuration
- **Build Issues**: Clean and rebuild with correct environment
- **Testing Issues**: Review test documentation and run automated tests

## 📋 **Quick Reference Commands**

### **Development Workflow**
```bash
# Start development
git checkout develop
flutter run -d chrome --web-port=8083

# Run tests
flutter test
flutter test test/medicine_location_rendering_test.dart

# Build for production
flutter build web --release
flutter build apk --release
```

### **Production Deployment**
```bash
# Switch to production
git checkout main
git merge develop
git tag -a v0.5.1 -m "Release notes"
git push origin main --tags

# Deploy production build
flutter build web --release --dart-define-from-file=.env.prod
```

### **Emergency Procedures**
```bash
# Quick rollback
git checkout main
git reset --hard [previous-stable-commit]
git push --force-with-lease origin main

# Database rollback
pg_restore -h [prod-host] -U postgres -d [prod-db] backup.sql
```

---

**Last Updated**: 2025-09-12
**Version**: 2.0
**Applicable to**: MedyTrack Mobile v0.5.1+
**Includes**: Production hardening, testing infrastructure, rollback procedures, GitHub release management
