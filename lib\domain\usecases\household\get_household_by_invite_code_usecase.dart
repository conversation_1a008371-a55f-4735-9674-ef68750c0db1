import 'package:dartz/dartz.dart';
import '../../entities/household.dart';
import '../../repositories/household_repository.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';

/// Use case for getting household by invite code
class GetHouseholdByInviteCodeUseCase implements UseCase<Household, String> {
  final HouseholdRepository repository;

  GetHouseholdByInviteCodeUseCase(this.repository);

  @override
  Future<Either<Failure, Household>> call(String inviteCode) async {
    return await repository.getHouseholdByInviteCode(inviteCode);
  }
}
