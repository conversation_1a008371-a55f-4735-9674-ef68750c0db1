import 'package:flutter_test/flutter_test.dart';
import 'package:medytrack_mobile_v2/domain/entities/reminder.dart';

void main() {
  group('DoseHistory Entity Tests', () {
    const testUserId = '123e4567-e89b-12d3-a456-426614174000';
    const testUserMedicineId = '987fcdeb-51a2-43d1-9f12-123456789abc';
    const testReminderId = '456e7890-e12b-34d5-a678-901234567def';
    final testScheduledAt = DateTime(2024, 7, 29, 8, 0);
    final testActionAt = DateTime(2024, 7, 29, 8, 5);

    test('should create DoseHistory with all required fields including userId',
        () {
      // Arrange & Act
      final doseHistory = DoseHistory(
        id: 1,
        userId: testUserId,
        userMedicineId: testUserMedicineId,
        reminderId: testReminderId,
        scheduledAt: testScheduledAt,
        actionAt: testActionAt,
        status: 'TAKEN',
      );

      // Assert
      expect(doseHistory.id, equals(1));
      expect(doseHistory.userId, equals(testUserId));
      expect(doseHistory.userMedicineId, equals(testUserMedicineId));
      expect(doseHistory.reminderId, equals(testReminderId));
      expect(doseHistory.scheduledAt, equals(testScheduledAt));
      expect(doseHistory.actionAt, equals(testActionAt));
      expect(doseHistory.status, equals('TAKEN'));
    });

    test('should create DoseHistory without optional fields', () {
      // Arrange & Act
      final doseHistory = DoseHistory(
        userId: testUserId,
        userMedicineId: testUserMedicineId,
        scheduledAt: testScheduledAt,
        status: 'SKIPPED',
      );

      // Assert
      expect(doseHistory.id, isNull);
      expect(doseHistory.userId, equals(testUserId));
      expect(doseHistory.userMedicineId, equals(testUserMedicineId));
      expect(doseHistory.reminderId, isNull);
      expect(doseHistory.scheduledAt, equals(testScheduledAt));
      expect(doseHistory.actionAt, isNull);
      expect(doseHistory.status, equals('SKIPPED'));
    });

    test('should serialize to JSON correctly with user_id field', () {
      // Arrange
      final doseHistory = DoseHistory(
        id: 1,
        userId: testUserId,
        userMedicineId: testUserMedicineId,
        reminderId: testReminderId,
        scheduledAt: testScheduledAt,
        actionAt: testActionAt,
        status: 'TAKEN',
      );

      // Act
      final json = doseHistory.toJson();

      // Assert
      expect(json['id'], equals(1));
      expect(json['user_id'], equals(testUserId));
      expect(json['user_medicine_id'], equals(testUserMedicineId));
      expect(json['reminder_id'], equals(testReminderId));
      expect(json['scheduled_time'], equals(testScheduledAt.toIso8601String()));
      expect(json['action_time'], equals(testActionAt.toIso8601String()));
      expect(json['status'], equals('TAKEN'));
    });

    test('should serialize to JSON with null values correctly', () {
      // Arrange
      final doseHistory = DoseHistory(
        userId: testUserId,
        userMedicineId: testUserMedicineId,
        scheduledAt: testScheduledAt,
        status: 'SNOOZED',
      );

      // Act
      final json = doseHistory.toJson();

      // Assert
      expect(
          json.containsKey('id'), isFalse); // id should be excluded when null
      expect(json['user_id'], equals(testUserId));
      expect(json['user_medicine_id'], equals(testUserMedicineId));
      expect(json['reminder_id'], isNull);
      expect(json['scheduled_time'], equals(testScheduledAt.toIso8601String()));
      expect(json['action_time'], isNull);
      expect(json['status'], equals('SNOOZED'));
    });

    test('should deserialize from JSON correctly with user_id field', () {
      // Arrange
      final json = {
        'id': 1,
        'user_id': testUserId,
        'user_medicine_id': testUserMedicineId,
        'reminder_id': testReminderId,
        'scheduled_time': testScheduledAt.toIso8601String(),
        'action_time': testActionAt.toIso8601String(),
        'status': 'TAKEN',
      };

      // Act
      final doseHistory = DoseHistory.fromJson(json);

      // Assert
      expect(doseHistory.id, equals(1));
      expect(doseHistory.userId, equals(testUserId));
      expect(doseHistory.userMedicineId, equals(testUserMedicineId));
      expect(doseHistory.reminderId, equals(testReminderId));
      expect(doseHistory.scheduledAt, equals(testScheduledAt));
      expect(doseHistory.actionAt, equals(testActionAt));
      expect(doseHistory.status, equals('TAKEN'));
    });

    test('should deserialize from JSON with null values correctly', () {
      // Arrange
      final json = {
        'id': null,
        'user_id': testUserId,
        'user_medicine_id': testUserMedicineId,
        'reminder_id': null,
        'scheduled_time': testScheduledAt.toIso8601String(),
        'action_time': null,
        'status': 'SKIPPED',
      };

      // Act
      final doseHistory = DoseHistory.fromJson(json);

      // Assert
      expect(doseHistory.id, isNull);
      expect(doseHistory.userId, equals(testUserId));
      expect(doseHistory.userMedicineId, equals(testUserMedicineId));
      expect(doseHistory.reminderId, isNull);
      expect(doseHistory.scheduledAt, equals(testScheduledAt));
      expect(doseHistory.actionAt, isNull);
      expect(doseHistory.status, equals('SKIPPED'));
    });

    test('should support equality comparison with userId field', () {
      // Arrange
      final doseHistory1 = DoseHistory(
        id: 1,
        userId: testUserId,
        userMedicineId: testUserMedicineId,
        reminderId: testReminderId,
        scheduledAt: testScheduledAt,
        actionAt: testActionAt,
        status: 'TAKEN',
      );

      final doseHistory2 = DoseHistory(
        id: 1,
        userId: testUserId,
        userMedicineId: testUserMedicineId,
        reminderId: testReminderId,
        scheduledAt: testScheduledAt,
        actionAt: testActionAt,
        status: 'TAKEN',
      );

      final doseHistory3 = DoseHistory(
        id: 1,
        userId: 'different-user-id',
        userMedicineId: testUserMedicineId,
        reminderId: testReminderId,
        scheduledAt: testScheduledAt,
        actionAt: testActionAt,
        status: 'TAKEN',
      );

      // Act & Assert
      expect(doseHistory1, equals(doseHistory2));
      expect(doseHistory1, isNot(equals(doseHistory3)));
    });

    test('should handle different status values', () {
      // Arrange & Act
      final takenDose = DoseHistory(
        userId: testUserId,
        userMedicineId: testUserMedicineId,
        scheduledAt: testScheduledAt,
        status: 'TAKEN',
      );

      final skippedDose = DoseHistory(
        userId: testUserId,
        userMedicineId: testUserMedicineId,
        scheduledAt: testScheduledAt,
        status: 'SKIPPED',
      );

      final snoozedDose = DoseHistory(
        userId: testUserId,
        userMedicineId: testUserMedicineId,
        scheduledAt: testScheduledAt,
        status: 'SNOOZED',
      );

      // Assert
      expect(takenDose.status, equals('TAKEN'));
      expect(skippedDose.status, equals('SKIPPED'));
      expect(snoozedDose.status, equals('SNOOZED'));
    });

    test('should exclude null id from JSON for database auto-increment', () {
      // Arrange - Create DoseHistory without id (for new inserts)
      final doseHistory = DoseHistory(
        userId: testUserId,
        userMedicineId: testUserMedicineId,
        scheduledAt: testScheduledAt,
        status: 'TAKEN',
      );

      // Act
      final json = doseHistory.toJson();

      // Assert - id should not be present in JSON when null
      expect(json.containsKey('id'), isFalse);
      expect(json['user_id'], equals(testUserId));
      expect(json['user_medicine_id'], equals(testUserMedicineId));
      expect(json['status'], equals('TAKEN'));
    });

    test('should include id in JSON when present (for updates)', () {
      // Arrange - Create DoseHistory with id (for updates)
      final doseHistory = DoseHistory(
        id: 42,
        userId: testUserId,
        userMedicineId: testUserMedicineId,
        scheduledAt: testScheduledAt,
        status: 'TAKEN',
      );

      // Act
      final json = doseHistory.toJson();

      // Assert - id should be present in JSON when not null
      expect(json.containsKey('id'), isTrue);
      expect(json['id'], equals(42));
      expect(json['user_id'], equals(testUserId));
      expect(json['user_medicine_id'], equals(testUserMedicineId));
      expect(json['status'], equals('TAKEN'));
    });
  });
}
