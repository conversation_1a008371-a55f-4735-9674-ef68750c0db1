import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'l10n/generated/app_localizations.dart';
import 'utils/logger.dart';
import 'utils/error_reporter.dart';

import 'core/di/injection_container.dart';
import 'core/theme/app_theme.dart';
import 'core/router/app_router.dart';
import 'core/config/environment_config.dart';
import 'core/services/supabase_service.dart';
import 'core/services/notification_service.dart';
import 'core/network/network_info.dart';
import 'presentation/bloc/auth/auth_bloc.dart';
import 'presentation/bloc/auth/auth_event.dart';
import 'presentation/bloc/dashboard/dashboard_bloc.dart';
import 'presentation/bloc/medicine/medicine_bloc.dart';
import 'presentation/bloc/location/location_bloc.dart';
import 'presentation/bloc/my_medicines/my_medicines_bloc.dart';
import 'presentation/bloc/family_manager/family_manager_bloc.dart';
import 'presentation/bloc/reminder/reminder_bloc.dart';
import 'presentation/bloc/settings/settings_bloc.dart';
import 'presentation/bloc/settings/settings_state.dart';
import 'presentation/bloc/profile_security/profile_security_bloc.dart';
import 'presentation/bloc/personalization/personalization_bloc.dart';
import 'presentation/bloc/language/language_bloc.dart';
import 'presentation/bloc/language/language_event.dart';
import 'presentation/bloc/language/language_state.dart';
import 'presentation/widgets/auth/auth_state_listener.dart';
import 'core/services/language_service.dart';

void main() async {
  // Set up comprehensive error handling before anything else
  runZonedGuarded<Future<void>>(() async {
    WidgetsFlutterBinding.ensureInitialized();

    // Initialize environment configuration first
    await EnvironmentConfig.initialize();

    // Set up Flutter framework error handling
    FlutterError.onError = (FlutterErrorDetails details) {
      AppLogger.error('Flutter framework error',
          error: details.exception, stackTrace: details.stack);

      ErrorReporter.captureUIError(
        details.exception,
        details.stack,
        widget: details.context?.toString(),
      );
    };

    // Set up platform error handling
    PlatformDispatcher.instance.onError = (error, stack) {
      AppLogger.error('Platform error', error: error, stackTrace: stack);
      ErrorReporter.captureError(error, stack, context: {'source': 'platform'});
      return true; // Handled
    };

    // Initialize dependency injection first
    await configureDependencies();

    // Initialize enhanced Supabase service
    final supabaseService = getIt<SupabaseService>();
    final networkInfo = getIt<NetworkInfo>();

    await supabaseService.initialize(networkInfo: networkInfo);

    // Initialize notification service
    final notificationService = getIt<NotificationService>();
    await notificationService.initialize();
    await notificationService.requestPermissions();

    // Test notification to verify Android system works
    if (kDebugMode) {
      await notificationService.showNotification(
        id: 999,
        title: '🧪 MedyTrack Test',
        body: 'Notification system initialized successfully!',
      );
      AppLogger.log('Test notification sent');
    }

    // Set system UI overlay style
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );

    // Set preferred orientations
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    runApp(const MedyTrackApp());
  }, (error, stack) {
    // Catch any uncaught async errors
    AppLogger.error('Uncaught async error', error: error, stackTrace: stack);
    ErrorReporter.captureError(error, stack, context: {'source': 'async_zone'});
  });
}

class MedyTrackApp extends StatelessWidget {
  const MedyTrackApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<AuthBloc>(
          create: (context) =>
              getIt<AuthBloc>()..add(AuthInitializeRequested()),
        ),
        BlocProvider<DashboardBloc>(
          create: (context) => getIt<DashboardBloc>(),
        ),
        BlocProvider<MedicineBloc>(create: (context) => getIt<MedicineBloc>()),
        BlocProvider<LocationBloc>(create: (context) => getIt<LocationBloc>()),
        BlocProvider<MyMedicinesBloc>(
            create: (context) => getIt<MyMedicinesBloc>()),
        BlocProvider<FamilyManagerBloc>(
            create: (context) => getIt<FamilyManagerBloc>()),
        BlocProvider<ReminderBloc>(create: (context) => getIt<ReminderBloc>()),
        BlocProvider<SettingsBloc>(create: (context) => getIt<SettingsBloc>()),
        BlocProvider<ProfileSecurityBloc>(
            create: (context) => getIt<ProfileSecurityBloc>()),
        BlocProvider<PersonalizationBloc>(
            create: (context) => getIt<PersonalizationBloc>()),
        BlocProvider<LanguageBloc>(
            create: (context) =>
                LanguageBloc(languageService: getIt<LanguageService>())
                  ..add(const LanguageLoadRequested())),
      ],
      child: AuthStateListener(
        child: BlocBuilder<LanguageBloc, LanguageState>(
          builder: (context, languageState) {
            Locale currentLocale = const Locale('fr', 'FR');
            if (languageState is LanguageLoaded) {
              currentLocale = languageState.locale;
              if (kDebugMode) {
                AppLogger.ui('MaterialApp: Updating locale to $currentLocale');
              }
            }

            return BlocBuilder<SettingsBloc, SettingsState>(
              builder: (context, settingsState) {
                ThemeMode currentThemeMode = ThemeMode.system;
                if (settingsState is SettingsLoaded) {
                  currentThemeMode = settingsState.settings.app.isDarkMode
                      ? ThemeMode.dark
                      : ThemeMode.light;
                  if (kDebugMode) {
                    AppLogger.ui(
                        'MaterialApp: Updating theme mode to $currentThemeMode');
                  }
                }

                return MaterialApp.router(
                  title: 'MedyTrack',
                  theme: AppTheme.lightTheme,
                  darkTheme: AppTheme.darkTheme,
                  themeMode: currentThemeMode,
                  routerConfig: AppRouter.router,
                  debugShowCheckedModeBanner: false,
                  localizationsDelegates: const [
                    AppLocalizations.delegate,
                    GlobalMaterialLocalizations.delegate,
                    GlobalWidgetsLocalizations.delegate,
                    GlobalCupertinoLocalizations.delegate,
                  ],
                  supportedLocales: const [
                    Locale('fr', 'FR'),
                    Locale('en', 'US'),
                    Locale('ar', 'TN'),
                  ],
                  locale: currentLocale,
                );
              },
            );
          },
        ),
      ),
    );
  }
}
