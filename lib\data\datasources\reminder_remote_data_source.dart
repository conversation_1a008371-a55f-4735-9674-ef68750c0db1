import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../core/error/exceptions.dart';
import '../../domain/entities/reminder.dart';
import '../models/reminder_model.dart';
import '../../utils/logger.dart';

abstract class ReminderRemoteDataSource {
  Future<List<ReminderModel>> getRemindersForUserMedicine(
      String userMedicineId);
  Future<List<ReminderModel>> getRemindersForMultipleMedicines(
      List<String> userMedicineIds);
  Future<ReminderModel> addReminder(ReminderModel reminder);
  Future<ReminderModel> updateReminder(ReminderModel reminder);
  Future<void> deleteReminder(String reminderId);
  Future<List<DoseHistory>> getDoseHistoryForUserMedicine(
      String userMedicineId);
  Future<void> addDoseHistory(DoseHistory doseHistory);
}

@LazySingleton(as: ReminderRemoteDataSource)
class ReminderRemoteDataSourceImpl implements ReminderRemoteDataSource {
  final SupabaseClient supabaseClient;

  ReminderRemoteDataSourceImpl({required this.supabaseClient});

  @override
  Future<List<ReminderModel>> getRemindersForUserMedicine(
      String userMedicineId) async {
    try {
      final response = await supabaseClient
          .from('reminders')
          .select()
          .eq('user_medicine_id', userMedicineId)
          .eq('is_active', true);

      return (response as List)
          .map((json) => ReminderModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException('Failed to get reminders: ${e.toString()}');
    }
  }

  @override
  Future<List<ReminderModel>> getRemindersForMultipleMedicines(
      List<String> userMedicineIds) async {
    try {
      if (userMedicineIds.isEmpty) {
        return [];
      }

      // Use 'in' operator to fetch reminders for multiple medicines in one query
      final response = await supabaseClient
          .from('reminders')
          .select()
          .inFilter('user_medicine_id', userMedicineIds)
          .eq('is_active', true);

      return (response as List)
          .map((json) => ReminderModel.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException(
          'Failed to get reminders for multiple medicines: ${e.toString()}');
    }
  }

  @override
  Future<ReminderModel> addReminder(ReminderModel reminder) async {
    try {
      final payload = reminder.toJson();

      // Debug logging - capture exact payload being sent
      if (kDebugMode) {
        AppLogger.database('AddReminder - Payload being sent to Supabase');
        AppLogger.database('Raw payload: $payload');
        AppLogger.database('Payload details:');
        payload.forEach((key, value) {
          AppLogger.database('  - $key: $value (${value.runtimeType})');
        });

        // Debug authentication and ownership
        final currentUser = supabaseClient.auth.currentUser;
        AppLogger.database(
            'Auth: ${currentUser?.id} | Medicine: ${payload['user_medicine_id']}');
      }

      final response = await supabaseClient
          .from('reminders')
          .insert(payload)
          .select()
          .single();

      if (kDebugMode) {
        AppLogger.database('AddReminder - Success response: $response');
      }
      return ReminderModel.fromJson(response);
    } catch (e) {
      if (kDebugMode) {
        AppLogger.error('AddReminder - Error occurred', error: e);
        AppLogger.error('AddReminder - Error type: ${e.runtimeType}');
        if (e is PostgrestException) {
          AppLogger.error('PostgrestException details:');
          AppLogger.error('  - Code: ${e.code}');
          AppLogger.error('  - Message: ${e.message}');
          AppLogger.error('  - Details: ${e.details}');
          AppLogger.error('  - Hint: ${e.hint}');
        }
      }
      throw ServerException('Failed to add reminder: ${e.toString()}');
    }
  }

  @override
  Future<ReminderModel> updateReminder(ReminderModel reminder) async {
    try {
      // Build a PATCH-safe payload:
      // - Never send immutable fields like 'id'
      // - Avoid sending created_at; set updated_at to now
      // - Ensure dates/arrays are in DB-expected formats
      final json = reminder.toJson();
      // Remove fields that commonly cause 400 on PATCH
      json.remove('id');
      json.remove('created_at');
      // Force updated_at to server time
      json['updated_at'] = DateTime.now().toIso8601String();

      // If frequency_days or specific_dates are empty lists, PostgREST can accept them
      // but if your schema forbids empty arrays, uncomment the following:
      // if ((json['frequency_days'] as List).isEmpty) json['frequency_days'] = null;
      // if ((json['specific_dates'] as List).isEmpty) json['specific_dates'] = null;

      final response = await supabaseClient
          .from('reminders')
          .update(json)
          .eq('id', reminder.id!)
          .select()
          .single();

      return ReminderModel.fromJson(response);
    } catch (e) {
      // Provide richer diagnostics when a 400 occurs
      if (e is PostgrestException) {
        final code = e.code ?? 'unknown';
        final message = e.message;
        final details = e.details ?? 'no details';
        final hint = e.hint ?? 'no hint';
        throw ServerException(
            'Failed to update reminder [PostgREST $code]: $message | details: $details | hint: $hint');
      }
      throw ServerException('Failed to update reminder: ${e.toString()}');
    }
  }

  @override
  Future<void> deleteReminder(String reminderId) async {
    try {
      await supabaseClient.from('reminders').delete().eq('id', reminderId);
    } catch (e) {
      throw ServerException('Failed to delete reminder: ${e.toString()}');
    }
  }

  @override
  Future<List<DoseHistory>> getDoseHistoryForUserMedicine(
      String userMedicineId) async {
    try {
      final response = await supabaseClient
          .from('dose_history')
          .select()
          .eq('user_medicine_id', userMedicineId)
          .order('scheduled_time', ascending: false);

      return (response as List)
          .map((json) => DoseHistory.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerException('Failed to get dose history: ${e.toString()}');
    }
  }

  @override
  Future<void> addDoseHistory(DoseHistory doseHistory) async {
    try {
      await supabaseClient.from('dose_history').insert(doseHistory.toJson());
    } catch (e) {
      throw ServerException('Failed to add dose history: ${e.toString()}');
    }
  }
}
