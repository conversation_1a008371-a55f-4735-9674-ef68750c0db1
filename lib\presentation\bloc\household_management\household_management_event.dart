import 'package:equatable/equatable.dart';

/// Base class for household management events
abstract class HouseholdManagementEvent extends Equatable {
  const HouseholdManagementEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load user's households
class LoadUserHouseholds extends HouseholdManagementEvent {
  const LoadUserHouseholds();
}

/// Event to create a new household
class CreateHousehold extends HouseholdManagementEvent {
  final String name;
  final String? description;

  const CreateHousehold({
    required this.name,
    this.description,
  });

  @override
  List<Object?> get props => [name, description];
}

/// Event to join a household using invite code
class JoinHouseholdWithCode extends HouseholdManagementEvent {
  final String inviteCode;

  const JoinHouseholdWithCode({required this.inviteCode});

  @override
  List<Object?> get props => [inviteCode];
}

/// Event to generate invite code for a household
class GenerateInviteCode extends HouseholdManagementEvent {
  final String householdId;

  const GenerateInviteCode({required this.householdId});

  @override
  List<Object?> get props => [householdId];
}

/// Event to leave a household
class LeaveHousehold extends HouseholdManagementEvent {
  final String householdId;

  const LeaveHousehold({required this.householdId});

  @override
  List<Object?> get props => [householdId];
}

/// Event to refresh household data
class RefreshHouseholds extends HouseholdManagementEvent {
  const RefreshHouseholds();
}

/// Event to validate invite code
class ValidateInviteCode extends HouseholdManagementEvent {
  final String inviteCode;

  const ValidateInviteCode({required this.inviteCode});

  @override
  List<Object?> get props => [inviteCode];
}

/// Event to load active invitations for a household
class LoadActiveInvitations extends HouseholdManagementEvent {
  final String householdId;

  const LoadActiveInvitations({required this.householdId});

  @override
  List<Object?> get props => [householdId];
}
