import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../bloc/add_medicine/add_medicine_bloc.dart';
import '../../bloc/add_medicine/add_medicine_event.dart';
import '../../bloc/add_medicine/add_medicine_state.dart';
import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/auth/auth_state.dart' as auth_state;
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/di/injection_container.dart';
import '../../../core/utils/debug_logger.dart';

/// Comprehensive debug page for testing all Add Medicine functionality
/// Tests: Location/Family dropdowns, Custom medicine, Database submission
class AddMedicineFullDebugPage extends StatefulWidget {
  const AddMedicineFullDebugPage({super.key});

  @override
  State<AddMedicineFullDebugPage> createState() =>
      _AddMedicineFullDebugPageState();
}

class _AddMedicineFullDebugPageState extends State<AddMedicineFullDebugPage> {
  final List<String> _debugLog = [];
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _addDebugLog('🚀 Full Add Medicine Debug Page initialized');
    _addDebugLog('Testing: Dropdowns, Custom Medicine, Database Submission');
  }

  void _addDebugLog(String message) {
    setState(() {
      _debugLog.add(
          '${DateTime.now().toIso8601String().substring(11, 19)}: $message');
    });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _syncWithDebugLogger() {
    // Get recent logs from DebugLogger and add them to our display
    final recentLogs = DebugLogger.getRecentLogs(10);
    for (final log in recentLogs) {
      if (!_debugLog.contains(log)) {
        setState(() {
          _debugLog.add(log);
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) {
        DebugLogger.startAudit('AddMedicineBloc Creation');

        final bloc = AddMedicineBloc(
          getIt(),
          getIt(),
          getIt(),
          getIt(),
          getIt(),
        );

        // Comprehensive auth state debugging
        DebugLogger.logAuth('Reading auth state from context');
        final authBloc = context.read<AuthBloc>();
        final authState = authBloc.state;

        DebugLogger.logAuth('Auth state type: ${authState.runtimeType}');
        DebugLogger.logAuth('Auth state details', data: {
          'isAuthenticated': authState is auth_state.AuthAuthenticated,
          'state': authState.toString(),
        });

        String householdId = '';
        if (authState is auth_state.AuthAuthenticated) {
          householdId = authState.householdId ?? '';
          DebugLogger.logAuth('Authenticated user found', data: {
            'userId': authState.user.id,
            'email': authState.user.email,
            'householdId': householdId,
            'householdName': authState.householdName,
            'householdIdIsEmpty': householdId.isEmpty,
            'householdIdLength': householdId.length,
          });
        } else {
          DebugLogger.logAuth('User not authenticated', data: {
            'stateType': authState.runtimeType.toString(),
          });
        }

        DebugLogger.logAuth('Final household ID for BLoC: "$householdId"');

        // Initialize with proper household ID and trigger data loading
        DebugLogger.logBloc(
            'Initializing AddMedicineBloc with household ID: "$householdId"');
        bloc.add(AddMedicineInitialized(householdId: householdId));

        // Trigger loading of locations and family members
        DebugLogger.logBloc('Triggering LocationsLoaded event');
        bloc.add(const LocationsLoaded());

        DebugLogger.logBloc('Triggering FamilyMembersLoaded event');
        bloc.add(const FamilyMembersLoaded());

        DebugLogger.endAudit('AddMedicineBloc Creation');
        return bloc;
      },
      child: Scaffold(
        body: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              color: AppColors.teal,
              child: Row(
                children: [
                  const Icon(Icons.bug_report, color: Colors.white, size: 24),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Add Medicine Debug',
                        style: AppTextStyles.titleLarge
                            .copyWith(color: Colors.white),
                      ),
                      Text(
                        'Full functionality testing',
                        style: AppTextStyles.bodyMedium
                            .copyWith(color: Colors.white70),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Main content
            Expanded(
              child: BlocConsumer<AddMedicineBloc, AddMedicineState>(
                listener: (context, state) {
                  // Add BLoC state changes to debug log
                  _addDebugLog('📊 BLoC State: ${state.runtimeType}');

                  if (state is AddMedicineFormState) {
                    _addDebugLog(
                        '📊 Form State Details: ${_getStateInfo(state)}');
                    if (state.availableLocations.isNotEmpty) {
                      _addDebugLog(
                          '📍 Locations in state: ${state.availableLocations.map((l) => l.displayName).join(", ")}');
                    }
                    if (state.availableFamilyMembers.isNotEmpty) {
                      _addDebugLog(
                          '👥 Family members in state: ${state.availableFamilyMembers.map((m) => m.displayName).join(", ")}');
                    }
                  } else if (state is AddMedicineSuccess) {
                    _addDebugLog('✅ SUCCESS: ${state.message}');
                  } else if (state is AddMedicineError) {
                    _addDebugLog('❌ ERROR: ${state.message}');
                  } else if (state is AddMedicineLoading) {
                    _addDebugLog('⏳ Loading state...');
                  } else if (state is AddMedicineInitial) {
                    _addDebugLog('🚀 Initial state - BLoC ready');
                  }

                  // Also sync with DebugLogger logs
                  _syncWithDebugLogger();
                },
                builder: (context, state) {
                  if (state is AddMedicineLoading) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (state is AddMedicineFormState) {
                    return _buildDebugInterface(context, state);
                  }

                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const CircularProgressIndicator(),
                        const SizedBox(height: 16),
                        Text('Initializing debug environment...',
                            style: AppTextStyles.bodyMedium),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDebugInterface(
      BuildContext context, AddMedicineFormState state) {
    return Row(
      children: [
        // Left side - Controls and tests
        Expanded(
          flex: 2,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTestSection('Location Dropdown Test', [
                  _buildLocationDropdownTest(context, state),
                ]),
                const SizedBox(height: 24),
                _buildTestSection('Family Member Dropdown Test', [
                  _buildFamilyMemberDropdownTest(context, state),
                ]),
                const SizedBox(height: 24),
                _buildTestSection('Custom Medicine Test', [
                  _buildCustomMedicineTest(context, state),
                ]),
                const SizedBox(height: 24),
                _buildTestSection('State Information', [
                  _buildStateInfo(state),
                ]),
                const SizedBox(height: 24),
                _buildTestSection('Debug Logger Controls', [
                  _buildDebugLoggerControls(),
                ]),
              ],
            ),
          ),
        ),

        // Right side - Debug log
        Expanded(
          flex: 1,
          child: Container(
            color: Colors.black87,
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  color: Colors.grey[800],
                  child: Row(
                    children: [
                      const Icon(Icons.bug_report,
                          color: Colors.greenAccent, size: 16),
                      const SizedBox(width: 8),
                      Text('Debug Log',
                          style: AppTextStyles.titleSmall
                              .copyWith(color: Colors.white)),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.clear,
                            color: Colors.white, size: 16),
                        onPressed: () => setState(() => _debugLog.clear()),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: ListView.builder(
                    controller: _scrollController,
                    itemCount: _debugLog.length,
                    itemBuilder: (context, index) {
                      return Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 2),
                        child: Text(
                          _debugLog[index],
                          style: const TextStyle(
                            color: Colors.greenAccent,
                            fontFamily: 'monospace',
                            fontSize: 11,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTestSection(String title, List<Widget> children) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(title, style: AppTextStyles.titleMedium),
            const SizedBox(height: 12),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildLocationDropdownTest(
      BuildContext context, AddMedicineFormState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Available Locations: ${state.availableLocations.length}',
            style: AppTextStyles.bodySmall),
        Text('Selected: ${state.selectedLocation?.displayName ?? "None"}',
            style: AppTextStyles.bodySmall),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          initialValue: state.selectedLocation?.id,
          decoration: const InputDecoration(
            labelText: 'Test Location Dropdown',
            border: OutlineInputBorder(),
          ),
          items: state.availableLocations.map((location) {
            return DropdownMenuItem(
              value: location.id,
              child: Text(location.displayName),
            );
          }).toList(),
          onChanged: (value) {
            _addDebugLog('🎯 Location dropdown changed: $value');
            if (value != null) {
              final location =
                  state.availableLocations.firstWhere((l) => l.id == value);
              _addDebugLog(
                  '📍 Triggering LocationSelected event for: ${location.displayName}');
              context
                  .read<AddMedicineBloc>()
                  .add(LocationSelected(location: location));
            }
          },
        ),
        const SizedBox(height: 8),
        ElevatedButton(
          onPressed: () {
            _addDebugLog('🔄 Manually loading locations...');
            context.read<AddMedicineBloc>().add(const LocationsLoaded());
          },
          child: const Text('Reload Locations'),
        ),
      ],
    );
  }

  Widget _buildFamilyMemberDropdownTest(
      BuildContext context, AddMedicineFormState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Available Members: ${state.availableFamilyMembers.length}',
            style: AppTextStyles.bodySmall),
        Text('Selected: ${state.selectedFamilyMember?.displayName ?? "None"}',
            style: AppTextStyles.bodySmall),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          initialValue: state.selectedFamilyMember?.id,
          decoration: const InputDecoration(
            labelText: 'Test Family Member Dropdown',
            border: OutlineInputBorder(),
          ),
          items: state.availableFamilyMembers.map((member) {
            return DropdownMenuItem(
              value: member.id,
              child: Text(member.displayName),
            );
          }).toList(),
          onChanged: (value) {
            _addDebugLog('👥 Family member dropdown changed: $value');
            if (value != null) {
              final member =
                  state.availableFamilyMembers.firstWhere((m) => m.id == value);
              _addDebugLog(
                  '👤 Triggering FamilyMemberSelected event for: ${member.displayName}');
              context
                  .read<AddMedicineBloc>()
                  .add(FamilyMemberSelected(member: member));
            }
          },
        ),
        const SizedBox(height: 8),
        ElevatedButton(
          onPressed: () {
            _addDebugLog('🔄 Manually loading family members...');
            context.read<AddMedicineBloc>().add(const FamilyMembersLoaded());
          },
          child: const Text('Reload Family Members'),
        ),
      ],
    );
  }

  Widget _buildCustomMedicineTest(
      BuildContext context, AddMedicineFormState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Custom Mode: ${state.isCustomMode}',
            style: AppTextStyles.bodySmall),
        const SizedBox(height: 8),
        Row(
          children: [
            ElevatedButton(
              onPressed: () {
                _addDebugLog('🔄 Toggling custom mode...');
                context
                    .read<AddMedicineBloc>()
                    .add(ModeToggled(isCustomMode: !state.isCustomMode));
              },
              child: Text(
                  state.isCustomMode ? 'Switch to Search' : 'Switch to Custom'),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: () {
                _addDebugLog('💾 Testing custom medicine submission...');
                _testCustomMedicineSubmission(context, state);
              },
              child: const Text('Test Custom Submit'),
            ),
          ],
        ),
      ],
    );
  }

  void _testCustomMedicineSubmission(
      BuildContext context, AddMedicineFormState state) {
    // Set up test data for custom medicine
    context.read<AddMedicineBloc>().add(const FormFieldUpdated(
        field: 'customName', value: 'Test Custom Medicine'));
    context
        .read<AddMedicineBloc>()
        .add(const FormFieldUpdated(field: 'dosage', value: '500mg'));
    context
        .read<AddMedicineBloc>()
        .add(const FormFieldUpdated(field: 'form', value: 'Comprimé'));
    context.read<AddMedicineBloc>().add(FormFieldUpdated(
        field: 'expiration',
        value: DateTime.now().add(const Duration(days: 365))));
    context
        .read<AddMedicineBloc>()
        .add(const FormFieldUpdated(field: 'quantity', value: 1));
    context
        .read<AddMedicineBloc>()
        .add(const FormFieldUpdated(field: 'lowStockThreshold', value: 0));

    _addDebugLog('📝 Custom medicine data set, triggering submission...');
    context.read<AddMedicineBloc>().add(MedicineSubmitted(context: context));
  }

  Widget _buildStateInfo(AddMedicineFormState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Form Valid: ${state.isValid}', style: AppTextStyles.bodySmall),
        Text('Submitting: ${state.isSubmitting}',
            style: AppTextStyles.bodySmall),
        Text('Custom Mode: ${state.isCustomMode}',
            style: AppTextStyles.bodySmall),
        Text('Selected Medicine: ${state.selectedMedicine?.nom ?? "None"}',
            style: AppTextStyles.bodySmall),
        Text('Form Errors: ${state.formErrors.length}',
            style: AppTextStyles.bodySmall),
        if (state.formErrors.isNotEmpty) ...[
          const SizedBox(height: 4),
          ...state.formErrors.entries.map((entry) => Text(
              '  ${entry.key}: ${entry.value}',
              style: AppTextStyles.bodySmall.copyWith(color: Colors.red))),
        ],
      ],
    );
  }

  String _getStateInfo(AddMedicineFormState state) {
    return 'Locations: ${state.availableLocations.length}, '
        'Members: ${state.availableFamilyMembers.length}, '
        'Selected Location: ${state.selectedLocation?.displayName ?? "None"}, '
        'Selected Member: ${state.selectedFamilyMember?.displayName ?? "None"}, '
        'Valid: ${state.isValid}';
  }

  Widget _buildDebugLoggerControls() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Total Logs: ${DebugLogger.logs.length}',
            style: AppTextStyles.bodySmall),
        Text('Error Logs: ${DebugLogger.getErrorLogs().length}',
            style: AppTextStyles.bodySmall),
        const SizedBox(height: 8),
        Row(
          children: [
            ElevatedButton(
              onPressed: () {
                _addDebugLog('🔄 Syncing with DebugLogger...');
                _syncWithDebugLogger();
              },
              child: const Text('Sync Logs'),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: () {
                DebugLogger.clearLogs();
                setState(() {
                  _debugLog.clear();
                });
                _addDebugLog('🧹 All logs cleared');
              },
              child: const Text('Clear All Logs'),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: () {
                _addDebugLog('📋 Exporting logs...');
                final logs = DebugLogger.exportLogs();
                _addDebugLog(
                    '📋 Exported ${logs.split('\n').length} log entries');
                // In a real app, you might save to file or copy to clipboard
              },
              child: const Text('Export Logs'),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            ElevatedButton(
              onPressed: () {
                _addDebugLog('🔍 Showing error logs only...');
                final errorLogs = DebugLogger.getErrorLogs();
                setState(() {
                  _debugLog.clear();
                  _debugLog.addAll(errorLogs);
                });
              },
              child: const Text('Show Errors Only'),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: () {
                _addDebugLog('🔍 Showing auth logs only...');
                final authLogs = DebugLogger.getLogsByCategory('AUTH');
                setState(() {
                  _debugLog.clear();
                  _debugLog.addAll(authLogs);
                });
              },
              child: const Text('Show Auth Logs'),
            ),
          ],
        ),
      ],
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}
