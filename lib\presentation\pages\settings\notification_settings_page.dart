import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/services/notification_service.dart';
import '../../../core/services/medicine_reminder_service.dart';
import '../../../core/di/injection_container.dart';
import '../../../l10n/generated/app_localizations.dart';
import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/auth/auth_state.dart' as auth_state;
import '../../bloc/settings/settings_bloc.dart';
import '../../bloc/settings/settings_event.dart';
import '../../bloc/settings/settings_state.dart';
import '../../widgets/settings/settings_section.dart';
import '../../widgets/settings/settings_tile.dart';

class NotificationSettingsPage extends StatefulWidget {
  const NotificationSettingsPage({super.key});

  @override
  State<NotificationSettingsPage> createState() =>
      _NotificationSettingsPageState();
}

class _NotificationSettingsPageState extends State<NotificationSettingsPage> {
  final NotificationService _notificationService = getIt<NotificationService>();
  final MedicineReminderService _reminderService =
      getIt<MedicineReminderService>();

  @override
  void initState() {
    super.initState();
    // Initialize notification service
    _initializeNotifications();

    // Load settings when page initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authState = context.read<AuthBloc>().state;
      if (authState is auth_state.AuthAuthenticated) {
        context.read<SettingsBloc>().add(
              SettingsLoadRequested(userId: authState.user.id),
            );
      }
    });
  }

  Future<void> _initializeNotifications() async {
    await _notificationService.initialize();
    await _notificationService.requestPermissions();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.teal,
      body: BlocBuilder<SettingsBloc, SettingsState>(
        builder: (context, state) {
          if (state is SettingsLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is! SettingsLoaded) {
            return const Center(child: Text('Erreur de chargement'));
          }

          return Column(
            children: [
              // Header with teal background
              Container(
                padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
                child: Row(
                  children: [
                    GestureDetector(
                      onTap: () => context.pop(),
                      child: Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.arrow_back,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        'Notifications',
                        style: AppTextStyles.headlineMedium.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Content container with white background
              Expanded(
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(24),
                      topRight: Radius.circular(24),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 8,
                        offset: const Offset(0, -4),
                      ),
                    ],
                  ),
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildReminderNotificationsSection(state),
                        const SizedBox(height: 24),
                        _buildSnoozeConfigurationSection(state),
                        const SizedBox(height: 24),
                        _buildExpiryNotificationsSection(state),
                        const SizedBox(height: 24),
                        _buildLowStockNotificationsSection(state),
                        const SizedBox(height: 24),
                        _buildEmailNotificationsSection(state),
                        const SizedBox(height: 32),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  LinearGradient _getHeaderGradient() {
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        AppColors.teal,
        AppColors.navy,
      ],
    );
  }

  Widget _buildIntegratedHeaderCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              GestureDetector(
                onTap: () => Navigator.pop(context),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppColors.navy.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.arrow_back,
                    color: AppColors.navy,
                    size: 24,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    AppLocalizations.of(context).notificationSettings,
                    style: AppTextStyles.headlineSmall.copyWith(
                      color: AppColors.navy,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'Gérer vos alertes et rappels',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.grey600,
                    ),
                  ),
                ],
              ),
            ],
          ),
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: AppColors.teal.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(
              Icons.notifications_outlined,
              color: AppColors.teal,
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReminderNotificationsSection(SettingsLoaded state) {
    return SettingsSection(
      title: '💊 Rappels de médicaments',
      children: [
        SettingsTile(
          icon: Icons.medication_outlined,
          title: 'Rappels de prise',
          subtitle: state.settings.notifications.medicationReminders
              ? 'Activés'
              : 'Désactivés',
          trailing: Switch(
            value: state.settings.notifications.medicationReminders,
            onChanged: (value) {
              context.read<SettingsBloc>().add(
                    NotificationSettingsUpdateRequested(
                      userId: state.settings.userId,
                      settings: state.settings.notifications.copyWith(
                        medicationReminders: value,
                      ),
                    ),
                  );
            },
            activeThumbColor: AppColors.teal,
          ),
        ),
        SettingsTile(
          icon: Icons.volume_up_outlined,
          title: 'Son des notifications',
          subtitle: 'Activer le son pour les rappels',
          trailing: Switch(
            value: true, // Default to true for now
            onChanged: (value) {
              // TODO: Implement sound setting when added to Settings entity
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    value ? 'Son activé' : 'Son désactivé',
                  ),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
            activeThumbColor: AppColors.teal,
          ),
        ),
        SettingsTile(
          icon: Icons.vibration_outlined,
          title: 'Vibration',
          subtitle: 'Vibrer lors des rappels',
          trailing: Switch(
            value: false, // Default to false for now
            onChanged: (value) {
              // TODO: Implement vibration setting when added to Settings entity
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    value ? 'Vibration activée' : 'Vibration désactivée',
                  ),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
            activeThumbColor: AppColors.teal,
          ),
        ),
      ],
    );
  }

  Widget _buildSnoozeConfigurationSection(SettingsLoaded state) {
    // Available snooze durations (from enhanced_reminder_notification_service.dart)
    final snoozeDurations = [
      {'duration': const Duration(minutes: 5), 'label': '5 minutes'},
      {'duration': const Duration(minutes: 15), 'label': '15 minutes'},
      {'duration': const Duration(minutes: 30), 'label': '30 minutes'},
      {'duration': const Duration(hours: 1), 'label': '1 heure'},
    ];

    return SettingsSection(
      title: '⏰ Configuration du report (Snooze)',
      children: [
        SettingsTile(
          icon: Icons.snooze_outlined,
          title: 'Options de report disponibles',
          subtitle: 'Intervalles configurés pour reporter les rappels',
          trailing: Icon(
            Icons.info_outline,
            color: AppColors.teal,
            size: 20,
          ),
          onTap: () => _showSnoozeInfoDialog(),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.grey50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.grey200),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Intervalles de report disponibles:',
                style: AppTextStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.grey700,
                ),
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: snoozeDurations.map((snooze) {
                  return Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AppColors.teal.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                          color: AppColors.teal.withValues(alpha: 0.3)),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.timer_outlined,
                          size: 16,
                          color: AppColors.teal,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          snooze['label'] as String,
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.teal,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildExpiryNotificationsSection(SettingsLoaded state) {
    final l10n = AppLocalizations.of(context);

    return SettingsSection(
      title: '⏰ ${l10n.expiryAlerts}',
      children: [
        SettingsTile(
          icon: Icons.schedule_outlined,
          title: l10n.expiryAlerts,
          subtitle: state.settings.notifications.expiryAlerts
              ? l10n.activated
              : l10n.deactivated,
          trailing: Switch(
            value: state.settings.notifications.expiryAlerts,
            onChanged: (value) {
              context.read<SettingsBloc>().add(
                    NotificationSettingsUpdateRequested(
                      userId: state.settings.userId,
                      settings: state.settings.notifications.copyWith(
                        expiryAlerts: value,
                      ),
                    ),
                  );
            },
          ),
        ),
        SettingsTile(
          icon: Icons.timer_outlined,
          title: 'Seuil d\'alerte',
          subtitle:
              '${state.settings.notifications.expiryWarningDays} jours avant expiration',
          onTap: () => _showExpiryThresholdDialog(context, state),
        ),
        SettingsTile(
          icon: Icons.access_time_outlined,
          title: 'Seuil d\'alerte (jours)',
          subtitle: '${state.settings.notifications.expiryWarningDays} jours',
          onTap: () => _showExpiryThresholdDialog(context, state),
        ),
      ],
    );
  }

  Widget _buildLowStockNotificationsSection(SettingsLoaded state) {
    return SettingsSection(
      title: '📦 Notifications de stock faible',
      children: [
        SettingsTile(
          icon: Icons.inventory_2_outlined,
          title: 'Alertes de stock faible',
          subtitle: state.settings.notifications.lowStockAlerts
              ? 'Activées'
              : 'Désactivées',
          trailing: Switch(
            value: state.settings.notifications.lowStockAlerts,
            onChanged: (value) {
              context.read<SettingsBloc>().add(
                    NotificationSettingsUpdateRequested(
                      userId: state.settings.userId,
                      settings: state.settings.notifications.copyWith(
                        lowStockAlerts: value,
                      ),
                    ),
                  );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEmailNotificationsSection(SettingsLoaded state) {
    final l10n = AppLocalizations.of(context);

    return SettingsSection(
      title: '📧 Notifications par email',
      children: [
        SettingsTile(
          icon: Icons.email_outlined,
          title: 'Notifications par email',
          subtitle: state.settings.notifications.emailNotifications
              ? 'Activées'
              : 'Désactivées',
          trailing: Switch(
            value: state.settings.notifications.emailNotifications,
            onChanged: (value) {
              context.read<SettingsBloc>().add(
                    NotificationSettingsUpdateRequested(
                      userId: state.settings.userId,
                      settings: state.settings.notifications.copyWith(
                        emailNotifications: value,
                      ),
                    ),
                  );
            },
          ),
        ),
        SettingsTile(
          icon: Icons.notifications_outlined,
          title: 'Notifications push',
          subtitle: state.settings.notifications.pushNotifications
              ? 'Activées'
              : 'Désactivées',
          trailing: Switch(
            value: state.settings.notifications.pushNotifications,
            onChanged: (value) {
              context.read<SettingsBloc>().add(
                    NotificationSettingsUpdateRequested(
                      userId: state.settings.userId,
                      settings: state.settings.notifications.copyWith(
                        pushNotifications: value,
                      ),
                    ),
                  );
            },
          ),
        ),
        SettingsTile(
          icon: Icons.bug_report_outlined,
          title: l10n.testNotifications,
          subtitle: l10n.sendTestNotification,
          trailing: ElevatedButton(
            onPressed: () => _showTestNotification(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.teal,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
            child: Text(l10n.test),
          ),
        ),
      ],
    );
  }

  /// Show a test notification
  Future<void> _showTestNotification() async {
    try {
      await _reminderService.showTestNotification();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Notification de test envoyée!'),
            backgroundColor: AppColors.teal,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: $e'),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  String _formatTime(TimeOfDay time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  String _getReminderFrequencyText(String frequency) {
    switch (frequency) {
      case 'daily':
        return 'Quotidien';
      case 'weekly':
        return 'Hebdomadaire';
      case 'monthly':
        return 'Mensuel';
      default:
        return 'Quotidien';
    }
  }

  void _showExpiryThresholdDialog(BuildContext context, SettingsLoaded state) {
    double currentValue =
        state.settings.notifications.expiryWarningDays.toDouble();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Seuil d\'alerte d\'expiration'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Recevoir une alerte ${currentValue.round()} jours avant l\'expiration',
                style: AppTextStyles.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  activeTrackColor: AppColors.teal,
                  thumbColor: AppColors.teal,
                  overlayColor: AppColors.teal.withValues(alpha: 0.2),
                ),
                child: Slider(
                  value: currentValue,
                  min: 1,
                  max: 90,
                  divisions: 89,
                  onChanged: (value) {
                    setState(() {
                      currentValue = value;
                    });
                  },
                ),
              ),
              Text(
                'De 1 à 90 jours',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.grey600,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () {
                context.read<SettingsBloc>().add(
                      NotificationSettingsUpdateRequested(
                        userId: state.settings.userId,
                        settings: state.settings.notifications.copyWith(
                          expiryWarningDays: currentValue.round(),
                        ),
                      ),
                    );
                Navigator.pop(context);
              },
              child: const Text('Enregistrer'),
            ),
          ],
        ),
      ),
    );
  }

  void _showLowStockThresholdDialog(
      BuildContext context, SettingsLoaded state) {
    double currentValue =
        state.settings.notifications.lowStockThreshold.toDouble();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Seuil de stock faible'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Recevoir une alerte quand il reste ${currentValue.round()} unités ou moins',
                style: AppTextStyles.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  activeTrackColor: AppColors.teal,
                  thumbColor: AppColors.teal,
                  overlayColor: AppColors.teal.withValues(alpha: 0.2),
                ),
                child: Slider(
                  value: currentValue,
                  min: 0,
                  max: 20,
                  divisions: 20,
                  onChanged: (value) {
                    setState(() {
                      currentValue = value;
                    });
                  },
                ),
              ),
              Text(
                'De 0 à 20 unités',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.grey600,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () {
                context.read<SettingsBloc>().add(
                      NotificationSettingsUpdateRequested(
                        userId: state.settings.userId,
                        settings: state.settings.notifications.copyWith(
                          lowStockThreshold: currentValue.round(),
                        ),
                      ),
                    );
                Navigator.pop(context);
              },
              child: const Text('Enregistrer'),
            ),
          ],
        ),
      ),
    );
  }

  void _showReminderTimeDialog(BuildContext context, SettingsLoaded state) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Heure des rappels'),
        content: const Text(
            'Cette fonctionnalité sera disponible dans une prochaine version.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showReminderFrequencyDialog(
      BuildContext context, SettingsLoaded state) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Fréquence des rappels'),
        content: const Text(
            'Cette fonctionnalité sera disponible dans une prochaine version.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showSnoozeInfoDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        icon: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.teal.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.snooze_outlined,
            color: AppColors.teal,
            size: 32,
          ),
        ),
        title: Text(
          'Configuration du report (Snooze)',
          style: AppTextStyles.headlineSmall.copyWith(
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Les intervalles de report suivants sont disponibles lors des rappels de médicaments:',
              style: AppTextStyles.bodyMedium,
            ),
            const SizedBox(height: 16),
            _buildSnoozeOptionItem(
                '5 minutes', 'Report court pour une prise immédiate'),
            _buildSnoozeOptionItem(
                '15 minutes', 'Report standard pour finir une activité'),
            _buildSnoozeOptionItem(
                '30 minutes', 'Report moyen pour une pause repas'),
            _buildSnoozeOptionItem(
                '1 heure', 'Report long pour une activité importante'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.warning.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border:
                    Border.all(color: AppColors.warning.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: AppColors.warning,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Ces intervalles sont configurés par défaut et optimisés pour une gestion efficace des rappels.',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.warning,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Compris',
              style: TextStyle(color: AppColors.teal),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSnoozeOptionItem(String duration, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: AppColors.teal,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  duration,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.teal,
                  ),
                ),
                Text(
                  description,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.grey600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
