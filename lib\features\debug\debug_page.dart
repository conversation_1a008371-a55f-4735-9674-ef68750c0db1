import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:device_info_plus/device_info_plus.dart';
import '../../utils/logger.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/utils/debug_logger.dart';

/// Debug page for testing error tracking and viewing system information
/// Only accessible in debug builds through Settings page
class DebugPage extends StatefulWidget {
  const DebugPage({super.key});

  @override
  State<DebugPage> createState() => _DebugPageState();
}

class _DebugPageState extends State<DebugPage> {
  String _systemInfo = 'Loading system information...';
  List<String> _errorLogs = [];
  bool _isLoadingSystemInfo = true;

  @override
  void initState() {
    super.initState();
    _loadSystemInfo();
    _loadErrorLogs();
  }

  Future<void> _loadSystemInfo() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      String info = '';

      if (kIsWeb) {
        final webInfo = await deviceInfo.webBrowserInfo;
        info = '''
Platform: Web
Browser: ${webInfo.browserName}
Version: ${webInfo.appVersion}
User Agent: ${webInfo.userAgent}
''';
      } else if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        info = '''
Platform: Android
Brand: ${androidInfo.brand}
Model: ${androidInfo.model}
Version: ${androidInfo.version.release}
SDK: ${androidInfo.version.sdkInt}
Manufacturer: ${androidInfo.manufacturer}
''';
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        info = '''
Platform: iOS
Name: ${iosInfo.name}
Model: ${iosInfo.model}
System Version: ${iosInfo.systemVersion}
Identifier: ${iosInfo.identifierForVendor}
''';
      } else {
        info = 'Platform: ${Platform.operatingSystem}';
      }

      setState(() {
        _systemInfo = info;
        _isLoadingSystemInfo = false;
      });
    } catch (e) {
      setState(() {
        _systemInfo = 'Error loading system info: $e';
        _isLoadingSystemInfo = false;
      });
    }
  }

  void _loadErrorLogs() {
    setState(() {
      _errorLogs = DebugLogger.getErrorLogs();
    });
  }

  Future<void> _testCrash() async {
    try {
      AppLogger.warning('User triggered test crash from debug page');

      // Trigger a test error
      throw Exception(
          'Test crash triggered from debug page - this is intentional for testing error tracking');
    } catch (e, stackTrace) {
      // This will be caught by the global error handler
      AppLogger.error('Test crash executed successfully',
          error: e, stackTrace: stackTrace);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content:
                Text('Test crash triggered! Check Supabase for error report.'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }

  Future<void> _testAsyncError() async {
    AppLogger.warning('User triggered test async error from debug page');

    // Trigger an async error that should be caught by runZonedGuarded
    Future.delayed(const Duration(milliseconds: 100), () {
      throw Exception(
          'Test async error - this should be caught by runZonedGuarded');
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Async error triggered! Check logs and Supabase.'),
          backgroundColor: Colors.blue,
        ),
      );
    }
  }

  void _clearLogs() {
    DebugLogger.clearLogs();
    _loadErrorLogs();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Debug logs cleared')),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: Column(
        children: [
          // Header with teal gradient (following MedyTrack pattern)
          Container(
            height: 120,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.teal,
                  AppColors.tealDark,
                ],
              ),
            ),
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Debug Tools',
                      style: AppTextStyles.headlineMedium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    const Icon(Icons.bug_report, color: Colors.white, size: 28),
                  ],
                ),
              ),
            ),
          ),

          // Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Test Crash Section
                  _buildSection(
                    'Error Testing',
                    Icons.warning_amber,
                    [
                      _buildActionCard(
                        'Test Crash',
                        'Trigger a test exception to verify error reporting',
                        Icons.bug_report,
                        Colors.red,
                        _testCrash,
                      ),
                      const SizedBox(height: 12),
                      _buildActionCard(
                        'Test Async Error',
                        'Trigger an async error to test runZonedGuarded',
                        Icons.timer,
                        Colors.orange,
                        _testAsyncError,
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // System Information Section
                  _buildSection(
                    'System Information',
                    Icons.info_outline,
                    [
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        child: _isLoadingSystemInfo
                            ? const Center(child: CircularProgressIndicator())
                            : Text(
                                _systemInfo,
                                style: AppTextStyles.bodyMedium.copyWith(
                                  fontFamily: 'monospace',
                                ),
                              ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Error Logs Section
                  _buildSection(
                    'Error Logs (${_errorLogs.length})',
                    Icons.list_alt,
                    [
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _loadErrorLogs,
                              icon: const Icon(Icons.refresh),
                              label: const Text('Refresh Logs'),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _clearLogs,
                              icon: const Icon(Icons.clear),
                              label: const Text('Clear Logs'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.red[100],
                                foregroundColor: Colors.red[800],
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Container(
                        height: 200,
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.black87,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: _errorLogs.isEmpty
                            ? const Center(
                                child: Text(
                                  'No error logs found',
                                  style: TextStyle(color: Colors.grey),
                                ),
                              )
                            : ListView.builder(
                                itemCount: _errorLogs.length,
                                itemBuilder: (context, index) {
                                  return Padding(
                                    padding: const EdgeInsets.only(bottom: 4),
                                    child: Text(
                                      _errorLogs[index],
                                      style: const TextStyle(
                                        color: Colors.greenAccent,
                                        fontSize: 12,
                                        fontFamily: 'monospace',
                                      ),
                                    ),
                                  );
                                },
                              ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(String title, IconData icon, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: AppColors.teal, size: 24),
            const SizedBox(width: 8),
            Text(
              title,
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.tealDark,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ...children,
      ],
    );
  }

  Widget _buildActionCard(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 2,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withValues(alpha: 0.1),
          child: Icon(icon, color: color),
        ),
        title: Text(title, style: AppTextStyles.titleMedium),
        subtitle: Text(subtitle, style: AppTextStyles.bodySmall),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }
}
