import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';

/// Category card component matching the mockup's 2x2 grid design
class CategoryCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final VoidCallback? onTap;
  final Color? iconColor;
  final Color? backgroundColor;
  final bool isSelected;

  const CategoryCard({
    super.key,
    required this.icon,
    required this.title,
    this.onTap,
    this.iconColor,
    this.backgroundColor,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor ?? Colors.white,
          borderRadius: BorderRadius.circular(16),
          border:
              isSelected ? Border.all(color: AppColors.teal, width: 2) : null,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.08),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Icon container
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  color: (iconColor ?? AppColors.teal).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  icon,
                  size: 32,
                  color: iconColor ?? AppColors.teal,
                ),
              ),

              const SizedBox(height: 12),

              // Title
              Text(
                title,
                style: AppTextStyles.titleSmall.copyWith(
                  color: AppColors.navy,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Grid layout for category cards
class CategoryGrid extends StatelessWidget {
  final List<CategoryItem> categories;
  final Function(CategoryItem)? onCategoryTap;
  final String? selectedCategoryId;

  const CategoryGrid({
    super.key,
    required this.categories,
    this.onCategoryTap,
    this.selectedCategoryId,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.0,
      ),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final category = categories[index];
        return CategoryCard(
          icon: category.icon,
          title: category.title,
          iconColor: category.color,
          isSelected: selectedCategoryId == category.id,
          onTap: () => onCategoryTap?.call(category),
        );
      },
    );
  }
}

/// Data class for category items
class CategoryItem {
  final String id;
  final String title;
  final IconData icon;
  final Color color;
  final String? description;

  const CategoryItem({
    required this.id,
    required this.title,
    required this.icon,
    required this.color,
    this.description,
  });
}

/// Predefined medicine categories matching the mockup
class MedicineCategories {
  static const List<CategoryItem> items = [
    CategoryItem(
      id: 'pills',
      title: 'Pilules',
      icon: Icons.medication,
      color: AppColors.teal,
      description: 'Comprimés et gélules',
    ),
    CategoryItem(
      id: 'respiratory',
      title: 'Respiratoire',
      icon: Icons.air,
      color: AppColors.navy,
      description: 'Inhalateurs et sprays',
    ),
    CategoryItem(
      id: 'injection',
      title: 'Injection',
      icon: Icons.colorize,
      color: AppColors.orange,
      description: 'Seringues et injections',
    ),
    CategoryItem(
      id: 'cardio',
      title: 'Cardio',
      icon: Icons.favorite,
      color: AppColors.error,
      description: 'Médicaments cardiovasculaires',
    ),
  ];
}

/// Specialist/Medicine card component matching the mockup's list design
class SpecialistCard extends StatelessWidget {
  final String name;
  final String specialty;
  final String? hospital;
  final String? avatarUrl;
  final VoidCallback? onTap;
  final Widget? actionButton;
  final bool showCheckmark;

  const SpecialistCard({
    super.key,
    required this.name,
    required this.specialty,
    this.hospital,
    this.avatarUrl,
    this.onTap,
    this.actionButton,
    this.showCheckmark = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: _buildAvatar(),
        title: Text(
          name,
          style: AppTextStyles.titleMedium.copyWith(
            color: AppColors.navy,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              specialty,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.grey600,
              ),
            ),
            if (hospital != null) ...[
              const SizedBox(height: 2),
              Text(
                hospital!,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.grey500,
                ),
              ),
            ],
          ],
        ),
        trailing: actionButton ?? _buildDefaultAction(),
        onTap: onTap,
      ),
    );
  }

  Widget _buildAvatar() {
    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        color: AppColors.tealLight,
        shape: BoxShape.circle,
        border: Border.all(
          color: AppColors.teal.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: avatarUrl != null
          ? ClipOval(
              child: Image.network(
                avatarUrl!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) =>
                    _buildDefaultAvatar(),
              ),
            )
          : _buildDefaultAvatar(),
    );
  }

  Widget _buildDefaultAvatar() {
    return Icon(
      Icons.person,
      color: AppColors.teal,
      size: 28,
    );
  }

  Widget _buildDefaultAction() {
    if (showCheckmark) {
      return Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          color: AppColors.success,
          shape: BoxShape.circle,
        ),
        child: const Icon(
          Icons.check,
          color: Colors.white,
          size: 20,
        ),
      );
    }

    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: AppColors.grey100,
        shape: BoxShape.circle,
      ),
      child: Icon(
        Icons.arrow_forward_ios,
        color: AppColors.navy,
        size: 16,
      ),
    );
  }
}

/// Medicine card specifically for medicine list
class MedicineCard extends StatelessWidget {
  final String medicineName;
  final String dosage;
  final String? frequency;
  final String? nextDose;
  final Color? statusColor;
  final VoidCallback? onTap;
  final VoidCallback? onEditTap;
  final VoidCallback? onDeleteTap;
  final bool showActions;

  // New optional fields to support standardized grid layout
  final String? expiryText; // e.g., "Expiration: 12/09/2025"
  final String? locationText; // e.g., "Lieu: Armoire Salle de bain"

  const MedicineCard({
    super.key,
    required this.medicineName,
    required this.dosage,
    this.frequency,
    this.nextDose,
    this.statusColor,
    this.onTap,
    this.onEditTap,
    this.onDeleteTap,
    this.showActions = true,
    this.expiryText,
    this.locationText,
  });

  @override
  Widget build(BuildContext context) {
    // Standardized card container: elevation 1, radius 12, 1px border
    // Dynamic background: if statusColor provided, tint background subtly like before
    final Color? bgTint =
        statusColor?.withValues(alpha: 0.06);

    return Card(
      elevation: 1,
      color: bgTint, // restore dynamic background tint
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: const BorderSide(color: AppColors.grey200, width: 1),
      ),
      margin: const EdgeInsets.only(bottom: 12),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Content first
          Material(
            type: MaterialType.transparency,
            child: InkWell(
              onTap: onTap,
              borderRadius: BorderRadius.circular(12),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: _buildGridContent(),
              ),
            ),
          ),
          // Menu button LAST to guarantee it's on top and clickable
          if (showActions && (onEditTap != null || onDeleteTap != null))
            Positioned(
              top: 0,
              right: 0,
              child: _cornerMenuButton(context),
            ),
        ],
      ),
    );
  }

  // Top-right three-dots icon per spec
  Widget _cornerMenuButton(BuildContext context) {
    // Ensure topmost hit target; use navy background per request
    return Material(
      type: MaterialType.transparency,
      child: PopupMenuButton<String>(
        tooltip: 'Options',
        splashRadius: 22,
        onSelected: (value) {
          if (value == 'edit') {
            onEditTap?.call();
          } else if (value == 'delete') {
            onDeleteTap?.call();
          }
        },
        itemBuilder: (context) => [
          if (onEditTap != null)
            PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit_outlined, size: 18, color: AppColors.grey600),
                  const SizedBox(width: 8),
                  const Text('Modifier'),
                ],
              ),
            ),
          if (onDeleteTap != null)
            PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete_outline, size: 18, color: AppColors.error),
                  const SizedBox(width: 8),
                  Text('Supprimer', style: TextStyle(color: AppColors.error)),
                ],
              ),
            ),
        ],
        offset: const Offset(0, 40),
        elevation: 8,
        color: Colors.white,
        constraints: const BoxConstraints(minWidth: 160),
        child: Container(
          width: 35,
          height: 35,
          decoration: BoxDecoration(
            color: AppColors.navy, // navy per request
            borderRadius: const BorderRadius.only(
              topRight: Radius.circular(12),
              bottomLeft: Radius.circular(12),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.08),
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          alignment: Alignment.center,
          child: const Icon(Icons.more_vert, color: Colors.white, size: 18),
        ),
      ),
    );
  }

// Ensure the popup menu is not obstructed by InkWell hit testing by placing it last in the Stack
  Widget _buildGridContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Row 1: Medicine name
        Text(
          medicineName,
          style: AppTextStyles.titleMedium.copyWith(
            color: AppColors.navy,
            fontWeight: FontWeight.bold,
            fontSize: AppTextStyles.titleMedium.fontSize! + 2,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),

        // Row 2: Dosage/form (use dosage; frequency retained separately if needed)
        Text(
          dosage,
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.black,
            fontWeight: FontWeight.normal,
            fontSize: AppTextStyles.bodySmall.fontSize! - 2,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),

        // Row 3: Expiry date (optional)
        if (expiryText != null && expiryText!.trim().isNotEmpty) ...[
          Text(
            expiryText!,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.black,
              fontWeight: FontWeight.normal,
              fontSize: AppTextStyles.bodySmall.fontSize! - 2,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),
        ] else
          const SizedBox(height: 8),

        // Row 4: Two columns -> Left: location text, Right: status badge (dynamic color retained)
        Row(
          children: [
            Expanded(
              child: Text(
                (locationText != null && locationText!.trim().isNotEmpty)
                    ? locationText!
                    : '',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.black,
                  fontWeight: FontWeight.normal,
                  fontSize: AppTextStyles.bodySmall.fontSize! - 2,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(width: 12),
            if (statusColor != null)
              _buildStatusPill(statusColor!)
            else
              const SizedBox.shrink(),
          ],
        ),

        // Optional legacy info (frequency, next dose) retained below if provided
        // Remove unwanted "Selon prescription" line and any legacy frequency display
        // Keep nextDose only if provided explicitly
        if (nextDose != null && nextDose!.trim().isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(
            'Prochaine prise: $nextDose',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.teal,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }

  Widget _buildStatusPill(Color color) {
    // Derive readable status label from color if possible. If not, display empty to avoid "Status" placeholder.
    String label = '';
    if (color == AppColors.error) {
      label = 'Expiré';
    } else if (color == AppColors.warning) {
      label = 'Stock faible';
    } else if (color == AppColors.info) {
      label = 'Expire bientôt';
    } else if (color == AppColors.success) {
      label = 'Valide';
    } else if (color == AppColors.grey600) {
      label = 'Rupture';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        label,
        style: AppTextStyles.labelSmall.copyWith(color: color),
      ),
    );
  }
}
