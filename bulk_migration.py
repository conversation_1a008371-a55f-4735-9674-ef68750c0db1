#!/usr/bin/env python3
"""
MedyTrack Database Bulk Migration Script
Migrates data from Production to Development environment using Supabase API
"""

import requests
import json
import time
from typing import List, Dict, Any

class SupabaseMigrator:
    def __init__(self, prod_project_id: str, dev_project_id: str, api_key: str):
        self.prod_project_id = prod_project_id
        self.dev_project_id = dev_project_id
        self.api_key = api_key
        self.base_url = "https://api.supabase.com/v1/projects"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def execute_query(self, project_id: str, query: str) -> List[Dict[Any, Any]]:
        """Execute a SQL query on the specified project"""
        url = f"{self.base_url}/{project_id}/database/query"
        payload = {"query": query}
        
        response = requests.post(url, headers=self.headers, json=payload)
        if response.status_code != 200:
            raise Exception(f"Query failed: {response.text}")
        
        return response.json()
    
    def get_table_data(self, table_name: str, limit: int = 1000, offset: int = 0) -> List[Dict]:
        """Get data from a table in production"""
        query = f"SELECT * FROM {table_name} LIMIT {limit} OFFSET {offset};"
        return self.execute_query(self.prod_project_id, query)
    
    def insert_batch_data(self, table_name: str, data: List[Dict], batch_size: int = 100):
        """Insert data in batches to development database"""
        if not data:
            return
        
        # Get column names from first record
        columns = list(data[0].keys())
        column_list = ", ".join(columns)
        
        for i in range(0, len(data), batch_size):
            batch = data[i:i + batch_size]
            
            # Create VALUES clause
            values_list = []
            for record in batch:
                values = []
                for col in columns:
                    value = record[col]
                    if value is None:
                        values.append("NULL")
                    elif isinstance(value, str):
                        # Escape single quotes
                        escaped_value = value.replace("'", "''")
                        values.append(f"'{escaped_value}'")
                    elif isinstance(value, (list, dict)):
                        # Handle JSON/array types
                        json_str = json.dumps(value).replace("'", "''")
                        values.append(f"'{json_str}'")
                    else:
                        values.append(str(value))
                
                values_list.append(f"({', '.join(values)})")
            
            values_clause = ", ".join(values_list)
            
            # Create INSERT query
            insert_query = f"""
            INSERT INTO {table_name} ({column_list}) 
            VALUES {values_clause}
            ON CONFLICT DO NOTHING;
            """
            
            try:
                self.execute_query(self.dev_project_id, insert_query)
                print(f"Inserted batch {i//batch_size + 1} for {table_name}")
                time.sleep(0.1)  # Rate limiting
            except Exception as e:
                print(f"Error inserting batch for {table_name}: {e}")
                # Continue with next batch
    
    def migrate_table(self, table_name: str, batch_size: int = 1000):
        """Migrate entire table from prod to dev"""
        print(f"Starting migration for {table_name}...")
        
        offset = 0
        total_migrated = 0
        
        while True:
            try:
                data = self.get_table_data(table_name, limit=batch_size, offset=offset)
                
                if not data:
                    break
                
                self.insert_batch_data(table_name, data, batch_size=100)
                
                total_migrated += len(data)
                offset += batch_size
                
                print(f"Migrated {total_migrated} records for {table_name}")
                
                if len(data) < batch_size:
                    break
                    
            except Exception as e:
                print(f"Error migrating {table_name}: {e}")
                break
        
        print(f"Completed migration for {table_name}: {total_migrated} records")
    
    def verify_migration(self):
        """Verify migration by comparing record counts"""
        tables = [
            'public.tunisia_medicines',
            'public.specialties', 
            'public.presentations',
            'public.tags',
            'auth.users',
            'public.users',
            'public.profiles',
            'public.households',
            'public.household_members',
            'public.families',
            'public.family_members',
            'public.locations',
            'public.user_medicines',
            'public.medicine_tags',
            'public.reminders',
            'public.dose_history',
            'public.app_errors'
        ]
        
        print("\n=== MIGRATION VERIFICATION ===")
        print(f"{'Table':<30} {'Prod':<10} {'Dev':<10} {'Status':<10}")
        print("-" * 60)
        
        for table in tables:
            try:
                prod_count = self.execute_query(self.prod_project_id, f"SELECT COUNT(*) as count FROM {table}")[0]['count']
                dev_count = self.execute_query(self.dev_project_id, f"SELECT COUNT(*) as count FROM {table}")[0]['count']
                
                status = "✅ OK" if prod_count == dev_count else "❌ DIFF"
                print(f"{table:<30} {prod_count:<10} {dev_count:<10} {status:<10}")
                
            except Exception as e:
                print(f"{table:<30} {'ERROR':<10} {'ERROR':<10} {'❌ ERR':<10}")

def main():
    # Configuration
    PROD_PROJECT_ID = "wzzykbnebhyvdoagpwvk"
    DEV_PROJECT_ID = "sppqqjqbvlsbjovsvdgb"
    API_KEY = "your_supabase_api_key_here"  # Replace with actual API key
    
    migrator = SupabaseMigrator(PROD_PROJECT_ID, DEV_PROJECT_ID, API_KEY)
    
    # Migration order (dependency-based)
    migration_order = [
        # Reference data first
        'public.specialties',  # Complete missing records
        'public.presentations',
        'public.tags',
        
        # Auth data
        'auth.users',
        'auth.identities',
        
        # User data
        'public.users',
        'public.profiles',
        
        # Household structure
        'public.households',
        'public.household_members',
        'public.families',
        'public.family_members',
        'public.locations',
        
        # Medicine data
        'public.user_medicines',
        'public.medicine_tags',
        'public.reminders',
        'public.dose_history',
        
        # System data
        'public.app_errors',
        'auth.sessions',
        'auth.refresh_tokens'
    ]
    
    print("Starting bulk migration...")
    
    for table in migration_order:
        migrator.migrate_table(table)
        time.sleep(1)  # Brief pause between tables
    
    # Verify migration
    migrator.verify_migration()
    
    print("\nBulk migration completed!")

if __name__ == "__main__":
    main()
