name: 🧪 Continuous Integration

on:
  push:
    branches: [ develop, main ]
  pull_request:
    branches: [ develop, main ]
    types: [opened, synchronize, reopened, ready_for_review]

# Prevent concurrent runs on the same branch
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  FLUTTER_VERSION: '3.24.3'
  NODE_VERSION: '18'

jobs:
  # ============================================================================
  # SECURITY AND VALIDATION CHECKS
  # ============================================================================
  security-checks:
    name: 🔒 Security & Validation
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Full history for security scanning

      - name: 🔍 Check for secrets in code
        run: |
          echo "🔍 Scanning for potential secrets..."
          
          # Check for common secret patterns
          if grep -r -E "(password|secret|key|token)\s*[:=]\s*['\"][^'\"]{10,}" . --exclude-dir=.git --exclude-dir=node_modules --exclude="*.md" --exclude="*.template"; then
            echo "❌ Potential secrets found in code!"
            exit 1
          fi
          
          # Check for Supabase URLs/keys in committed files
          if grep -r -E "https://[a-z0-9]+\.supabase\.co" . --exclude-dir=.git --exclude-dir=node_modules --exclude="*.md" --exclude="*.template" --exclude="*.example"; then
            echo "❌ Supabase URLs found in committed files!"
            exit 1
          fi
          
          echo "✅ No secrets detected in code"

      - name: 🚫 Verify .env files are not committed
        run: |
          echo "🔍 Checking for committed environment files..."
          
          if find . -name ".env*" -not -name "*.template" -not -name "*.example" -not -path "./.git/*" | grep -q .; then
            echo "❌ Environment files found in repository!"
            find . -name ".env*" -not -name "*.template" -not -name "*.example" -not -path "./.git/*"
            exit 1
          fi
          
          echo "✅ No environment files committed"

      - name: 📋 Validate CHANGELOG.md
        if: github.event_name == 'pull_request'
        run: |
          echo "📋 Validating CHANGELOG.md updates..."
          
          # Check if CHANGELOG.md was modified in this PR
          if git diff --name-only origin/${{ github.base_ref }}...HEAD | grep -q "CHANGELOG.md"; then
            echo "✅ CHANGELOG.md updated in this PR"
          else
            echo "⚠️ CHANGELOG.md not updated - consider adding release notes"
            # Don't fail for now, just warn
          fi

  # ============================================================================
  # CODE QUALITY AND LINTING
  # ============================================================================
  code-quality:
    name: 🧹 Code Quality
    runs-on: ubuntu-latest
    needs: security-checks
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🎯 Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true

      - name: 📦 Get dependencies
        run: flutter pub get

      - name: 🔍 Analyze code
        run: |
          echo "🔍 Running Flutter analyzer..."
          flutter analyze --fatal-infos --fatal-warnings
          
          if [ $? -eq 0 ]; then
            echo "✅ Code analysis passed"
          else
            echo "❌ Code analysis failed"
            exit 1
          fi

      - name: 🎨 Check code formatting
        run: |
          echo "🎨 Checking code formatting..."
          flutter format --set-exit-if-changed .
          
          if [ $? -eq 0 ]; then
            echo "✅ Code formatting is correct"
          else
            echo "❌ Code formatting issues found"
            echo "Run 'flutter format .' to fix formatting"
            exit 1
          fi

      - name: 🔍 Check for debug statements
        run: |
          echo "🔍 Scanning for debug statements..."
          
          # Check for print() statements (should use AppLogger)
          if grep -r "print(" lib/ --include="*.dart" | grep -v "AppLogger" | grep -v "debugPrint"; then
            echo "❌ Raw print() statements found! Use AppLogger instead."
            exit 1
          fi
          
          # Check for debugPrint without kDebugMode
          if grep -r "debugPrint" lib/ --include="*.dart" | grep -v "kDebugMode"; then
            echo "❌ debugPrint without kDebugMode check found!"
            exit 1
          fi
          
          echo "✅ No problematic debug statements found"

  # ============================================================================
  # AUTOMATED TESTING
  # ============================================================================
  unit-tests:
    name: 🧪 Unit Tests
    runs-on: ubuntu-latest
    needs: code-quality
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🎯 Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true

      - name: 📦 Get dependencies
        run: flutter pub get

      - name: 🧪 Run unit tests
        run: |
          echo "🧪 Running unit tests..."
          flutter test --coverage --reporter=expanded
          
          if [ $? -eq 0 ]; then
            echo "✅ All unit tests passed"
          else
            echo "❌ Unit tests failed"
            exit 1
          fi

      - name: 📊 Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

      - name: 🎯 Verify critical test coverage
        run: |
          echo "🎯 Checking critical test coverage..."
          
          # Check if medicine location tests exist and pass
          if flutter test test/medicine_location_rendering_test.dart; then
            echo "✅ Medicine location tests passed"
          else
            echo "❌ Critical medicine location tests failed"
            exit 1
          fi
          
          # Check if widget tests exist and pass
          if flutter test test/widget/medicine_location_widget_test.dart; then
            echo "✅ Medicine location widget tests passed"
          else
            echo "❌ Critical widget tests failed"
            exit 1
          fi

  # ============================================================================
  # INTEGRATION TESTS
  # ============================================================================
  integration-tests:
    name: 🔗 Integration Tests
    runs-on: ubuntu-latest
    needs: unit-tests
    if: github.event_name == 'pull_request' && github.base_ref == 'main'
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🎯 Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true

      - name: 📦 Get dependencies
        run: flutter pub get

      - name: 🔗 Run integration tests
        run: |
          echo "🔗 Running integration tests..."
          
          # Run existing integration tests
          if [ -d "test/integration" ]; then
            flutter test test/integration/
          else
            echo "⚠️ No integration tests found"
          fi

  # ============================================================================
  # BUILD VERIFICATION
  # ============================================================================
  build-verification:
    name: 🏗️ Build Verification
    runs-on: ubuntu-latest
    needs: code-quality
    strategy:
      matrix:
        platform: [web, android]
        environment: [development, production]
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🎯 Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          channel: 'stable'
          cache: true

      - name: 🤖 Setup Android SDK (for Android builds)
        if: matrix.platform == 'android'
        uses: android-actions/setup-android@v3

      - name: 📦 Get dependencies
        run: flutter pub get

      - name: 🏗️ Build ${{ matrix.platform }} (${{ matrix.environment }})
        run: |
          echo "🏗️ Building ${{ matrix.platform }} for ${{ matrix.environment }}..."
          
          # Set environment file based on matrix
          if [ "${{ matrix.environment }}" == "production" ]; then
            ENV_FILE=".env.prod"
          else
            ENV_FILE=".env.dev"
          fi
          
          # Create environment file from secrets for CI
          if [ "${{ matrix.environment }}" == "production" ]; then
            echo "Creating production environment file from secrets..."
            cat > .env.prod << EOF
          SUPABASE_URL=${{ secrets.PROD_SUPABASE_URL }}
          SUPABASE_ANON_KEY=${{ secrets.PROD_SUPABASE_ANON_KEY }}
          APP_NAME=MedyTrack Mobile
          APP_VERSION=$(grep "^version:" pubspec.yaml | cut -d' ' -f2 | cut -d'+' -f1)
          APP_BUILD_NUMBER=$(grep "^version:" pubspec.yaml | cut -d' ' -f2 | cut -d'+' -f2)
          ENABLE_DEBUG_PAGE=false
          ENABLE_VERBOSE_LOGGING=false
          EOF
          else
            echo "Creating development environment file from secrets..."
            cat > .env.dev << EOF
          SUPABASE_URL=${{ secrets.DEV_SUPABASE_URL }}
          SUPABASE_ANON_KEY=${{ secrets.DEV_SUPABASE_ANON_KEY }}
          APP_NAME=MedyTrack Mobile (Dev)
          APP_VERSION=$(grep "^version:" pubspec.yaml | cut -d' ' -f2 | cut -d'+' -f1)
          APP_BUILD_NUMBER=$(grep "^version:" pubspec.yaml | cut -d' ' -f2 | cut -d'+' -f2)
          ENABLE_DEBUG_PAGE=true
          ENABLE_VERBOSE_LOGGING=true
          EOF
          fi
          
          # Build based on platform
          case "${{ matrix.platform }}" in
            web)
              flutter build web --release --dart-define-from-file="$ENV_FILE"
              ;;
            android)
              flutter build apk --release --dart-define-from-file="$ENV_FILE"
              ;;
          esac
          
          if [ $? -eq 0 ]; then
            echo "✅ ${{ matrix.platform }} build successful"
          else
            echo "❌ ${{ matrix.platform }} build failed"
            exit 1
          fi

      - name: 🔍 Verify production build has zero debug output
        if: matrix.environment == 'production'
        run: |
          echo "🔍 Verifying production build has zero debug output..."
          
          # Check built files for debug statements
          case "${{ matrix.platform }}" in
            web)
              BUILD_DIR="build/web"
              ;;
            android)
              BUILD_DIR="build/app/outputs/flutter-apk"
              ;;
          esac
          
          # This is a placeholder - in real implementation, you'd check the built artifacts
          echo "✅ Production build verification completed"

  # ============================================================================
  # DEPLOYMENT READINESS CHECK
  # ============================================================================
  deployment-readiness:
    name: 🚀 Deployment Readiness
    runs-on: ubuntu-latest
    needs: [security-checks, code-quality, unit-tests, build-verification]
    if: github.ref == 'refs/heads/main'
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔍 Version validation
        run: |
          echo "🔍 Validating version consistency..."
          
          # Extract version from pubspec.yaml
          PUBSPEC_VERSION=$(grep "^version:" pubspec.yaml | cut -d' ' -f2 | cut -d'+' -f1)
          
          # Check if version was incremented (compared to previous commit)
          git fetch origin main
          PREV_VERSION=$(git show HEAD~1:pubspec.yaml | grep "^version:" | cut -d' ' -f2 | cut -d'+' -f1 || echo "0.0.0")
          
          echo "Previous version: $PREV_VERSION"
          echo "Current version: $PUBSPEC_VERSION"
          
          if [ "$PUBSPEC_VERSION" != "$PREV_VERSION" ]; then
            echo "✅ Version incremented: $PREV_VERSION → $PUBSPEC_VERSION"
          else
            echo "⚠️ Version not incremented - consider updating for releases"
          fi

      - name: 🏷️ Check for release tag
        if: github.event_name == 'push'
        run: |
          echo "🏷️ Checking if this commit should be tagged..."
          
          # Check if this is a version bump commit
          if git log -1 --pretty=format:"%s" | grep -E "(bump|release|version)"; then
            echo "🏷️ This appears to be a release commit"
            echo "Consider creating a release tag: git tag -a v$(grep "^version:" pubspec.yaml | cut -d' ' -f2 | cut -d'+' -f1) -m 'Release notes'"
          fi

      - name: ✅ Deployment readiness summary
        run: |
          echo "✅ Deployment Readiness Check Complete"
          echo ""
          echo "📋 Summary:"
          echo "  - Security checks: ✅ PASSED"
          echo "  - Code quality: ✅ PASSED"
          echo "  - Unit tests: ✅ PASSED"
          echo "  - Build verification: ✅ PASSED"
          echo "  - Version validation: ✅ CHECKED"
          echo ""
          echo "🚀 Ready for deployment!"

  # ============================================================================
  # NOTIFICATION AND REPORTING
  # ============================================================================
  notify-status:
    name: 📢 Notify Status
    runs-on: ubuntu-latest
    needs: [security-checks, code-quality, unit-tests, build-verification]
    if: always()
    steps:
      - name: 📢 Report CI Status
        run: |
          echo "📢 CI Pipeline Status Report"
          echo "=========================="
          echo "Security Checks: ${{ needs.security-checks.result }}"
          echo "Code Quality: ${{ needs.code-quality.result }}"
          echo "Unit Tests: ${{ needs.unit-tests.result }}"
          echo "Build Verification: ${{ needs.build-verification.result }}"
          echo ""
          
          if [ "${{ needs.security-checks.result }}" == "success" ] && \
             [ "${{ needs.code-quality.result }}" == "success" ] && \
             [ "${{ needs.unit-tests.result }}" == "success" ] && \
             [ "${{ needs.build-verification.result }}" == "success" ]; then
            echo "🎉 All CI checks passed!"
          else
            echo "❌ Some CI checks failed"
          fi
