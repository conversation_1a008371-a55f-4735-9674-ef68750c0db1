import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/auth/auth_state.dart' as auth_state;
import '../../widgets/auth/sign_in_form.dart';
import '../../widgets/auth/sign_up_form.dart';

/// Authentication page that mirrors the web app's auth functionality
class AuthPage extends StatefulWidget {
  const AuthPage({super.key});

  @override
  State<AuthPage> createState() => _AuthPageState();
}

class _AuthPageState extends State<AuthPage> {
  bool _isLoading = false;
  bool _showRegisterForm = false;

  @override
  void initState() {
    super.initState();
  }

  void _toggleForm() {
    setState(() {
      _showRegisterForm = !_showRegisterForm;
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, auth_state.AuthState>(
      listener: (context, state) {
        setState(() {
          _isLoading = state is auth_state.AuthLoading;
        });

        if (state is auth_state.AuthAuthenticated) {
          context.go('/dashboard');
        } else if (state is auth_state.AuthOnboardingRequired) {
          context.go('/onboarding');
        } else if (state is auth_state.AuthError) {
          // Check if it's a registration error for existing user
          if (state.message.toLowerCase().contains('already') ||
              state.message.toLowerCase().contains('existe') ||
              state.message.toLowerCase().contains('registered')) {
            // Switch to login form and show message
            setState(() {
              _showRegisterForm = false;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                    'Cet email est déjà enregistré. Veuillez vous connecter.'),
                backgroundColor: AppColors.warning,
                action: SnackBarAction(
                  label: 'Se connecter',
                  textColor: AppColors.white,
                  onPressed: () {
                    // Form is already switched to login
                  },
                ),
              ),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          }
        } else if (state is auth_state.AuthSignUpSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: AppColors.success,
              duration: const Duration(seconds: 1),
            ),
          );
          // Redirect to onboarding after successful registration
          context.go('/onboarding');
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.grey50,
        body: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 40),
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 400),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Logo
                    Image.asset(
                      'Assets/Icons/Logo_App_v0.png',
                      width: 80,
                      height: 80,
                    ),

                    const SizedBox(height: 32),

                    // Auth Card
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.shadowLight,
                            blurRadius: 20,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(24),
                        child: _showRegisterForm
                            ? SignUpForm(
                                isLoading: _isLoading,
                                onToggleForm: _toggleForm,
                              )
                            : SignInForm(
                                isLoading: _isLoading,
                                onToggleForm: _toggleForm,
                              ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Footer
                    Text(
                      'En vous connectant, vous acceptez nos conditions d\'utilisation',
                      style: AppTextStyles.caption.copyWith(
                        color: AppColors.grey600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
