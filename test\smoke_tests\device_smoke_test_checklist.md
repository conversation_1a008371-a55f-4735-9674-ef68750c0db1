# 📱 MedyTrack Mobile - Device Smoke Test Checklist

## 🎯 Purpose
This checklist ensures MedyTrack Mobile functions correctly across different physical devices and platforms before production deployment.

## 📋 Pre-Test Setup

### Environment Configuration
- [ ] **Production Build**: `flutter build apk --release` or `flutter build ios --release`
- [ ] **Environment**: Production configuration (.env.prod)
- [ ] **Database**: Production Supabase instance
- [ ] **Logging**: Zero debug output verified
- [ ] **Version**: Correct version number displayed in app

### Test Devices Required
- [ ] **Android**: Minimum API 21 (Android 5.0)
- [ ] **iOS**: Minimum iOS 12.0
- [ ] **Web**: Chrome, Firefox, Safari
- [ ] **Different Screen Sizes**: Phone, tablet, desktop

## 🧪 Core Functionality Smoke Tests

### 1. Application Launch & Authentication
- [ ] **App Launch**: Application starts without crashes
- [ ] **Splash Screen**: Displays correctly with proper branding
- [ ] **Authentication**: User can log in successfully
- [ ] **Navigation**: Bottom navigation bar functions properly
- [ ] **Locale**: App displays in correct language (French/English/Arabic)

### 2. Medicine Management
- [ ] **Medicine List**: Displays all user medicines correctly
- [ ] **Location Display**: Medicine cards show human-readable location names (NOT UUIDs)
  - [ ] Verify "Armoire" displays as "Armoire" (not UUID)
  - [ ] Verify "Cuisine" displays as "Cuisine" (not UUID)
  - [ ] Verify "Salle de bain" displays correctly
- [ ] **Add Medicine**: Can add new medicine successfully
- [ ] **Edit Medicine**: Can modify existing medicine
- [ ] **Delete Medicine**: Can remove medicine with confirmation
- [ ] **Search**: Medicine search functionality works

### 3. Reminder System
- [ ] **Today's Reminders**: Dashboard shows correct count
- [ ] **Reminder Actions**: Take/Skip/Snooze buttons function
- [ ] **Dose History**: Actions are recorded correctly
- [ ] **Notification**: System notifications work (if enabled)
- [ ] **Reminder Status**: Active/Paused/Archived states work

### 4. Data Persistence
- [ ] **Offline Capability**: App functions without internet (cached data)
- [ ] **Data Sync**: Changes sync when connection restored
- [ ] **Session Persistence**: User remains logged in between app restarts
- [ ] **Data Integrity**: No data loss during normal operations

## 🔍 Critical Bug Verification

### Medicine Location Display Bug Fix
**Background**: Previously, medicine cards displayed UUIDs instead of location names.

#### Test Steps:
1. **Navigate** to "My Medicines" page
2. **Verify** each medicine card shows:
   - [ ] Human-readable location name (e.g., "Armoire", "Cuisine")
   - [ ] NO UUID strings (format: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx)
   - [ ] Proper French location names if applicable
3. **Add New Medicine** with location
4. **Verify** new medicine displays location name correctly
5. **Edit Medicine** location
6. **Verify** updated location displays correctly

#### Expected Results:
- ✅ **PASS**: All locations show as readable names
- ❌ **FAIL**: Any UUID displayed instead of location name

## 📱 Device-Specific Tests

### Android Devices
- [ ] **Permissions**: App requests necessary permissions
- [ ] **Back Button**: Android back button navigation works
- [ ] **App Switching**: App resumes correctly after switching
- [ ] **Keyboard**: Soft keyboard doesn't break layout
- [ ] **Orientation**: App handles rotation properly

### iOS Devices
- [ ] **Safe Area**: Content respects notch/safe areas
- [ ] **Gestures**: iOS gestures work correctly
- [ ] **App Backgrounding**: App handles background/foreground correctly
- [ ] **Keyboard**: iOS keyboard integration works
- [ ] **Status Bar**: Status bar styling is appropriate

### Web Browsers
- [ ] **Chrome**: Full functionality works
- [ ] **Firefox**: Core features functional
- [ ] **Safari**: Basic operations work
- [ ] **Responsive**: Layout adapts to different screen sizes
- [ ] **URL Navigation**: Deep linking works if implemented

## 🚨 Production Safety Checks

### Security Verification
- [ ] **No Debug Output**: Console shows zero debug messages
- [ ] **No Sensitive Data**: No tokens, UUIDs, or personal data in logs
- [ ] **Authentication**: Secure login/logout functionality
- [ ] **Data Protection**: User data properly isolated

### Performance Checks
- [ ] **App Launch Time**: < 3 seconds on average device
- [ ] **Navigation Speed**: Smooth transitions between screens
- [ ] **Memory Usage**: No excessive memory consumption
- [ ] **Battery Impact**: Reasonable battery usage during normal use

## 📊 Test Results Template

### Test Session Information
- **Date**: ___________
- **Tester**: ___________
- **App Version**: ___________
- **Build Type**: Production/Debug
- **Environment**: Production/Development

### Device Information
- **Device Model**: ___________
- **OS Version**: ___________
- **Screen Size**: ___________
- **Available RAM**: ___________

### Test Results Summary
- **Total Tests**: _____ / _____
- **Passed**: _____
- **Failed**: _____
- **Critical Issues**: _____
- **Minor Issues**: _____

### Critical Issues Found
| Issue | Severity | Description | Steps to Reproduce |
|-------|----------|-------------|-------------------|
|       |          |             |                   |

### Sign-off
- [ ] **All critical tests passed**
- [ ] **Medicine location display verified**
- [ ] **No debug output in production**
- [ ] **App ready for deployment**

**Tester Signature**: ___________  
**Date**: ___________

## 🔄 Automated Smoke Test Script

For automated testing, use this Flutter integration test:

```dart
// test_driver/smoke_test.dart
import 'package:flutter_driver/flutter_driver.dart';
import 'package:test/test.dart';

void main() {
  group('MedyTrack Smoke Tests', () {
    late FlutterDriver driver;

    setUpAll(() async {
      driver = await FlutterDriver.connect();
    });

    tearDownAll(() async {
      await driver.close();
    });

    test('App launches and shows dashboard', () async {
      // Verify app launches
      await driver.waitFor(find.byType('MaterialApp'));
      
      // Verify dashboard loads
      await driver.waitFor(find.text('Tableau de bord'));
    });

    test('Medicine location displays correctly', () async {
      // Navigate to medicines
      await driver.tap(find.text('Médicaments'));
      
      // Verify no UUIDs in location display
      final locationTexts = await driver.getText(find.byType('Text'));
      for (final text in locationTexts) {
        expect(text, isNot(matches(RegExp(r'^[0-9a-fA-F-]{36}$'))));
      }
    });
  });
}
```

---

**Last Updated**: 2025-09-12  
**Version**: 1.0  
**Applicable to**: MedyTrack Mobile v0.5.1+
