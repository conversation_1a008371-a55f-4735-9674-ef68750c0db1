name: medytrack_mobile_v2
description: "MedyTrack Mobile - Professional Medicine Management Application"
publish_to: 'none'

version: 0.6.0+1

environment:
  sdk: ^3.5.0

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # State Management
  flutter_bloc: ^8.1.6
  equatable: ^2.0.5

  # Backend & Storage
  supabase_flutter: ^2.6.0
  flutter_secure_storage: ^9.2.2
  flutter_dotenv: ^5.1.0

  # Navigation & DI
  go_router: ^14.2.7
  get_it: ^7.7.0
  injectable: ^2.4.4

  # UI & Media
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.4.1
  image_picker: ^1.1.2
  timezone: ^0.9.4
  google_fonts: ^6.2.1

  # Functional Programming & Network
  dartz: ^0.10.1
  dio: ^5.7.0
  connectivity_plus: ^6.0.5
  shared_preferences: ^2.3.2
  intl: ^0.20.2

  # UI Components
  cupertino_icons: ^1.0.8

  # Security & Authentication
  local_auth: ^2.3.0

  # Notifications
  flutter_local_notifications: ^17.2.3
  timeline_tile: ^2.0.0
  device_info_plus: ^11.5.0
  http: ^1.5.0
  package_info_plus: ^8.0.2

  # QR Code functionality
  mobile_scanner: ^5.2.3
  qr_flutter: ^4.1.0

  # Onboarding
  intro_slider: ^4.2.5

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter
  flutter_lints: ^6.0.0
  injectable_generator: ^2.6.2
  build_runner: ^2.4.13
  mocktail: ^1.0.4
  mockito: ^5.4.4
  bloc_test: ^9.1.7
  flutter_launcher_icons: ^0.13.1

flutter:
  uses-material-design: true
  generate: true

  assets:
    - Assets/Icons/Logo_App_v0.png
    - assets/Illustrations/
    - .env.dev
    - .env.prod

  # fonts:
  #   - family: Ubuntu
  #     fonts:
  #       - asset: assets/fonts/Ubuntu-Regular.ttf
  #       - asset: assets/fonts/Ubuntu-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/Ubuntu-Bold.ttf
  #         weight: 700

# App icon configuration
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/Icons/Logo_App_v0.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
