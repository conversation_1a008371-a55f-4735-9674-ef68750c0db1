-- MedyTrack Database Bulk Migration Script
-- From Production to Development Environment
-- Generated: 2025-09-11

-- =====================================================
-- PHASE 1: DISABLE FOREIGN KEY CONSTRAINTS TEMPORARILY
-- =====================================================

-- Disable foreign key constraints to allow bulk insertion
ALTER TABLE public.presentations DROP CONSTRAINT IF EXISTS presentations_cis_code_fkey;
ALTER TABLE public.medicine_tags DROP CONSTRAINT IF EXISTS medicine_tags_tag_id_fkey;
ALTER TABLE public.medicine_tags DROP CONSTRAINT IF EXISTS medicine_tags_user_medicine_id_fkey;
ALTER TABLE public.user_medicines DROP CONSTRAINT IF EXISTS user_medicines_medicine_id_fkey;
ALTER TABLE public.reminders DROP CONSTRAINT IF EXISTS reminders_user_medicine_id_fkey;
ALTER TABLE public.dose_history DROP CONSTRAINT IF EXISTS dose_history_user_medicine_id_fkey;
ALTER TABLE public.dose_history DROP CONSTRAINT IF EXISTS dose_history_reminder_id_fkey;

-- =====================================================
-- PHASE 2: CLEAR EXISTING PARTIAL DATA
-- =====================================================

-- Clear existing partial data to avoid conflicts
TRUNCATE TABLE public.dose_history CASCADE;
TRUNCATE TABLE public.reminders CASCADE;
TRUNCATE TABLE public.medicine_tags CASCADE;
TRUNCATE TABLE public.user_medicines CASCADE;
TRUNCATE TABLE public.household_members CASCADE;
TRUNCATE TABLE public.households CASCADE;
TRUNCATE TABLE public.family_members CASCADE;
TRUNCATE TABLE public.families CASCADE;
TRUNCATE TABLE public.locations CASCADE;
TRUNCATE TABLE public.app_errors CASCADE;

-- Clear auth data (will be repopulated)
DELETE FROM auth.refresh_tokens;
DELETE FROM auth.sessions;
DELETE FROM auth.identities;
DELETE FROM auth.users;

-- Clear profile data
DELETE FROM public.profiles;
DELETE FROM public.users;

-- Clear reference data that needs to be completed
DELETE FROM public.presentations;
DELETE FROM public.tags;

-- Note: Keep tunisia_medicines and partial specialties as they're already correctly populated

-- =====================================================
-- PHASE 3: MIGRATION COMMANDS PLACEHOLDER
-- =====================================================

-- This section will be populated with actual data migration commands
-- The migration will be executed in dependency order:

-- 1. Complete specialties data (missing 7,562 records)
-- 2. Insert presentations data (20,760 records)
-- 3. Insert tags data (336 records)
-- 4. Insert auth.users data (11 records)
-- 5. Insert auth.identities data (11 records)
-- 6. Insert public.users data (11 records)
-- 7. Insert public.profiles data (11 records)
-- 8. Insert households data (15 records)
-- 9. Insert household_members data (10 records)
-- 10. Insert families data (0 records)
-- 11. Insert family_members data (7 records)
-- 12. Insert locations data (10 records)
-- 13. Insert user_medicines data (48 records)
-- 14. Insert medicine_tags data (39 records)
-- 15. Insert reminders data (5 records)
-- 16. Insert dose_history data (46 records)
-- 17. Insert app_errors data (8 records)
-- 18. Insert auth.sessions data (93 records)
-- 19. Insert auth.refresh_tokens data (272 records)

-- =====================================================
-- PHASE 4: RESTORE FOREIGN KEY CONSTRAINTS
-- =====================================================

-- Restore foreign key constraints after migration
ALTER TABLE public.presentations ADD CONSTRAINT presentations_cis_code_fkey 
    FOREIGN KEY (cis_code) REFERENCES public.specialties(cis_code);

ALTER TABLE public.medicine_tags ADD CONSTRAINT medicine_tags_tag_id_fkey 
    FOREIGN KEY (tag_id) REFERENCES public.tags(id);

ALTER TABLE public.medicine_tags ADD CONSTRAINT medicine_tags_user_medicine_id_fkey 
    FOREIGN KEY (user_medicine_id) REFERENCES public.user_medicines(id);

ALTER TABLE public.user_medicines ADD CONSTRAINT user_medicines_medicine_id_fkey 
    FOREIGN KEY (medicine_id) REFERENCES public.tunisia_medicines(id);

ALTER TABLE public.reminders ADD CONSTRAINT reminders_user_medicine_id_fkey 
    FOREIGN KEY (user_medicine_id) REFERENCES public.user_medicines(id);

ALTER TABLE public.dose_history ADD CONSTRAINT dose_history_user_medicine_id_fkey 
    FOREIGN KEY (user_medicine_id) REFERENCES public.user_medicines(id);

ALTER TABLE public.dose_history ADD CONSTRAINT dose_history_reminder_id_fkey 
    FOREIGN KEY (reminder_id) REFERENCES public.reminders(id);

-- =====================================================
-- PHASE 5: VERIFICATION QUERIES
-- =====================================================

-- Verification queries to check migration success
SELECT 'tunisia_medicines' as table_name, COUNT(*) as record_count FROM public.tunisia_medicines
UNION ALL
SELECT 'specialties' as table_name, COUNT(*) as record_count FROM public.specialties
UNION ALL
SELECT 'presentations' as table_name, COUNT(*) as record_count FROM public.presentations
UNION ALL
SELECT 'tags' as table_name, COUNT(*) as record_count FROM public.tags
UNION ALL
SELECT 'auth.users' as table_name, COUNT(*) as record_count FROM auth.users
UNION ALL
SELECT 'public.users' as table_name, COUNT(*) as record_count FROM public.users
UNION ALL
SELECT 'profiles' as table_name, COUNT(*) as record_count FROM public.profiles
UNION ALL
SELECT 'households' as table_name, COUNT(*) as record_count FROM public.households
UNION ALL
SELECT 'user_medicines' as table_name, COUNT(*) as record_count FROM public.user_medicines
UNION ALL
SELECT 'reminders' as table_name, COUNT(*) as record_count FROM public.reminders
UNION ALL
SELECT 'dose_history' as table_name, COUNT(*) as record_count FROM public.dose_history
ORDER BY record_count DESC;

-- Expected results:
-- presentations: 20,760
-- specialties: 15,824
-- tunisia_medicines: 5,659
-- tags: 336
-- user_medicines: 48
-- dose_history: 46
-- households: 15
-- auth.users: 11
-- public.users: 11
-- profiles: 11
-- household_members: 10
-- locations: 10
-- app_errors: 8
-- family_members: 7
-- reminders: 5
-- families: 0
