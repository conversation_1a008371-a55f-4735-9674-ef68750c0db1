import 'package:equatable/equatable.dart';

/// Dashboard statistics entity that mirrors the web app's DashboardData type
class DashboardStats extends Equatable {
  final int total;
  final int expired;
  final int expiringSoon;
  final int lowStock;
  final int locationCount;
  final int totalLocations;
  final int tagCount;
  final int totalTags;
  final int locationUsagePercentage;
  final int tagUsagePercentage;

  const DashboardStats({
    this.total = 0,
    this.expired = 0,
    this.expiringSoon = 0,
    this.lowStock = 0,
    this.locationCount = 0,
    this.totalLocations = 0,
    this.tagCount = 0,
    this.totalTags = 7, // System tags count
    this.locationUsagePercentage = 0,
    this.tagUsagePercentage = 0,
  });

  /// Get total medicines with issues (expired + expiring + low stock)
  int get totalWithIssues => expired + expiringSoon + lowStock;

  /// Get percentage of medicines with issues
  double get issuesPercentage {
    if (total == 0) return 0.0;
    return (totalWithIssues / total) * 100;
  }

  /// Get percentage of expired medicines
  double get expiredPercentage {
    if (total == 0) return 0.0;
    return (expired / total) * 100;
  }

  /// Get percentage of expiring soon medicines
  double get expiringSoonPercentage {
    if (total == 0) return 0.0;
    return (expiringSoon / total) * 100;
  }

  /// Get percentage of low stock medicines
  double get lowStockPercentage {
    if (total == 0) return 0.0;
    return (lowStock / total) * 100;
  }

  /// Get percentage of adequate medicines
  double get adequatePercentage {
    if (total == 0) return 0.0;
    final adequate = total - totalWithIssues;
    return (adequate / total) * 100;
  }

  /// Get count of adequate medicines
  int get adequate => total - totalWithIssues;

  /// Check if there are any critical issues
  bool get hasCriticalIssues => expired > 0 || lowStock > 0;

  /// Check if there are any warnings
  bool get hasWarnings => expiringSoon > 0;

  /// Get overall health status
  String get healthStatus {
    if (total == 0) return 'empty';
    if (hasCriticalIssues) return 'critical';
    if (hasWarnings) return 'warning';
    return 'good';
  }

  /// Get health status color
  String get healthStatusColor {
    switch (healthStatus) {
      case 'critical':
        return 'red';
      case 'warning':
        return 'orange';
      case 'good':
        return 'green';
      default:
        return 'gray';
    }
  }

  /// Copy with method for immutable updates
  DashboardStats copyWith({
    int? total,
    int? expired,
    int? expiringSoon,
    int? lowStock,
    int? locationCount,
    int? totalLocations,
    int? tagCount,
    int? totalTags,
    int? locationUsagePercentage,
    int? tagUsagePercentage,
  }) {
    return DashboardStats(
      total: total ?? this.total,
      expired: expired ?? this.expired,
      expiringSoon: expiringSoon ?? this.expiringSoon,
      lowStock: lowStock ?? this.lowStock,
      locationCount: locationCount ?? this.locationCount,
      totalLocations: totalLocations ?? this.totalLocations,
      tagCount: tagCount ?? this.tagCount,
      totalTags: totalTags ?? this.totalTags,
      locationUsagePercentage:
          locationUsagePercentage ?? this.locationUsagePercentage,
      tagUsagePercentage: tagUsagePercentage ?? this.tagUsagePercentage,
    );
  }

  @override
  List<Object?> get props => [
        total,
        expired,
        expiringSoon,
        lowStock,
        locationCount,
        totalLocations,
        tagCount,
        totalTags,
        locationUsagePercentage,
        tagUsagePercentage,
      ];

  @override
  String toString() {
    return 'DashboardStats(total: $total, expired: $expired, expiringSoon: $expiringSoon, '
        'lowStock: $lowStock, locationCount: $locationCount, tagCount: $tagCount, '
        'healthStatus: $healthStatus)';
  }
}

/// Medicine expiring soon entity for dashboard alerts
class MedicineExpiringSoon extends Equatable {
  final String id;
  final String label;
  final DateTime? expirationDate;
  final int quantity;
  final String familyMemberName;
  final String location;

  const MedicineExpiringSoon({
    required this.id,
    required this.label,
    this.expirationDate,
    this.quantity = 0,
    this.familyMemberName = '',
    this.location = '',
  });

  /// Get formatted expiration date as MM/YY
  String get formattedExpiration {
    if (expirationDate == null) return 'Non définie';

    final month = expirationDate!.month.toString().padLeft(2, '0');
    final year = expirationDate!.year.toString().substring(2);
    return '$month/$year';
  }

  /// Get days until expiration
  int? get daysUntilExpiration {
    if (expirationDate == null) return null;

    final now = DateTime.now();
    final difference = expirationDate!.difference(now);
    return difference.inDays;
  }

  /// Get expiration urgency level
  String get urgencyLevel {
    final days = daysUntilExpiration;
    if (days == null) return 'unknown';

    if (days < 0) return 'expired';
    if (days <= 7) return 'critical';
    if (days <= 30) return 'warning';
    return 'normal';
  }

  @override
  List<Object?> get props => [
        id,
        label,
        expirationDate,
        quantity,
        familyMemberName,
        location,
      ];

  @override
  String toString() {
    return 'MedicineExpiringSoon(id: $id, label: $label, '
        'expirationDate: $expirationDate, urgencyLevel: $urgencyLevel)';
  }
}
