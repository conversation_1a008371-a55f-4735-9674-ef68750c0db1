import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import '../../domain/entities/medicine.dart';
import '../../domain/entities/reminder.dart';
import '../../domain/entities/settings.dart';
import 'notification_service.dart';

/// Service for managing medicine reminder notifications
class MedicineReminderService {
  static final MedicineReminderService _instance =
      MedicineReminderService._internal();
  factory MedicineReminderService() => _instance;
  MedicineReminderService._internal();

  final NotificationService _notificationService = NotificationService();

  /// Schedule expiry warning notifications for a medicine
  Future<void> scheduleExpiryWarning({
    required Medicine medicine,
    required int warningDays,
  }) async {
    if (medicine.expiration == null) return;

    final warningDate =
        medicine.expiration!.subtract(Duration(days: warningDays));

    // Don't schedule if the warning date is in the past
    if (warningDate.isBefore(DateTime.now())) return;

    final notificationId = _generateExpiryNotificationId(medicine.id);

    await _notificationService.scheduleNotification(
      id: notificationId,
      title: '⚠️ Médicament bientôt expiré',
      body:
          '${medicine.name} expire dans $warningDays jours (${_formatDate(medicine.expiration!)})',
      scheduledDate: warningDate,
      payload: 'expiry_${medicine.id}',
    );
  }

  /// Schedule low stock warning notification for a medicine
  Future<void> scheduleLowStockWarning({
    required Medicine medicine,
    required int threshold,
  }) async {
    if (medicine.quantity <= threshold) {
      final notificationId = _generateLowStockNotificationId(medicine.id);

      await _notificationService.showNotification(
        id: notificationId,
        title: '📦 Stock faible',
        body:
            '${medicine.name} - Il ne reste que ${medicine.quantity} unité(s)',
        payload: 'low_stock_${medicine.id}',
      );
    }
  }

  /// Schedule daily medication reminder
  Future<void> scheduleMedicationReminder({
    required Medicine medicine,
    required DateTime reminderTime,
    required String frequency,
  }) async {
    final notificationId = _generateReminderNotificationId(medicine.id);

    RepeatInterval repeatInterval;
    switch (frequency.toLowerCase()) {
      case 'daily':
        repeatInterval = RepeatInterval.daily;
        break;
      case 'weekly':
        repeatInterval = RepeatInterval.weekly;
        break;
      default:
        repeatInterval = RepeatInterval.daily;
    }

    await _notificationService.scheduleRepeatingNotification(
      id: notificationId,
      title: '💊 Rappel de médicament',
      body: 'Il est temps de prendre ${medicine.name}',
      repeatInterval: repeatInterval,
      payload: 'reminder_${medicine.id}',
    );
  }

  /// Update all notifications for a medicine based on settings
  Future<void> updateMedicineNotifications({
    required Medicine medicine,
    required NotificationSettings settings,
  }) async {
    // Cancel existing notifications for this medicine
    await cancelMedicineNotifications(medicine.id);

    // Schedule new notifications based on settings
    if (settings.expiryAlerts && medicine.expiration != null) {
      await scheduleExpiryWarning(
        medicine: medicine,
        warningDays: settings.expiryWarningDays,
      );
    }

    if (settings.lowStockAlerts) {
      await scheduleLowStockWarning(
        medicine: medicine,
        threshold: settings.lowStockThreshold,
      );
    }

    if (settings.medicationReminders) {
      // For now, use a default reminder time (9:00 AM)
      final reminderTime =
          DateTime.now().copyWith(hour: 9, minute: 0, second: 0);
      await scheduleMedicationReminder(
        medicine: medicine,
        reminderTime: reminderTime,
        frequency: 'daily',
      );
    }
  }

  /// Cancel all notifications for a specific medicine
  Future<void> cancelMedicineNotifications(String medicineId) async {
    await _notificationService
        .cancelNotification(_generateExpiryNotificationId(medicineId));
    await _notificationService
        .cancelNotification(_generateLowStockNotificationId(medicineId));
    await _notificationService
        .cancelNotification(_generateReminderNotificationId(medicineId));
  }

  /// Cancel all medicine notifications
  Future<void> cancelAllMedicineNotifications() async {
    await _notificationService.cancelAllNotifications();
  }

  /// Test notification (for debugging)
  Future<void> showTestNotification() async {
    await _notificationService.showNotification(
      id: 999999,
      title: '🧪 Test de notification',
      body:
          'Cette notification confirme que le système fonctionne correctement.',
      payload: 'test_notification',
    );
  }

  /// Generate unique notification ID for expiry warnings
  int _generateExpiryNotificationId(String medicineId) {
    return ('expiry_$medicineId').hashCode.abs() % 2147483647;
  }

  /// Generate unique notification ID for low stock warnings
  int _generateLowStockNotificationId(String medicineId) {
    return ('low_stock_$medicineId').hashCode.abs() % 2147483647;
  }

  /// Generate unique notification ID for medication reminders
  int _generateReminderNotificationId(String medicineId) {
    return ('reminder_$medicineId').hashCode.abs() % 2147483647;
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// Initialize the service
  Future<bool> initialize() async {
    return await _notificationService.initialize();
  }

  /// Request notification permissions
  Future<bool> requestPermissions() async {
    return await _notificationService.requestPermissions();
  }

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    return await _notificationService.areNotificationsEnabled();
  }

  /// Schedule reminder notifications for a reminder
  Future<void> scheduleReminderNotifications({
    required Reminder reminder,
    required Medicine medicine,
  }) async {
    // Cancel existing notifications for this reminder
    await cancelReminderNotifications(reminder.id!);

    if (!reminder.isActive) return;

    final now = DateTime.now();
    final endDate = reminder.endDate;

    // Don't schedule if the reminder period has ended
    if (endDate != null && endDate.isBefore(now)) return;

    switch (reminder.frequencyType) {
      case 'DAILY':
        await _scheduleDailyReminders(reminder, medicine);
        break;
      case 'WEEKLY':
        await _scheduleWeeklyReminders(reminder, medicine);
        break;
      case 'HOURLY_INTERVAL':
        await _scheduleHourlyIntervalReminders(reminder, medicine);
        break;
      case 'SPECIFIC_DATES':
        await _scheduleSpecificDateReminders(reminder, medicine);
        break;
    }
  }

  /// Schedule daily reminder notifications
  Future<void> _scheduleDailyReminders(
      Reminder reminder, Medicine medicine) async {
    for (int i = 0; i < reminder.times.length; i++) {
      final timeString = reminder.times[i];
      final timeParts = timeString.split(':');
      final hour = int.parse(timeParts[0]);
      final minute = int.parse(timeParts[1]);

      final notificationId =
          _generateReminderScheduleNotificationId(reminder.id!, i);

      // Calculate the next occurrence of this time
      final now = tz.TZDateTime.now(tz.local);
      var scheduledTime = now.copyWith(
        hour: hour,
        minute: minute,
        second: 0,
        millisecond: 0,
      );

      // If the time has passed today, schedule for tomorrow
      if (scheduledTime.isBefore(now)) {
        scheduledTime = scheduledTime.add(const Duration(days: 1));
      }

      // Schedule the first notification, then use repeating
      await _notificationService.scheduleNotification(
        id: notificationId,
        title: '💊 Rappel de médicament',
        body: 'Il est temps de prendre ${medicine.name}',
        scheduledDate: scheduledTime,
        payload: 'reminder_${reminder.id}_${medicine.id}',
      );

      // Also schedule a repeating notification for subsequent days
      await _notificationService.scheduleRepeatingNotification(
        id: notificationId + 10000, // Offset to avoid conflicts
        title: '💊 Rappel de médicament',
        body: 'Il est temps de prendre ${medicine.name}',
        repeatInterval: RepeatInterval.daily,
        payload: 'reminder_${reminder.id}_${medicine.id}',
      );
    }
  }

  /// Schedule weekly reminder notifications
  Future<void> _scheduleWeeklyReminders(
      Reminder reminder, Medicine medicine) async {
    if (reminder.frequencyDays.isEmpty) return;

    for (int dayOfWeek in reminder.frequencyDays) {
      for (int i = 0; i < reminder.times.length; i++) {
        final timeString = reminder.times[i];
        final timeParts = timeString.split(':');
        final hour = int.parse(timeParts[0]);
        final minute = int.parse(timeParts[1]);

        final notificationId = _generateWeeklyReminderNotificationId(
          reminder.id!,
          dayOfWeek,
          i,
        );

        // Calculate next occurrence of this day and time
        final now = tz.TZDateTime.now(tz.local);
        var scheduledDate = now.copyWith(
          hour: hour,
          minute: minute,
          second: 0,
          millisecond: 0,
        );

        // Adjust to the correct day of week
        final daysUntilTarget = (dayOfWeek - scheduledDate.weekday) % 7;
        scheduledDate = scheduledDate.add(Duration(days: daysUntilTarget));

        // If the time has passed today, schedule for next week
        if (scheduledDate.isBefore(now)) {
          scheduledDate = scheduledDate.add(const Duration(days: 7));
        }

        // Schedule the first notification
        await _notificationService.scheduleNotification(
          id: notificationId,
          title: '💊 Rappel de médicament',
          body: 'Il est temps de prendre ${medicine.name}',
          scheduledDate: scheduledDate,
          payload: 'reminder_${reminder.id}_${medicine.id}',
        );

        // Schedule weekly repeating notification
        await _notificationService.scheduleRepeatingNotification(
          id: notificationId + 20000, // Offset to avoid conflicts
          title: '💊 Rappel de médicament',
          body: 'Il est temps de prendre ${medicine.name}',
          repeatInterval: RepeatInterval.weekly,
          payload: 'reminder_${reminder.id}_${medicine.id}',
        );
      }
    }
  }

  /// Schedule specific date reminder notifications
  Future<void> _scheduleSpecificDateReminders(
      Reminder reminder, Medicine medicine) async {
    for (final specificDate in reminder.specificDates) {
      for (int i = 0; i < reminder.times.length; i++) {
        final timeString = reminder.times[i];
        final timeParts = timeString.split(':');
        if (timeParts.length != 2) continue;

        final hour = int.tryParse(timeParts[0]);
        final minute = int.tryParse(timeParts[1]);
        if (hour == null || minute == null) continue;

        final scheduledDateTime = DateTime(
          specificDate.year,
          specificDate.month,
          specificDate.day,
          hour,
          minute,
        );

        // Only schedule if the date is in the future
        if (scheduledDateTime.isAfter(DateTime.now())) {
          final notificationId =
              _generateReminderScheduleNotificationId(reminder.id!, i);

          await _notificationService.scheduleNotification(
            id: notificationId,
            title: '💊 Rappel de médicament',
            body: 'Il est temps de prendre ${medicine.name}',
            scheduledDate: scheduledDateTime,
            payload: 'reminder_${reminder.id}_${medicine.id}',
          );
        }
      }
    }
  }

  /// Schedule hourly interval reminder notifications
  Future<void> _scheduleHourlyIntervalReminders(
      Reminder reminder, Medicine medicine) async {
    if (reminder.frequencyValue == null) return;

    final intervalHours = reminder.frequencyValue!;

    // For hourly intervals, we'll use a simple repeating notification
    // This is a simplified approach - in a real app you might want more complex scheduling
    final notificationId =
        _generateReminderScheduleNotificationId(reminder.id!);

    await _notificationService.scheduleRepeatingNotification(
      id: notificationId,
      title: '💊 Rappel de médicament',
      body:
          'Il est temps de prendre ${medicine.displayName} (toutes les $intervalHours heures)',
      repeatInterval: RepeatInterval.hourly,
      payload: 'reminder_${reminder.id}_${medicine.id}',
    );
  }

  /// Cancel all notifications for a reminder
  Future<void> cancelReminderNotifications(String reminderId) async {
    // Cancel up to 50 possible notification IDs for this reminder
    for (int i = 0; i < 50; i++) {
      final notificationId =
          _generateReminderScheduleNotificationId(reminderId, i);
      await _notificationService.cancelNotification(notificationId);
    }

    // Cancel weekly notifications for all days of week
    for (int dayOfWeek = 1; dayOfWeek <= 7; dayOfWeek++) {
      for (int timeIndex = 0; timeIndex < 10; timeIndex++) {
        final notificationId = _generateWeeklyReminderNotificationId(
          reminderId,
          dayOfWeek,
          timeIndex,
        );
        await _notificationService.cancelNotification(notificationId);
      }
    }
  }

  /// Generate notification ID for reminder schedule
  int _generateReminderScheduleNotificationId(String reminderId,
      [int index = 0]) {
    return ('${reminderId.hashCode}$index').hashCode.abs() % 2147483647;
  }

  /// Generate notification ID for weekly reminder
  int _generateWeeklyReminderNotificationId(
      String reminderId, int dayOfWeek, int timeIndex) {
    return ('${reminderId.hashCode}$dayOfWeek$timeIndex').hashCode.abs() %
        2147483647;
  }
}
