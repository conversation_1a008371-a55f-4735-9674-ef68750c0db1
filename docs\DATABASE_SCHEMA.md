# MedyTrack Mobile - Database Schema Documentation

## 📊 Schema Overview

MedyTrack Mobile uses Supabase (PostgreSQL) as its backend database with comprehensive Row Level Security (RLS) policies for data protection.

## 🗄️ Core Tables

### 1. dose_history Table

**Purpose**: Track user actions on medicine reminders (taken, skipped, snoozed)

```sql
CREATE TABLE dose_history (
  id SERIAL PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id),
  user_medicine_id UUID NOT NULL REFERENCES user_medicines(id),
  reminder_id UUID REFERENCES reminders(id),
  scheduled_time TIMESTAMPTZ NOT NULL,
  action_time TIMESTAMPTZ,
  status VARCHAR(20) NOT NULL CHECK (status IN ('TAKEN', 'SKIPPED', 'SNOOZED')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Key Features**:
- **Auto-increment ID**: Serial primary key for unique record identification
- **User Association**: Foreign key to auth.users for user isolation
- **Medicine Reference**: Links to user_medicines table
- **Reminder Context**: Optional reference to specific reminder
- **Timestamp Tracking**: Both scheduled and actual action times
- **Status Validation**: Constrained to valid action types
- **Audit Trail**: Created/updated timestamps for tracking

### 2. reminders Table

**Purpose**: Store medicine reminder schedules and configurations

```sql
CREATE TABLE reminders (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_medicine_id UUID NOT NULL REFERENCES user_medicines(id),
  name VARCHAR(255) NOT NULL,
  dosage_amount DECIMAL(10,2) NOT NULL,
  dosage_unit VARCHAR(50) NOT NULL,
  frequency_type VARCHAR(20) NOT NULL CHECK (frequency_type IN ('DAILY', 'WEEKLY', 'HOURLY_INTERVAL', 'SPECIFIC_DATES')),
  frequency_value INTEGER,
  frequency_days INTEGER[],
  specific_dates DATE[],
  times TIME[],
  start_date DATE NOT NULL,
  end_date DATE,
  notes TEXT,
  is_active BOOLEAN DEFAULT true,
  status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'PAUSED', 'ARCHIVED')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 3. user_medicines Table

**Purpose**: Store user's medicine inventory and details

```sql
CREATE TABLE user_medicines (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id),
  household_id UUID REFERENCES households(id),
  medicine_id UUID REFERENCES medicines(id),
  custom_name VARCHAR(255),
  is_custom BOOLEAN DEFAULT false,
  dosage VARCHAR(100),
  form VARCHAR(100),
  presentation VARCHAR(100),
  quantity INTEGER NOT NULL DEFAULT 0,
  low_stock_threshold INTEGER DEFAULT 0,
  expiry_date DATE,
  location VARCHAR(255),
  family_member VARCHAR(255),
  tags TEXT[],
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

## 🔐 Row Level Security (RLS) Policies

### dose_history Policies

```sql
-- Enable RLS
ALTER TABLE dose_history ENABLE ROW LEVEL SECURITY;

-- Users can manage their own dose history
CREATE POLICY "Users can manage their own dose history" ON dose_history
  FOR ALL USING (auth.uid() = user_id);

-- Users can view dose history for their medicines
CREATE POLICY "Users can view dose history for their medicines" ON dose_history
  FOR SELECT USING (
    user_id = auth.uid() OR
    user_medicine_id IN (
      SELECT id FROM user_medicines WHERE user_id = auth.uid()
    )
  );
```

### reminders Policies

```sql
-- Enable RLS
ALTER TABLE reminders ENABLE ROW LEVEL SECURITY;

-- Users can manage reminders for their medicines
CREATE POLICY "Users can manage reminders for their medicines" ON reminders
  FOR ALL USING (
    user_medicine_id IN (
      SELECT id FROM user_medicines WHERE user_id = auth.uid()
    )
  );
```

## 📈 Database Indexes

### Performance Optimization Indexes

```sql
-- dose_history indexes
CREATE INDEX idx_dose_history_user_id ON dose_history(user_id);
CREATE INDEX idx_dose_history_user_medicine_id ON dose_history(user_medicine_id);
CREATE INDEX idx_dose_history_scheduled_time ON dose_history(scheduled_time);
CREATE INDEX idx_dose_history_status ON dose_history(status);

-- reminders indexes
CREATE INDEX idx_reminders_user_medicine_id ON reminders(user_medicine_id);
CREATE INDEX idx_reminders_is_active ON reminders(is_active);
CREATE INDEX idx_reminders_status ON reminders(status);
CREATE INDEX idx_reminders_start_date ON reminders(start_date);

-- user_medicines indexes
CREATE INDEX idx_user_medicines_user_id ON user_medicines(user_id);
CREATE INDEX idx_user_medicines_household_id ON user_medicines(household_id);
CREATE INDEX idx_user_medicines_expiry_date ON user_medicines(expiry_date);
```

## 🔄 Migration History

### Migration: 20240729000001_add_user_id_to_dose_history.sql

**Purpose**: Add user_id column to dose_history table for proper RLS compliance

```sql
-- Add user_id column
ALTER TABLE dose_history 
ADD COLUMN user_id UUID REFERENCES auth.users(id);

-- Update existing records (if any)
UPDATE dose_history 
SET user_id = (
  SELECT um.user_id 
  FROM user_medicines um 
  WHERE um.id = dose_history.user_medicine_id
);

-- Make user_id NOT NULL
ALTER TABLE dose_history 
ALTER COLUMN user_id SET NOT NULL;

-- Update RLS policies
DROP POLICY IF EXISTS "Users can manage their own dose history" ON dose_history;
CREATE POLICY "Users can manage their own dose history" ON dose_history
  FOR ALL USING (auth.uid() = user_id);
```

## 🔍 Query Patterns

### Common Queries

#### Get Today's Reminders for User
```sql
SELECT r.*, um.custom_name, um.dosage, um.form
FROM reminders r
JOIN user_medicines um ON r.user_medicine_id = um.id
WHERE um.user_id = auth.uid()
  AND r.is_active = true
  AND r.status = 'ACTIVE'
  AND (
    (r.frequency_type = 'DAILY') OR
    (r.frequency_type = 'WEEKLY' AND EXTRACT(DOW FROM CURRENT_DATE) = ANY(r.frequency_days)) OR
    (r.frequency_type = 'SPECIFIC_DATES' AND CURRENT_DATE = ANY(r.specific_dates))
  );
```

#### Get Dose History for Medicine
```sql
SELECT dh.*, r.name as reminder_name
FROM dose_history dh
LEFT JOIN reminders r ON dh.reminder_id = r.id
WHERE dh.user_id = auth.uid()
  AND dh.user_medicine_id = $1
ORDER BY dh.scheduled_time DESC
LIMIT 50;
```

#### Get Reminder Statistics
```sql
SELECT 
  status,
  COUNT(*) as count,
  DATE(scheduled_time) as date
FROM dose_history
WHERE user_id = auth.uid()
  AND scheduled_time >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY status, DATE(scheduled_time)
ORDER BY date DESC;
```

## 🛡️ Data Integrity Constraints

### Foreign Key Relationships
- `dose_history.user_id` → `auth.users.id`
- `dose_history.user_medicine_id` → `user_medicines.id`
- `dose_history.reminder_id` → `reminders.id`
- `reminders.user_medicine_id` → `user_medicines.id`
- `user_medicines.user_id` → `auth.users.id`

### Check Constraints
- `dose_history.status` must be in ('TAKEN', 'SKIPPED', 'SNOOZED')
- `reminders.frequency_type` must be in ('DAILY', 'WEEKLY', 'HOURLY_INTERVAL', 'SPECIFIC_DATES')
- `reminders.status` must be in ('ACTIVE', 'PAUSED', 'ARCHIVED')

### Data Validation
- All timestamp fields use TIMESTAMPTZ for timezone awareness
- UUID fields use gen_random_uuid() for secure unique identifiers
- Array fields properly handle PostgreSQL array syntax
- Decimal fields use appropriate precision for dosage amounts

## 📊 Performance Considerations

### Query Optimization
- Proper indexing on frequently queried columns
- RLS policies optimized for minimal performance impact
- Efficient JOIN patterns for related data retrieval

### Data Archival
- Consider archiving old dose_history records (>1 year)
- Implement soft delete for reminders (status = 'ARCHIVED')
- Regular VACUUM and ANALYZE operations for optimal performance

This schema provides a robust foundation for MedyTrack Mobile's medicine management functionality with proper security, performance, and data integrity considerations.
