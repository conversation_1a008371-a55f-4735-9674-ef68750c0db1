# MedyTrack Mobile - Production Environment Template
# Copy this file to .env.prod and fill in your actual values
# NEVER commit the actual .env.prod file to version control!

# Supabase Configuration (Production)
SUPABASE_URL=https://your-prod-project.supabase.co
SUPABASE_ANON_KEY=your-prod-anon-key-here
SUPABASE_SERVICE_KEY=your-prod-service-key-here

# App Configuration
APP_NAME=MedyTrack Mobile
APP_VERSION=0.5.1
APP_BUILD_NUMBER=1

# Feature Flags (Production)
ENABLE_DEBUG_PAGE=false
ENABLE_VERBOSE_LOGGING=false
ENABLE_NOTIFICATIONS=true
ENABLE_BIOMETRIC_AUTH=true
ENABLE_OFFLINE_MODE=true
ENABLE_ANALYTICS=true

# API Configuration
API_TIMEOUT_SECONDS=30
MAX_RETRY_ATTEMPTS=3

# Production-specific Settings
ENABLE_CRASH_REPORTING=true
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_ERROR_REPORTING=true
