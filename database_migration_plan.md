# MedyTrack Database Migration Plan - Complete Clone

## Overview
Since the Dev database is empty, we'll perform a complete clone of the Production database to ensure perfect schema and data compatibility.

## Migration Strategy

### Phase 1: Reference Data Migration (Large Tables)
These tables contain the core medicine catalog and must be preserved in Production:

1. **tunisia_medicines** (5,659 records)
2. **presentations** (20,760 records) 
3. **specialties** (15,824 records)
4. **tags** (336 records)

### Phase 2: User Data Migration (Small Tables)
These tables contain user-generated data and will be cleaned from Production after migration:

1. **Authentication Data:**
   - auth.users (11 records)
   - auth.identities (11 records)
   - auth.sessions (93 records)
   - auth.refresh_tokens (272 records)

2. **Application User Data:**
   - public.users (11 records)
   - public.profiles (11 records)
   - public.households (15 records)
   - public.household_members (10 records)

3. **Medicine & Reminder Data:**
   - public.user_medicines (48 records)
   - public.reminders (5 records)
   - public.dose_history (46 records)

4. **Supporting Data:**
   - public.families (0 records)
   - public.family_members (7 records)
   - public.locations (10 records)
   - public.medicine_tags (39 records)
   - public.app_errors (8 records)

## Migration Execution Plan

### Step 1: Create Missing Tables in Dev
The Dev database is missing these critical tables:
- reminders
- dose_history  
- app_errors
- error_summary (view)
- error_trends (view)

### Step 2: Data Migration Order (Dependency-Based)
1. Reference data (tunisia_medicines, presentations, specialties, tags)
2. Authentication data (auth.users, auth.identities)
3. User profiles (public.users, public.profiles)
4. Household structure (households, household_members)
5. Family data (families, family_members)
6. Locations and tags (locations, medicine_tags)
7. Medicine data (user_medicines)
8. Reminder system (reminders, dose_history)
9. Error logs (app_errors)
10. Auth tokens (sessions, refresh_tokens)

### Step 3: Data Verification
- Row count verification for each table
- Foreign key integrity checks
- Sample data validation

### Step 4: Production Cleanup
After successful migration and verification:
- Delete user-generated data from Production
- Preserve reference data (tunisia_medicines, presentations, specialties, tags)
- Preserve system infrastructure tables

## Tables to PRESERVE in Production (NEVER DELETE)

### Reference Data:
- public.tunisia_medicines
- public.presentations  
- public.specialties
- public.tags

### System Infrastructure:
- auth.instances
- auth.schema_migrations
- auth.saml_providers
- auth.sso_providers
- auth.sso_domains
- storage.buckets
- storage.migrations
- storage.prefixes
- realtime.schema_migrations
- realtime.subscription
- vault.secrets

### Views (Auto-regenerated):
- public.dashboard_medicine_alerts_view
- public.error_summary
- public.error_trends
- public.low_stock_medicines_view
- public.medicines_expiring_soon
- public.user_dashboard_data

## Risk Mitigation
- Complete backup documentation created
- Dependency-ordered migration to avoid foreign key violations
- Batch processing for large tables
- Verification at each step
- Rollback plan available

## Success Criteria
- All 42,579 reference records preserved in both Prod and Dev
- All user data (147+ records) successfully migrated to Dev
- Production cleaned of user data while maintaining system functionality
- Application fully functional in Dev environment
