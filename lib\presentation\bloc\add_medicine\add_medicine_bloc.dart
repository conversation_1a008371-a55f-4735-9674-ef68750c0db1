import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';

import '../../../domain/entities/medicine.dart';
import '../../../domain/entities/tag.dart';
import '../../../domain/entities/location.dart';
import '../../../domain/entities/family_member.dart';
import '../../../data/models/medicine_model.dart';
import '../../../domain/repositories/tunisia_medicine_repository.dart';
import '../../../domain/repositories/tag_repository.dart';
import '../../../domain/repositories/location_repository.dart';
import '../../../domain/repositories/family_member_repository.dart';
import '../../../domain/repositories/medicine_repository.dart';
import '../../../domain/entities/tunisia_medicine.dart';
import '../../../core/utils/debug_logger.dart';
import '../../../core/utils/supabase_utils.dart';
import 'add_medicine_event.dart';
import 'add_medicine_state.dart';

@injectable
class AddMedicineBloc extends Bloc<AddMedicineEvent, AddMedicineState> {
  final TunisiaMedicineRepository _tunisiaMedicineRepository;
  final TagRepository _tagRepository;
  final LocationRepository _locationRepository;
  final FamilyMemberRepository _familyMemberRepository;
  final MedicineRepository _medicineRepository;

  AddMedicineBloc(
    this._tunisiaMedicineRepository,
    this._tagRepository,
    this._locationRepository,
    this._familyMemberRepository,
    this._medicineRepository,
  ) : super(const AddMedicineInitial()) {
    on<AddMedicineInitialized>(_onInitialized);
    on<MedicinesSearched>(_onMedicinesSearched);
    on<SearchCleared>(_onSearchCleared);
    on<MedicineSelected>(_onMedicineSelected);
    on<MedicineNameSelected>(_onMedicineNameSelected);
    on<DosageFormSelected>(_onDosageFormSelected);
    on<ModeToggled>(_onModeToggled);
    on<FormFieldUpdated>(_onFormFieldUpdated);
    on<TagsLoaded>(_onTagsLoaded);
    on<TagToggled>(_onTagToggled);
    on<TagCreated>(_onTagCreated);
    on<LocationsLoaded>(_onLocationsLoaded);
    on<LocationSelected>(_onLocationSelected);
    on<FamilyMembersLoaded>(_onFamilyMembersLoaded);
    on<FamilyMemberSelected>(_onFamilyMemberSelected);
    on<LocationsAndFamilyMembersLoaded>(_onLocationsAndFamilyMembersLoaded);
    on<FormValidated>(_onFormValidated);
    on<MedicineSubmitted>(_onMedicineSubmitted);
    on<FormReset>(_onFormReset);
  }

  Future<void> _onInitialized(
    AddMedicineInitialized event,
    Emitter<AddMedicineState> emit,
  ) async {
    emit(const AddMedicineLoading());

    DebugLogger.logBloc(
        'Initializing AddMedicineBloc with immediate dropdown loading');

    try {
      // Load dropdown data immediately and concurrently with form initialization
      DebugLogger.logBloc(
          'Loading persistent dropdown data for household: "${event.householdId}"');

      final results = await Future.wait([
        _locationRepository.getHouseholdLocations(event.householdId).first,
        _familyMemberRepository.getHouseholdMembers(event.householdId).first,
        _tagRepository.getHouseholdTags(event.householdId).first,
      ]);

      final locations = results[0] as List<Location>;
      final familyMembers = results[1] as List<FamilyMember>;
      final tags = results[2] as List<Tag>;

      DebugLogger.logBloc('Persistent dropdown data loaded successfully',
          data: {
            'householdId': event.householdId,
            'locationCount': locations.length,
            'familyMemberCount': familyMembers.length,
            'tagCount': tags.length,
          });

      // Handle default locations if needed
      List<Location> finalLocations = locations;
      if (locations.isEmpty) {
        DebugLogger.logBloc('No locations found, creating defaults');
        await _locationRepository.initializeDefaultLocations(event.householdId);
        finalLocations = await _locationRepository
            .getHouseholdLocations(event.householdId)
            .first;
      }

      // Initialize form state with all dropdown data loaded
      final formState = AddMedicineFormState(
        householdId: event.householdId,
        availableLocations: finalLocations,
        availableFamilyMembers: familyMembers,
        availableTags: tags,
        formData: {
          'quantity': 1,
          'lowStockThreshold': 0,
        },
      );

      DebugLogger.logBloc(
          'Form state initialized with persistent dropdown data',
          data: {
            'availableLocationsCount': finalLocations.length,
            'availableFamilyMembersCount': familyMembers.length,
            'availableTagsCount': tags.length,
          });

      emit(formState);
    } catch (e, stackTrace) {
      DebugLogger.logError(
          'INITIALIZATION', 'Error during BLoC initialization: $e',
          data: {'householdId': event.householdId}, stackTrace: stackTrace);
      emit(AddMedicineError(
          message: 'Erreur lors de l\'initialisation: ${e.toString()}'));
    }
  }

  Future<void> _onMedicinesSearched(
    MedicinesSearched event,
    Emitter<AddMedicineState> emit,
  ) async {
    final currentState = state;
    if (currentState is! AddMedicineFormState) return;

    final query = event.query.trim();

    if (query.isEmpty) {
      emit(currentState.copyWith(
        searchResults: [],
        isSearching: false,
      ));
      return;
    }

    // Set searching state immediately
    emit(currentState.copyWith(isSearching: true));

    try {
      final results = await _tunisiaMedicineRepository.searchMedicines(
        query,
        limit: 50, // Increased limit to get more results for deduplication
      );

      // Deduplicate results by medicine name
      final Map<String, TunisiaMedicine> uniqueMedicines = {};
      for (final medicine in results) {
        if (!uniqueMedicines.containsKey(medicine.nom)) {
          uniqueMedicines[medicine.nom] = medicine;
        }
      }
      final deduplicatedResults = uniqueMedicines.values.toList();

      // Check if emit is still valid after async operation
      if (!emit.isDone) {
        emit(currentState.copyWith(
          searchResults: deduplicatedResults,
          isSearching: false,
        ));
      }
    } catch (e) {
      // Check if emit is still valid before emitting error state
      if (!emit.isDone) {
        emit(currentState.copyWith(
          searchResults: [],
          isSearching: false,
        ));
      }
    }
  }

  Future<void> _onSearchCleared(
    SearchCleared event,
    Emitter<AddMedicineState> emit,
  ) async {
    final currentState = state;
    if (currentState is! AddMedicineFormState) return;

    emit(currentState.copyWith(
      searchResults: [],
      isSearching: false,
    ));
  }

  Future<void> _onMedicineSelected(
    MedicineSelected event,
    Emitter<AddMedicineState> emit,
  ) async {
    final currentState = state;
    if (currentState is! AddMedicineFormState) return;

    // Pre-populate form with selected medicine data
    final updatedFormData = Map<String, dynamic>.from(currentState.formData);
    updatedFormData.addAll({
      'medicineName': event.medicine.nom,
      'dosage': event.medicine.dosage,
      'form': event.medicine.forme,
      'laboratoire': event.medicine.laboratoire,
      'dci': event.medicine.dci,
      'classe': event.medicine.classe,
      'sousClasse': event.medicine.sousClasse,
    });

    emit(currentState.copyWith(
      selectedMedicine: event.medicine,
      isCustomMode: false,
      searchResults: [],
      formData: updatedFormData,
    ));

    // Validate form after selection
    add(const FormValidated());
  }

  Future<void> _onMedicineNameSelected(
    MedicineNameSelected event,
    Emitter<AddMedicineState> emit,
  ) async {
    final currentState = state;
    if (currentState is! AddMedicineFormState) return;

    try {
      // Get all medicines with this name to show dosage/form options
      final allMedicines = await _tunisiaMedicineRepository.searchMedicines(
        event.medicineName,
        limit: 100,
      );

      // Filter to exact name matches and get unique dosage/form combinations
      final exactMatches = allMedicines
          .where((medicine) => medicine.nom == event.medicineName)
          .toList();

      emit(currentState.copyWith(
        selectedMedicineName: event.medicineName,
        availableDosageForms: exactMatches,
        searchResults: [], // Clear search results
      ));
    } catch (e) {
      // Handle error - keep current state
    }
  }

  Future<void> _onDosageFormSelected(
    DosageFormSelected event,
    Emitter<AddMedicineState> emit,
  ) async {
    final currentState = state;
    if (currentState is! AddMedicineFormState) return;

    // Pre-populate form with selected medicine data including notes
    final updatedFormData = Map<String, dynamic>.from(currentState.formData);
    updatedFormData.addAll({
      'medicineName': event.medicine.nom,
      'dosage': event.medicine.dosage,
      'form': event.medicine.forme,
      'laboratoire': event.medicine.laboratoire,
      'dci': event.medicine.dci,
      'classe': event.medicine.classe,
      'sousClasse': event.medicine.sousClasse,
    });

    // Auto-populate notes with DCI and therapeutic class
    String notes = '';
    if (event.medicine.dci != null && event.medicine.dci!.isNotEmpty) {
      notes += 'DCI: ${event.medicine.dci}';
    }
    if (event.medicine.classe != null && event.medicine.classe!.isNotEmpty) {
      if (notes.isNotEmpty) notes += ' | ';
      notes += 'Classe: ${event.medicine.classe}';
    }
    if (notes.isNotEmpty) {
      updatedFormData['notes'] = notes;
    }

    emit(currentState.copyWith(
      selectedMedicine: event.medicine,
      isCustomMode: false,
      // Keep availableDosageForms to maintain Supabase data for Presentation dropdown
      availableDosageForms: currentState.availableDosageForms,
      formData: updatedFormData,
    ));

    // Validate form after selection
    add(const FormValidated());
  }

  Future<void> _onModeToggled(
    ModeToggled event,
    Emitter<AddMedicineState> emit,
  ) async {
    final currentState = state;
    if (currentState is! AddMedicineFormState) return;

    DebugLogger.logBloc('ModeToggled event received', data: {
      'isCustomMode': event.isCustomMode,
      'currentCustomMode': currentState.isCustomMode,
      'searchResultsCount': currentState.searchResults.length,
      'customNameFromEvent': event.customName,
    });

    // Prepare updated form data when switching to custom mode
    Map<String, dynamic>? updatedFormData;
    if (event.isCustomMode) {
      updatedFormData = Map<String, dynamic>.from(currentState.formData);

      // Use customName from event (passed from button) or preserve existing one
      final customName = event.customName ?? updatedFormData['customName'];

      // Clear medicine-specific form data when switching to custom mode
      updatedFormData.removeWhere((key, value) => [
            'medicineName',
            'dosage',
            'form',
            'laboratoire',
            'dci',
            'classe',
            'sousClasse',
          ].contains(key));

      // Set customName if provided
      if (customName != null && customName.isNotEmpty) {
        updatedFormData['customName'] = customName;
      }

      DebugLogger.logBloc('Custom mode form data prepared', data: {
        'customName': updatedFormData['customName'],
        'customNameFromEvent': event.customName,
        'formDataKeys': updatedFormData.keys.toList(),
      });
    }

    // Single emit with all changes
    emit(currentState.copyWith(
      isCustomMode: event.isCustomMode,
      clearSelectedMedicine: event.isCustomMode,
      searchResults: [],
      formData: updatedFormData,
    ));

    add(const FormValidated());
  }

  Future<void> _onFormFieldUpdated(
    FormFieldUpdated event,
    Emitter<AddMedicineState> emit,
  ) async {
    final currentState = state;
    if (currentState is! AddMedicineFormState) return;

    DebugLogger.logBloc('FormFieldUpdated event received', data: {
      'field': event.field,
      'value': event.value,
      'isCustomMode': currentState.isCustomMode,
    });

    final updatedState = currentState.updateFormData(event.field, event.value);
    emit(updatedState);

    // Validate form after field update
    add(const FormValidated());
  }

  Future<void> _onTagsLoaded(
    TagsLoaded event,
    Emitter<AddMedicineState> emit,
  ) async {
    final currentState = state;
    if (currentState is! AddMedicineFormState) return;

    try {
      final tags = await _tagRepository.getTagsByCategory(
          currentState.householdId, 'therapeutic');
      final usageTags = await _tagRepository.getTagsByCategory(
          currentState.householdId, 'usage');

      final allTags = [...tags, ...usageTags];

      // If no tags exist, create some default ones
      if (allTags.isEmpty) {
        await _createDefaultTags(currentState.householdId);
        // Reload tags after creating defaults
        final newTags = await _tagRepository.getTagsByCategory(
            currentState.householdId, 'therapeutic');
        final newUsageTags = await _tagRepository.getTagsByCategory(
            currentState.householdId, 'usage');
        final newAllTags = [...newTags, ...newUsageTags];
        emit(currentState.copyWith(availableTags: newAllTags));
      } else {
        emit(currentState.copyWith(availableTags: allTags));
      }
    } catch (e) {
      // Continue with empty tags list if loading fails
      emit(currentState.copyWith(availableTags: []));
    }
  }

  Future<void> _createDefaultTags(String householdId) async {
    try {
      // Create some default therapeutic tags
      final defaultTherapeuticTags = [
        {'name': 'Antalgique', 'color': '#4ECDC4', 'category': 'therapeutic'},
        {'name': 'Antibiotique', 'color': '#FF6B6B', 'category': 'therapeutic'},
        {
          'name': 'Anti-inflammatoire',
          'color': '#45B7D1',
          'category': 'therapeutic'
        },
        {
          'name': 'Cardiovasculaire',
          'color': '#96CEB4',
          'category': 'therapeutic'
        },
      ];

      // Create some default usage tags
      final defaultUsageTags = [
        {'name': 'Quotidien', 'color': '#2ECC71', 'category': 'usage'},
        {'name': 'Au besoin', 'color': '#F39C12', 'category': 'usage'},
        {'name': 'Urgence', 'color': '#E74C3C', 'category': 'usage'},
        {'name': 'Chronique', 'color': '#9B59B6', 'category': 'usage'},
      ];

      final allDefaultTags = [...defaultTherapeuticTags, ...defaultUsageTags];

      for (final tagData in allDefaultTags) {
        final tag = Tag(
          id: '', // Will be set by repository
          householdId: householdId,
          name: tagData['name']!,
          color: tagData['color']!,
          category: tagData['category']!,
          createdAt: DateTime.now(),
        );
        await _tagRepository.createTag(tag);
      }
    } catch (e) {
      // Ignore errors when creating default tags
    }
  }

  Future<void> _onTagToggled(
    TagToggled event,
    Emitter<AddMedicineState> emit,
  ) async {
    final currentState = state;
    if (currentState is! AddMedicineFormState) return;

    final selectedTags = List<Tag>.from(currentState.selectedTags);

    if (selectedTags.contains(event.tag)) {
      selectedTags.remove(event.tag);
    } else {
      selectedTags.add(event.tag);
    }

    emit(currentState.copyWith(selectedTags: selectedTags));
  }

  Future<void> _onTagCreated(
    TagCreated event,
    Emitter<AddMedicineState> emit,
  ) async {
    final currentState = state;
    if (currentState is! AddMedicineFormState) return;

    try {
      final newTag = Tag(
        id: '', // Will be set by repository
        householdId: currentState.householdId,
        name: event.name,
        color: event.color,
        category: event.category,
        createdAt: DateTime.now(),
      );

      final createdTag = await _tagRepository.createTag(newTag);

      final updatedTags = List<Tag>.from(currentState.availableTags);
      updatedTags.add(createdTag);

      final updatedSelectedTags = List<Tag>.from(currentState.selectedTags);
      updatedSelectedTags.add(createdTag);

      emit(currentState.copyWith(
        availableTags: updatedTags,
        selectedTags: updatedSelectedTags,
      ));
    } catch (e) {
      emit(AddMedicineError(
          message: 'Erreur lors de la création du tag: ${e.toString()}'));
    }
  }

  // Add flags to prevent duplicate loading
  bool _isLoadingLocations = false;
  bool _isLoadingFamilyMembers = false;

  Future<void> _onLocationsLoaded(
    LocationsLoaded event,
    Emitter<AddMedicineState> emit,
  ) async {
    final currentState = state;
    if (currentState is! AddMedicineFormState) return;

    // Skip if locations are already loaded (persistent data approach)
    if (currentState.availableLocations.isNotEmpty) {
      DebugLogger.logRepository(
          'Locations already loaded (${currentState.availableLocations.length}), skipping redundant load');
      return;
    }

    // Prevent duplicate loading
    if (_isLoadingLocations) {
      DebugLogger.logRepository(
          'Locations already loading, skipping duplicate request');
      return;
    }
    _isLoadingLocations = true;

    DebugLogger.logRepository(
        'Loading locations for household: "${currentState.householdId}"');
    try {
      final locationsStream =
          _locationRepository.getHouseholdLocations(currentState.householdId);
      final locations = await locationsStream.first;
      DebugLogger.logRepository('Found ${locations.length} locations', data: {
        'householdId': currentState.householdId,
        'locationCount': locations.length,
        'locations':
            locations.map((l) => {'id': l.id, 'name': l.name}).toList(),
      });

      // If no locations exist, create default ones
      if (locations.isEmpty) {
        await _locationRepository
            .initializeDefaultLocations(currentState.householdId);
        // Reload locations after creating defaults
        final newLocationsStream =
            _locationRepository.getHouseholdLocations(currentState.householdId);
        final newLocations = await newLocationsStream.first;
        emit(currentState.copyWith(
          availableLocations: newLocations,
          // Preserve existing family members to prevent race condition
          availableFamilyMembers: currentState.availableFamilyMembers,
        ));
      } else {
        emit(currentState.copyWith(
          availableLocations: locations,
          // Preserve existing family members to prevent race condition
          availableFamilyMembers: currentState.availableFamilyMembers,
        ));
      }
    } catch (e) {
      // Continue with empty locations list if loading fails
      emit(currentState.copyWith(
        availableLocations: [],
        // Preserve existing family members to prevent race condition
        availableFamilyMembers: currentState.availableFamilyMembers,
      ));
    } finally {
      _isLoadingLocations = false;
    }
  }

  Future<void> _onLocationSelected(
    LocationSelected event,
    Emitter<AddMedicineState> emit,
  ) async {
    final currentState = state;
    if (currentState is! AddMedicineFormState) return;

    DebugLogger.logBloc('LocationSelected event received', data: {
      'locationId': event.location?.id,
      'locationName': event.location?.displayName,
    });
    emit(currentState.copyWith(selectedLocation: event.location));
    DebugLogger.logBloc('Location state updated', data: {
      'selectedLocationId': event.location?.id,
      'selectedLocationName': event.location?.displayName,
    });

    // Trigger form validation after location selection
    add(const FormValidated());
  }

  Future<void> _onFamilyMembersLoaded(
    FamilyMembersLoaded event,
    Emitter<AddMedicineState> emit,
  ) async {
    final currentState = state;
    if (currentState is! AddMedicineFormState) return;

    // Skip if family members are already loaded (persistent data approach)
    if (currentState.availableFamilyMembers.isNotEmpty) {
      DebugLogger.logRepository(
          'Family members already loaded (${currentState.availableFamilyMembers.length}), skipping redundant load');
      return;
    }

    // Prevent duplicate loading
    if (_isLoadingFamilyMembers) {
      DebugLogger.logRepository(
          'Family members already loading, skipping duplicate request');
      return;
    }
    _isLoadingFamilyMembers = true;

    DebugLogger.logRepository(
        'Loading family members for household: "${currentState.householdId}"');
    try {
      final membersStream =
          _familyMemberRepository.getHouseholdMembers(currentState.householdId);
      final members = await membersStream.first;
      DebugLogger.logRepository('Found ${members.length} family members',
          data: {
            'householdId': currentState.householdId,
            'memberCount': members.length,
            'members':
                members.map((m) => {'id': m.id, 'name': m.name}).toList(),
          });

      emit(currentState.copyWith(
        availableFamilyMembers: members,
        // Preserve existing locations to prevent race condition
        availableLocations: currentState.availableLocations,
      ));
    } catch (e, stackTrace) {
      DebugLogger.logError('FAMILY_MEMBERS', 'Error loading family members: $e',
          data: {'householdId': currentState.householdId},
          stackTrace: stackTrace);
      // Continue with empty members list if loading fails
      emit(currentState.copyWith(
        availableFamilyMembers: [],
        // Preserve existing locations to prevent race condition
        availableLocations: currentState.availableLocations,
      ));
    } finally {
      _isLoadingFamilyMembers = false;
    }
  }

  /// Batched loading of both locations and family members to prevent race conditions
  Future<void> _onLocationsAndFamilyMembersLoaded(
    LocationsAndFamilyMembersLoaded event,
    Emitter<AddMedicineState> emit,
  ) async {
    final currentState = state;
    if (currentState is! AddMedicineFormState) return;

    // Prevent duplicate loading
    if (_isLoadingLocations || _isLoadingFamilyMembers) {
      DebugLogger.logRepository(
          'Data already loading, skipping duplicate batched request');
      return;
    }
    _isLoadingLocations = true;
    _isLoadingFamilyMembers = true;

    DebugLogger.logRepository(
        'Batched loading: locations and family members for household: "${currentState.householdId}"');

    try {
      // Load both datasets concurrently using Future.wait
      final results = await Future.wait([
        _locationRepository
            .getHouseholdLocations(currentState.householdId)
            .first,
        _familyMemberRepository
            .getHouseholdMembers(currentState.householdId)
            .first,
      ]);

      final locations = results[0] as List<Location>;
      final familyMembers = results[1] as List<FamilyMember>;

      DebugLogger.logRepository('Batched loading completed', data: {
        'householdId': currentState.householdId,
        'locationCount': locations.length,
        'familyMemberCount': familyMembers.length,
      });

      // Handle default locations if needed
      List<Location> finalLocations = locations;
      if (locations.isEmpty) {
        await _locationRepository
            .initializeDefaultLocations(currentState.householdId);
        finalLocations = await _locationRepository
            .getHouseholdLocations(currentState.householdId)
            .first;
      }

      // Emit single state update with both datasets
      emit(AddMedicineFormState(
        householdId: currentState.householdId,
        availableTags: currentState.availableTags,
        availableLocations: finalLocations,
        availableFamilyMembers: familyMembers,
        formData: currentState.formData,
        formErrors: currentState.formErrors,
        selectedMedicine: currentState.selectedMedicine,
        selectedLocation: currentState.selectedLocation,
        selectedFamilyMember: currentState.selectedFamilyMember,
        selectedTags: currentState.selectedTags,
        isCustomMode: currentState.isCustomMode,
        isSubmitting: currentState.isSubmitting,
        isValid: currentState.isValid,
      ));
    } catch (e, stackTrace) {
      DebugLogger.logError('BATCHED_LOADING', 'Error in batched loading: $e',
          data: {'householdId': currentState.householdId},
          stackTrace: stackTrace);

      // Continue with empty lists if loading fails
      emit(AddMedicineFormState(
        householdId: currentState.householdId,
        availableTags: currentState.availableTags,
        availableLocations: [],
        availableFamilyMembers: [],
        formData: currentState.formData,
        formErrors: currentState.formErrors,
        selectedMedicine: currentState.selectedMedicine,
        selectedLocation: currentState.selectedLocation,
        selectedFamilyMember: currentState.selectedFamilyMember,
        selectedTags: currentState.selectedTags,
        isCustomMode: currentState.isCustomMode,
        isSubmitting: currentState.isSubmitting,
        isValid: currentState.isValid,
      ));
    } finally {
      _isLoadingLocations = false;
      _isLoadingFamilyMembers = false;
    }
  }

  Future<void> _onFamilyMemberSelected(
    FamilyMemberSelected event,
    Emitter<AddMedicineState> emit,
  ) async {
    final currentState = state;
    if (currentState is! AddMedicineFormState) return;

    DebugLogger.logBloc('FamilyMemberSelected event received', data: {
      'memberId': event.member?.id,
      'memberName': event.member?.displayName,
    });
    emit(currentState.copyWith(selectedFamilyMember: event.member));
    DebugLogger.logBloc('Family member state updated', data: {
      'selectedMemberId': event.member?.id,
      'selectedMemberName': event.member?.displayName,
    });

    // Trigger form validation after family member selection
    add(const FormValidated());
  }

  Future<void> _onFormValidated(
    FormValidated event,
    Emitter<AddMedicineState> emit,
  ) async {
    final currentState = state;
    if (currentState is! AddMedicineFormState) return;

    final errors = <String, String?>{};

    // Validate medicine name/selection
    if (currentState.isCustomMode) {
      final customName = currentState.getFormValue<String>('customName');
      if (customName == null || customName.trim().isEmpty) {
        errors['customName'] = 'Le nom du médicament est requis';
      }
    } else {
      if (currentState.selectedMedicine == null) {
        errors['medicine'] = 'Veuillez sélectionner un médicament';
      }
    }

    // Validate quantity
    final quantity = currentState.getFormValue<int>('quantity');
    if (quantity == null || quantity <= 0) {
      errors['quantity'] = 'La quantité doit être supérieure à 0';
    }

    // Validate low stock threshold
    final lowStockThreshold =
        currentState.getFormValue<int>('lowStockThreshold');
    if (lowStockThreshold == null || lowStockThreshold < 0) {
      errors['lowStockThreshold'] = 'Le seuil doit être supérieur ou égal à 0';
    }

    // Validate expiration date (required, month/year format)
    final expiration = currentState.getFormValue<DateTime>('expiration');
    if (expiration == null) {
      errors['expiration'] = 'La date d\'expiration est requise';
    } else {
      // Allow dates up to 1 year in the past (compare by month/year only)
      final now = DateTime.now();
      final oneYearAgo = DateTime(now.year - 1, now.month, 1);
      if (expiration.isBefore(oneYearAgo)) {
        errors['expiration'] =
            'La date d\'expiration ne peut pas être antérieure à 1 an';
      }
    }

    final isValid = errors.values.every((error) => error == null);

    emit(currentState.copyWith(
      formErrors: errors,
      isValid: isValid,
    ));
  }

  Future<void> _onMedicineSubmitted(
    MedicineSubmitted event,
    Emitter<AddMedicineState> emit,
  ) async {
    final currentState = state;
    if (currentState is! AddMedicineFormState) return;

    if (!currentState.isValid) {
      add(const FormValidated());
      return;
    }

    emit(currentState.copyWith(isSubmitting: true));

    try {
      // Get user ID from context
      final userId = SupabaseUtils.getUserId(event.context);
      if (userId == null) {
        emit(const AddMedicineError(
          message: 'User not authenticated. Please log in again.',
        ));
        return;
      }

      DebugLogger.logBloc('Starting medicine submission', data: {
        'isCustomMode': currentState.isCustomMode,
        'selectedLocationId': currentState.selectedLocation?.id,
        'selectedLocationName': currentState.selectedLocation?.displayName,
        'selectedMemberId': currentState.selectedFamilyMember?.id,
        'selectedMemberName': currentState.selectedFamilyMember?.displayName,
        'householdId': currentState.householdId,
        'userId': userId,
        'formData': {
          'customName': currentState.getFormValue<String>('customName'),
          'dosage': currentState.getFormValue<String>('dosage'),
          'form': currentState.getFormValue<String>('form'),
          'quantity': currentState.getFormValue<int>('quantity'),
          'lowStockThreshold':
              currentState.getFormValue<int>('lowStockThreshold'),
          'notes': currentState.getFormValue<String>('notes'),
        },
      });

      // Create medicine entity
      final medicine = Medicine(
        id: '', // Will be set by repository
        householdId: currentState.householdId,
        userId: userId, // Add the authenticated user ID
        isCustom: currentState.isCustomMode,
        medicineId: currentState.selectedMedicine?.id,
        customName: currentState.isCustomMode
            ? currentState.getFormValue<String>('customName')
            : null,
        medicineName: currentState.selectedMedicine?.nom,
        laboratoire: currentState.selectedMedicine?.laboratoire,
        dci: currentState.selectedMedicine?.dci,
        classe: currentState.selectedMedicine?.classe,
        sousClasse: currentState.selectedMedicine?.sousClasse,
        amm: currentState.selectedMedicine?.amm,
        dosage: currentState.medicineDosage,
        form: currentState.medicineForm,
        expiration: currentState.getFormValue<DateTime>('expiration'),
        quantity: currentState.getFormValue<int>('quantity') ?? 1,
        lowStockThreshold:
            currentState.getFormValue<int>('lowStockThreshold') ?? 0,
        location: currentState.selectedLocation?.id,
        locationName: currentState.selectedLocation?.name,
        familyMemberId: currentState.selectedFamilyMember?.id,
        familyMemberName: currentState.selectedFamilyMember?.name,
        notes: currentState.getFormValue<String>('notes'),
        tags: currentState.selectedTags.map((tag) => tag.name).toList(),
        barcode: null, // Not used in current implementation
        createdAt: DateTime.now(),
      );

      DebugLogger.logBloc('Medicine entity created', data: {
        'medicineId': medicine.medicineId,
        'customName': medicine.customName,
        'isCustom': medicine.isCustom,
        'dosage': medicine.dosage,
        'form': medicine.form,
        'quantity': medicine.quantity,
        'location': medicine.location,
        'familyMemberId': medicine.familyMemberId,
        'householdId': medicine.householdId,
        'expiration': medicine.expiration?.toIso8601String(),
        'notes': medicine.notes,
        'lowStockThreshold': medicine.lowStockThreshold,
      });

      // Debug the exact JSON that will be sent to database
      final medicineModel = MedicineModel.fromEntity(medicine);
      final jsonData = medicineModel.toJson();
      DebugLogger.logRepository('Medicine JSON for database submission',
          data: jsonData);

      // Add medicine to repository
      DebugLogger.logRepository('Calling addMedicine repository method');
      final result = await _medicineRepository.addMedicine(medicine);

      result.fold(
        (failure) {
          DebugLogger.logError(
              'MEDICINE_SUBMISSION', 'Medicine submission failed',
              data: {'error': failure.message});
          emit(currentState.copyWith(isSubmitting: false));
          emit(AddMedicineError(
              message: 'Erreur lors de l\'ajout: ${failure.message}'));
        },
        (addedMedicine) async {
          DebugLogger.logBloc('Medicine submission successful', data: {
            'addedMedicineId': addedMedicine.id,
            'addedMedicineName': addedMedicine.name,
          });
          // Tags are already associated in the data source layer
          // No need to handle them here
          emit(const AddMedicineSuccess(
              message: 'Médicament ajouté avec succès'));
        },
      );
    } catch (e) {
      emit(currentState.copyWith(isSubmitting: false));
      emit(AddMedicineError(
          message: 'Erreur lors de l\'ajout: ${e.toString()}'));
    }
  }

  Future<void> _onFormReset(
    FormReset event,
    Emitter<AddMedicineState> emit,
  ) async {
    final currentState = state;
    if (currentState is! AddMedicineFormState) return;

    emit(AddMedicineFormState(
      householdId: currentState.householdId,
      availableTags: currentState.availableTags,
      availableLocations: currentState.availableLocations,
      availableFamilyMembers: currentState.availableFamilyMembers,
      formData: {
        'quantity': 1,
        'lowStockThreshold': 0,
      },
    ));
  }
}
