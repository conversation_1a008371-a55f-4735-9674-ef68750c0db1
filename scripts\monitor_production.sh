#!/bin/bash

# MedyTrack Mobile - Production Monitoring Script
# Version: 1.0
# Purpose: Automated production health monitoring with rollback triggers
# Author: MedyTrack Development Team

set -euo pipefail

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_FILE="$PROJECT_ROOT/logs/monitoring_$(date +%Y%m%d_%H%M%S).log"

# Monitoring thresholds
CRITICAL_ERROR_THRESHOLD=5.0    # 5% error rate triggers immediate rollback
HIGH_ERROR_THRESHOLD=2.0        # 2% error rate triggers urgent rollback
WARNING_ERROR_THRESHOLD=1.0     # 1% error rate triggers warning

CRITICAL_RESPONSE_TIME=5000     # 5 seconds triggers immediate rollback
HIGH_RESPONSE_TIME=3000         # 3 seconds triggers urgent rollback
WARNING_RESPONSE_TIME=2000      # 2 seconds triggers warning

CRITICAL_CRASH_RATE=5.0         # 5% crash rate triggers immediate rollback
HIGH_CRASH_RATE=1.0             # 1% crash rate triggers urgent rollback

# Monitoring endpoints
HEALTH_CHECK_URL="${PRODUCTION_URL:-https://medytrack.app}/health"
METRICS_API_URL="${METRICS_API_URL:-}"
SENTRY_API_URL="${SENTRY_API_URL:-}"

# Notification settings
SLACK_WEBHOOK_URL="${SLACK_WEBHOOK_URL:-}"
EMAIL_RECIPIENTS="${EMAIL_RECIPIENTS:-}"

# Logging function
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "$LOG_FILE"
}

# Error handling
error_exit() {
    log "ERROR" "$1"
    echo -e "${RED}❌ MONITORING ERROR: $1${NC}" >&2
    send_alert "MONITORING_ERROR" "$1"
    exit 1
}

# Success message
success() {
    log "SUCCESS" "$1"
    echo -e "${GREEN}✅ $1${NC}"
}

# Warning message
warning() {
    log "WARNING" "$1"
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Info message
info() {
    log "INFO" "$1"
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Initialize monitoring
init_monitoring() {
    mkdir -p "$(dirname "$LOG_FILE")"
    info "Production monitoring initialized"
    info "Log file: $LOG_FILE"
    info "Monitoring thresholds:"
    info "  Error rate: Warning=${WARNING_ERROR_THRESHOLD}%, High=${HIGH_ERROR_THRESHOLD}%, Critical=${CRITICAL_ERROR_THRESHOLD}%"
    info "  Response time: Warning=${WARNING_RESPONSE_TIME}ms, High=${HIGH_RESPONSE_TIME}ms, Critical=${CRITICAL_RESPONSE_TIME}ms"
    info "  Crash rate: High=${HIGH_CRASH_RATE}%, Critical=${CRITICAL_CRASH_RATE}%"
}

# Health check
perform_health_check() {
    info "Performing application health check..."
    
    local start_time=$(date +%s%3N)
    local http_code
    local response_time
    
    if command -v curl >/dev/null 2>&1; then
        http_code=$(curl -s -w "%{http_code}" -o /dev/null --max-time 10 "$HEALTH_CHECK_URL" || echo "000")
        local end_time=$(date +%s%3N)
        response_time=$((end_time - start_time))
    else
        error_exit "curl command not found. Please install curl for health checks."
    fi
    
    if [[ "$http_code" == "200" ]]; then
        success "Health check passed (${response_time}ms)"
        
        # Check response time thresholds
        if [[ $response_time -gt $CRITICAL_RESPONSE_TIME ]]; then
            send_alert "CRITICAL_RESPONSE_TIME" "Health check response time: ${response_time}ms (threshold: ${CRITICAL_RESPONSE_TIME}ms)"
            return 3  # Critical
        elif [[ $response_time -gt $HIGH_RESPONSE_TIME ]]; then
            send_alert "HIGH_RESPONSE_TIME" "Health check response time: ${response_time}ms (threshold: ${HIGH_RESPONSE_TIME}ms)"
            return 2  # High
        elif [[ $response_time -gt $WARNING_RESPONSE_TIME ]]; then
            warning "Health check response time: ${response_time}ms (threshold: ${WARNING_RESPONSE_TIME}ms)"
            return 1  # Warning
        fi
        
        return 0  # Success
    else
        send_alert "HEALTH_CHECK_FAILED" "Health check failed with HTTP code: $http_code"
        return 3  # Critical
    fi
}

# Check error rates
check_error_rates() {
    info "Checking application error rates..."
    
    if [[ -z "$SENTRY_API_URL" ]]; then
        warning "Sentry API URL not configured, skipping error rate check"
        return 0
    fi
    
    # Placeholder for actual Sentry API integration
    # In real implementation, you would query Sentry API for error rates
    local error_rate=0.5  # Mock error rate
    
    info "Current error rate: ${error_rate}%"
    
    if (( $(echo "$error_rate > $CRITICAL_ERROR_THRESHOLD" | bc -l) )); then
        send_alert "CRITICAL_ERROR_RATE" "Error rate: ${error_rate}% (threshold: ${CRITICAL_ERROR_THRESHOLD}%)"
        return 3  # Critical
    elif (( $(echo "$error_rate > $HIGH_ERROR_THRESHOLD" | bc -l) )); then
        send_alert "HIGH_ERROR_RATE" "Error rate: ${error_rate}% (threshold: ${HIGH_ERROR_THRESHOLD}%)"
        return 2  # High
    elif (( $(echo "$error_rate > $WARNING_ERROR_THRESHOLD" | bc -l) )); then
        warning "Error rate: ${error_rate}% (threshold: ${WARNING_ERROR_THRESHOLD}%)"
        return 1  # Warning
    fi
    
    success "Error rate within acceptable limits: ${error_rate}%"
    return 0
}

# Check crash rates
check_crash_rates() {
    info "Checking application crash rates..."
    
    # Placeholder for actual crash rate monitoring
    # In real implementation, you would integrate with crash reporting service
    local crash_rate=0.1  # Mock crash rate
    
    info "Current crash rate: ${crash_rate}%"
    
    if (( $(echo "$crash_rate > $CRITICAL_CRASH_RATE" | bc -l) )); then
        send_alert "CRITICAL_CRASH_RATE" "Crash rate: ${crash_rate}% (threshold: ${CRITICAL_CRASH_RATE}%)"
        return 3  # Critical
    elif (( $(echo "$crash_rate > $HIGH_CRASH_RATE" | bc -l) )); then
        send_alert "HIGH_CRASH_RATE" "Crash rate: ${crash_rate}% (threshold: ${HIGH_CRASH_RATE}%)"
        return 2  # High
    fi
    
    success "Crash rate within acceptable limits: ${crash_rate}%"
    return 0
}

# Check database performance
check_database_performance() {
    info "Checking database performance..."
    
    # Test database connectivity and response time
    local db_start_time=$(date +%s%3N)
    
    # Placeholder for database health check
    # In real implementation, you would test actual database queries
    sleep 0.1  # Simulate database query
    
    local db_end_time=$(date +%s%3N)
    local db_response_time=$((db_end_time - db_start_time))
    
    info "Database response time: ${db_response_time}ms"
    
    if [[ $db_response_time -gt 2000 ]]; then
        send_alert "SLOW_DATABASE" "Database response time: ${db_response_time}ms"
        return 2  # High
    elif [[ $db_response_time -gt 1000 ]]; then
        warning "Database response time: ${db_response_time}ms"
        return 1  # Warning
    fi
    
    success "Database performance within acceptable limits"
    return 0
}

# Send alert notification
send_alert() {
    local alert_type="$1"
    local message="$2"
    local severity="${3:-HIGH}"
    
    log "ALERT" "[$alert_type] $message"
    
    # Send Slack notification
    if [[ -n "$SLACK_WEBHOOK_URL" ]]; then
        local slack_payload=$(cat <<EOF
{
  "text": "🚨 MedyTrack Production Alert",
  "attachments": [
    {
      "color": "danger",
      "fields": [
        {
          "title": "Alert Type",
          "value": "$alert_type",
          "short": true
        },
        {
          "title": "Severity",
          "value": "$severity",
          "short": true
        },
        {
          "title": "Message",
          "value": "$message",
          "short": false
        },
        {
          "title": "Timestamp",
          "value": "$(date)",
          "short": true
        }
      ]
    }
  ]
}
EOF
        )
        
        curl -X POST -H 'Content-type: application/json' \
             --data "$slack_payload" \
             "$SLACK_WEBHOOK_URL" >/dev/null 2>&1 || true
    fi
    
    # Send email notification (if configured)
    if [[ -n "$EMAIL_RECIPIENTS" ]]; then
        echo "Subject: MedyTrack Production Alert - $alert_type
        
Alert Type: $alert_type
Severity: $severity
Message: $message
Timestamp: $(date)
        
Please investigate immediately.
        
MedyTrack Monitoring System" | mail -s "MedyTrack Production Alert" "$EMAIL_RECIPIENTS" 2>/dev/null || true
    fi
}

# Determine if rollback is needed
should_rollback() {
    local max_severity=0
    
    # Perform all checks and track maximum severity
    perform_health_check
    local health_severity=$?
    [[ $health_severity -gt $max_severity ]] && max_severity=$health_severity
    
    check_error_rates
    local error_severity=$?
    [[ $error_severity -gt $max_severity ]] && max_severity=$error_severity
    
    check_crash_rates
    local crash_severity=$?
    [[ $crash_severity -gt $max_severity ]] && max_severity=$crash_severity
    
    check_database_performance
    local db_severity=$?
    [[ $db_severity -gt $max_severity ]] && max_severity=$db_severity
    
    return $max_severity
}

# Trigger emergency rollback
trigger_rollback() {
    local reason="$1"
    
    warning "INITIATING EMERGENCY ROLLBACK"
    log "ROLLBACK" "Reason: $reason"
    
    send_alert "EMERGENCY_ROLLBACK" "Initiating emergency rollback. Reason: $reason" "CRITICAL"
    
    # Execute rollback script
    if [[ -f "$SCRIPT_DIR/db_sync.sh" ]]; then
        info "Executing database rollback..."
        "$SCRIPT_DIR/db_sync.sh" rollback
    else
        warning "Database rollback script not found"
    fi
    
    # Additional rollback procedures would go here
    # - Code rollback
    # - Service restart
    # - Cache clearing
    
    success "Emergency rollback completed"
    send_alert "ROLLBACK_COMPLETED" "Emergency rollback completed successfully" "INFO"
}

# Generate monitoring report
generate_report() {
    local report_file="$PROJECT_ROOT/logs/monitoring_report_$(date +%Y%m%d_%H%M%S).txt"
    
    cat > "$report_file" << EOF
MedyTrack Production Monitoring Report
Generated: $(date)

=== SYSTEM HEALTH ===
Health Check: $(perform_health_check >/dev/null 2>&1 && echo "✅ PASSED" || echo "❌ FAILED")
Error Rates: $(check_error_rates >/dev/null 2>&1 && echo "✅ NORMAL" || echo "⚠️ ELEVATED")
Crash Rates: $(check_crash_rates >/dev/null 2>&1 && echo "✅ NORMAL" || echo "⚠️ ELEVATED")
Database: $(check_database_performance >/dev/null 2>&1 && echo "✅ HEALTHY" || echo "⚠️ SLOW")

=== THRESHOLDS ===
Error Rate Thresholds: ${WARNING_ERROR_THRESHOLD}% / ${HIGH_ERROR_THRESHOLD}% / ${CRITICAL_ERROR_THRESHOLD}%
Response Time Thresholds: ${WARNING_RESPONSE_TIME}ms / ${HIGH_RESPONSE_TIME}ms / ${CRITICAL_RESPONSE_TIME}ms
Crash Rate Thresholds: ${HIGH_CRASH_RATE}% / ${CRITICAL_CRASH_RATE}%

=== RECOMMENDATIONS ===
$(should_rollback >/dev/null 2>&1; case $? in
  0) echo "✅ System healthy - continue monitoring" ;;
  1) echo "⚠️ Minor issues detected - increased monitoring recommended" ;;
  2) echo "🔶 Significant issues detected - consider rollback within 1 hour" ;;
  3) echo "🚨 Critical issues detected - immediate rollback recommended" ;;
esac)

=== LOG LOCATION ===
Detailed logs: $LOG_FILE
EOF

    info "Monitoring report generated: $report_file"
    cat "$report_file"
}

# Main monitoring function
main() {
    local mode="${1:-check}"
    
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║              MedyTrack Production Monitoring                 ║"
    echo "║                    HEALTH & ROLLBACK                         ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    init_monitoring
    
    case "$mode" in
        "check")
            info "Performing production health check..."
            should_rollback
            local severity=$?
            
            case $severity in
                0)
                    success "All systems healthy"
                    ;;
                1)
                    warning "Minor issues detected - monitoring continues"
                    ;;
                2)
                    warning "Significant issues detected - consider rollback"
                    send_alert "ROLLBACK_CONSIDERATION" "Significant issues detected. Consider rollback within 1 hour."
                    ;;
                3)
                    error_exit "Critical issues detected - immediate rollback recommended"
                    ;;
            esac
            ;;
            
        "rollback")
            trigger_rollback "Manual rollback requested"
            ;;
            
        "report")
            generate_report
            ;;
            
        "continuous")
            info "Starting continuous monitoring mode..."
            while true; do
                should_rollback
                local severity=$?
                
                if [[ $severity -ge 3 ]]; then
                    trigger_rollback "Automatic rollback triggered by monitoring"
                    break
                fi
                
                sleep 300  # Check every 5 minutes
            done
            ;;
            
        *)
            echo "Usage: $0 {check|rollback|report|continuous}"
            echo ""
            echo "Commands:"
            echo "  check      - Perform single health check"
            echo "  rollback   - Trigger emergency rollback"
            echo "  report     - Generate monitoring report"
            echo "  continuous - Start continuous monitoring"
            exit 1
            ;;
    esac
}

# Execute main function with all arguments
main "$@"
