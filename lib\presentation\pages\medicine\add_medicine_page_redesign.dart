import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/supabase_utils.dart';
import '../../../domain/entities/tunisia_medicine.dart';
import '../../bloc/add_medicine/add_medicine_bloc.dart';
import '../../bloc/add_medicine/add_medicine_event.dart';
import '../../bloc/add_medicine/add_medicine_state.dart';
import '../../widgets/common/form_components.dart';
import '../../widgets/medicine/tag_selection_widget.dart';

/// Redesigned add medicine page with modern UI
class AddMedicinePageRedesign extends StatefulWidget {
  const AddMedicinePageRedesign({super.key});

  @override
  State<AddMedicinePageRedesign> createState() =>
      _AddMedicinePageRedesignState();
}

class _AddMedicinePageRedesignState extends State<AddMedicinePageRedesign> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _dosageController = TextEditingController();
  final _notesController = TextEditingController();

  String? _selectedForm;
  String? _selectedPresentation;
  DateTime? _expirationDate;
  final _quantityController = TextEditingController();
  final _lowStockController = TextEditingController();
  String? _selectedLocationId;
  String? _selectedFamilyMemberId;

  int _currentStep = 0;
  final int _totalSteps = 5;

  // Medicine search functionality
  Timer? _searchDebounceTimer;
  TunisiaMedicine? _selectedMedicine;
  bool _isCustomMode = false;

  @override
  void initState() {
    super.initState();

    // Set default values for Quantity fields
    _quantityController.text = '1';

    // Set default values for Low stock fields
    _lowStockController.text = '0';

    // Initialize AddMedicineBloc
    final householdId = SupabaseUtils.getHouseholdId(context);
    if (householdId != null) {
      context.read<AddMedicineBloc>().add(
            AddMedicineInitialized(householdId: householdId),
          );
    }
  }

  @override
  void dispose() {
    _searchDebounceTimer?.cancel();
    _nameController.dispose();
    _dosageController.dispose();
    _notesController.dispose();
    _quantityController.dispose();
    _lowStockController.dispose();
    super.dispose();
  }

  void _selectExpirationDate() async {
    final initialDate =
        _expirationDate ?? DateTime.now().add(const Duration(days: 365));

    // Show month/year picker dialog
    final selectedDate = await _showMonthYearPicker(context, initialDate);

    if (selectedDate != null) {
      setState(() {
        // Store as first day of selected month (web app format)
        _expirationDate = DateTime(selectedDate.year, selectedDate.month, 1);
      });
    }
  }

  Future<DateTime?> _showMonthYearPicker(
      BuildContext context, DateTime initialDate) async {
    DateTime selectedDate = initialDate;

    return showDialog<DateTime>(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Sélectionner le mois d\'expiration'),
              content: SizedBox(
                width: 300,
                height: 300,
                child: Column(
                  children: [
                    // Year selector
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        IconButton(
                          onPressed: () {
                            setState(() {
                              selectedDate = DateTime(
                                  selectedDate.year - 1, selectedDate.month);
                            });
                          },
                          icon: const Icon(Icons.chevron_left),
                        ),
                        Text(
                          '${selectedDate.year}',
                          style: AppTextStyles.titleMedium,
                        ),
                        IconButton(
                          onPressed: () {
                            setState(() {
                              selectedDate = DateTime(
                                  selectedDate.year + 1, selectedDate.month);
                            });
                          },
                          icon: const Icon(Icons.chevron_right),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    // Month grid
                    Expanded(
                      child: GridView.builder(
                        gridDelegate:
                            const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 3,
                          childAspectRatio: 2,
                          crossAxisSpacing: 8,
                          mainAxisSpacing: 8,
                        ),
                        itemCount: 12,
                        itemBuilder: (context, index) {
                          final month = index + 1;
                          final isSelected = selectedDate.month == month;
                          final monthName = DateFormat('MMM', 'fr')
                              .format(DateTime(2024, month));

                          return InkWell(
                            onTap: () {
                              setState(() {
                                selectedDate =
                                    DateTime(selectedDate.year, month);
                              });
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                color: isSelected
                                    ? AppColors.teal
                                    : Colors.transparent,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: isSelected
                                      ? AppColors.teal
                                      : AppColors.grey300,
                                ),
                              ),
                              child: Center(
                                child: Text(
                                  monthName,
                                  style: TextStyle(
                                    color: isSelected
                                        ? Colors.white
                                        : AppColors.grey700,
                                    fontWeight: isSelected
                                        ? FontWeight.w600
                                        : FontWeight.normal,
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Annuler'),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(selectedDate),
                  child: const Text('Confirmer'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AddMedicineBloc, AddMedicineState>(
      listener: (context, state) {
        if (state is AddMedicineSuccess) {
          // Show success message and navigate to My Medicines page
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Médicament ajouté avec succès!'),
              backgroundColor: AppColors.teal,
              duration: const Duration(seconds: 2),
            ),
          );
          // Navigate to My Medicines page
          context.go('/medicines/my');
        } else if (state is AddMedicineError) {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.teal,
        body: Column(
          children: [
            // Header with teal background
            Container(
              padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () => context.pop(),
                    child: Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.arrow_back,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      'Ajouter un médicament',
                      style: AppTextStyles.headlineMedium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Text(
                    '${_currentStep + 1}/$_totalSteps',
                    style: AppTextStyles.labelLarge.copyWith(
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),

            // Content container with white background
            Expanded(
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(24),
                    topRight: Radius.circular(24),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, -4),
                    ),
                  ],
                ),
                child: Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      const SizedBox(height: 24),

                      // Progress indicator
                      _buildProgressIndicator(),

                      const SizedBox(height: 32),

                      // Step content
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          child: _buildStepContent(),
                        ),
                      ),

                      // Navigation buttons
                      Padding(
                        padding: const EdgeInsets.all(20),
                        child: _buildNavigationButtons(),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Row(
      children: List.generate(_totalSteps, (index) {
        final isCompleted = index < _currentStep;
        final isCurrent = index == _currentStep;

        return Expanded(
          child: Container(
            height: 4,
            margin: EdgeInsets.only(right: index < _totalSteps - 1 ? 8 : 0),
            decoration: BoxDecoration(
              color:
                  isCompleted || isCurrent ? AppColors.teal : AppColors.grey300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildStepContent() {
    switch (_currentStep) {
      case 0:
        return _buildMedicineSelectionStep();
      case 1:
        return _buildExpiryQuantityStep();
      case 2:
        return _buildTagsStep();
      case 3:
        return _buildLocationFamilyStep();
      case 4:
        return _buildSummaryStep();
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildMedicineSelectionStep() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Sélection du médicament',
            style: AppTextStyles.headlineSmall.copyWith(
              color: AppColors.navy,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Recherchez et sélectionnez votre médicament, son dosage, sa forme et sa présentation.',
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.grey600,
            ),
          ),
          const SizedBox(height: 20),
          _buildMedicineSearchField(),
          const SizedBox(height: 16),
          _buildDosageFormSelection(),
        ],
      ),
    );
  }

  Widget _buildExpiryQuantityStep() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Expiration et quantité',
            style: AppTextStyles.headlineSmall.copyWith(
              color: AppColors.navy,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Indiquez la date d\'expiration et la quantité disponible.',
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.grey600,
            ),
          ),
          const SizedBox(height: 32),

          // Expiration date
          ModernTextField(
            label: 'Date d\'expiration',
            hint: 'Sélectionner le mois d\'expiration',
            readOnly: true,
            onTap: () => _selectExpirationDate(),
            controller: TextEditingController(
              text: _expirationDate != null
                  ? '${_expirationDate!.month.toString().padLeft(2, '0')}/${_expirationDate!.year}'
                  : '',
            ),
            suffixIcon: Icon(Icons.calendar_today, color: AppColors.teal),
          ),
          const SizedBox(height: 24),

          // Quantity and low stock threshold
          Row(
            children: [
              Expanded(
                child: ModernTextField(
                  label: 'Quantité *',
                  hint: 'Ex: 1',
                  controller: _quantityController,
                  keyboardType: TextInputType.number,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ModernTextField(
                  label: 'Seuil stock faible',
                  hint: 'Ex: 1 (0 pour ne pas surveiller)',
                  controller: _lowStockController,
                  keyboardType: TextInputType.number,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTagsStep() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Étiquettes',
            style: AppTextStyles.headlineSmall.copyWith(
              color: AppColors.navy,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Ajoutez des étiquettes pour organiser vos médicaments.',
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.grey600,
            ),
          ),
          const SizedBox(height: 32),
          const TagSelectionWidget(),
        ],
      ),
    );
  }

  Widget _buildLocationFamilyStep() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Emplacement et famille',
            style: AppTextStyles.headlineSmall.copyWith(
              color: AppColors.navy,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Indiquez où stocker le médicament et pour quel membre de la famille.',
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.grey600,
            ),
          ),
          const SizedBox(height: 32),
          _buildLocationFamilySelection(),
        ],
      ),
    );
  }

  Widget _buildLocationFamilySelection() {
    return BlocBuilder<AddMedicineBloc, AddMedicineState>(
      builder: (context, state) {
        if (state is! AddMedicineFormState) {
          return const Center(child: CircularProgressIndicator());
        }

        return Column(
          children: [
            // Location selection
            ModernDropdown<String>(
              label: 'Emplacement',
              hint: 'Sélectionner un emplacement',
              value: _selectedLocationId,
              items: state.availableLocations.map((location) {
                return DropdownMenuItem<String>(
                  value: location.id,
                  child: Text(location.displayName),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedLocationId = value;
                });
              },
            ),
            const SizedBox(height: 24),

            // Family member selection
            ModernDropdown<String>(
              label: 'Membre de la famille',
              hint: 'Sélectionner un membre',
              value: _selectedFamilyMemberId,
              items: state.availableFamilyMembers.map((member) {
                return DropdownMenuItem<String>(
                  value: member.id,
                  child: Text(member.displayName),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedFamilyMemberId = value;
                });
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildSummaryStep() {
    return BlocBuilder<AddMedicineBloc, AddMedicineState>(
      builder: (context, state) {
        if (state is! AddMedicineFormState) {
          return const Center(child: CircularProgressIndicator());
        }

        return SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Résumé',
                style: AppTextStyles.headlineSmall.copyWith(
                  color: AppColors.navy,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Vérifiez les informations avant de sauvegarder.',
                style: AppTextStyles.bodyLarge.copyWith(
                  color: AppColors.grey600,
                ),
              ),
              const SizedBox(height: 32),
              _buildSummaryCard(state),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSummaryCard(AddMedicineFormState state) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.grey50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.grey200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSummaryRow('Médicament',
              _selectedMedicine?.displayName ?? _nameController.text),
          if (_dosageController.text.isNotEmpty)
            _buildSummaryRow('Dosage', _dosageController.text),
          if (_selectedForm != null) _buildSummaryRow('Forme', _selectedForm!),
          if (_selectedPresentation != null)
            _buildSummaryRow('Présentation', _selectedPresentation!),
          if (_expirationDate != null)
            _buildSummaryRow('Expiration',
                '${_expirationDate!.month.toString().padLeft(2, '0')}/${_expirationDate!.year}'),
          if (_quantityController.text.isNotEmpty)
            _buildSummaryRow('Quantité', _quantityController.text),
          if (_lowStockController.text.isNotEmpty)
            _buildSummaryRow('Seuil stock faible', _lowStockController.text),
          if (state.selectedTags.isNotEmpty)
            _buildSummaryRow('Étiquettes',
                state.selectedTags.map((tag) => tag.displayName).join(', ')),
          if (_selectedLocationId != null &&
              state.availableLocations.any((l) => l.id == _selectedLocationId))
            _buildSummaryRow(
                'Emplacement',
                state.availableLocations
                    .firstWhere((l) => l.id == _selectedLocationId)
                    .displayName),
          if (_selectedFamilyMemberId != null &&
              state.availableFamilyMembers
                  .any((m) => m.id == _selectedFamilyMemberId))
            _buildSummaryRow(
                'Membre de la famille',
                state.availableFamilyMembers
                    .firstWhere((m) => m.id == _selectedFamilyMemberId)
                    .displayName),
          if (_notesController.text.isNotEmpty)
            _buildSummaryRow('Notes', _notesController.text),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.navy,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Row(
      children: [
        if (_currentStep > 0)
          Expanded(
            child: ModernButton(
              text: 'Précédent',
              isOutlined: true,
              onPressed: () {
                setState(() {
                  _currentStep--;
                });
              },
            ),
          ),
        if (_currentStep > 0) const SizedBox(width: 16),
        Expanded(
          child: BlocBuilder<AddMedicineBloc, AddMedicineState>(
            builder: (context, state) {
              final isSubmitting =
                  state is AddMedicineFormState && state.isSubmitting;

              return ModernButton(
                text: _currentStep == _totalSteps - 1 ? 'Ajouter' : 'Suivant',
                isLoading:
                    _currentStep == _totalSteps - 1 ? isSubmitting : false,
                onPressed: (_currentStep == _totalSteps - 1 && isSubmitting)
                    ? null // Disable button when submitting
                    : _currentStep == _totalSteps - 1
                        ? _submitForm
                        : _canProceedToNext()
                            ? () {
                                setState(() {
                                  _currentStep++;
                                });
                              }
                            : null,
              );
            },
          ),
        ),
      ],
    );
  }

  bool _canProceedToNext() {
    switch (_currentStep) {
      case 0:
        // Medicine selection step now includes dosage, form, and presentation selection
        return (_nameController.text.isNotEmpty) &&
            _dosageController.text.isNotEmpty &&
            _selectedForm != null &&
            _selectedPresentation != null;
      case 1:
        // Expiry date and quantity are mandatory
        return _quantityController.text.isNotEmpty && _expirationDate != null;
      case 2:
        return true; // Tags are optional
      case 3:
        // Location and family member are now mandatory
        return _selectedLocationId != null && _selectedFamilyMemberId != null;
      case 4:
        return true; // Summary step
      default:
        return false;
    }
  }

  void _updateFormSelection(AddMedicineFormState state) {
    // When both dosage and form are selected, find the matching medicine and trigger selection
    if (_dosageController.text.isNotEmpty && _selectedForm != null) {
      final matchingMedicine = state.availableDosageForms.firstWhere(
        (medicine) =>
            medicine.dosage == _dosageController.text &&
            medicine.forme == _selectedForm,
        orElse: () => state.availableDosageForms.first,
      );
      context.read<AddMedicineBloc>().add(
            DosageFormSelected(medicine: matchingMedicine),
          );
    }
  }

  void _submitForm() {
    if (_formKey.currentState?.validate() ?? false) {
      final bloc = context.read<AddMedicineBloc>();

      // Update form fields in the BLoC state
      bloc.add(FormFieldUpdated(
          field: 'customName',
          value: _isCustomMode ? _nameController.text : null));
      bloc.add(
          FormFieldUpdated(field: 'dosage', value: _dosageController.text));
      bloc.add(FormFieldUpdated(field: 'form', value: _selectedForm));
      bloc.add(FormFieldUpdated(
          field: 'presentation', value: _selectedPresentation));
      bloc.add(FormFieldUpdated(field: 'expiration', value: _expirationDate));
      bloc.add(FormFieldUpdated(
          field: 'quantity',
          value: int.tryParse(_quantityController.text) ?? 1));
      bloc.add(FormFieldUpdated(
          field: 'lowStockThreshold',
          value: int.tryParse(_lowStockController.text) ?? 0));
      bloc.add(FormFieldUpdated(
          field: 'notes',
          value:
              _notesController.text.isNotEmpty ? _notesController.text : null));

      // Update location and family member selections
      final state = bloc.state;
      if (state is AddMedicineFormState) {
        // Find selected location and family member from the current state
        final selectedLocation = state.availableLocations
            .where((location) => location.id == _selectedLocationId)
            .firstOrNull;
        final selectedFamilyMember = state.availableFamilyMembers
            .where((member) => member.id == _selectedFamilyMemberId)
            .firstOrNull;

        if (selectedLocation != null) {
          bloc.add(LocationSelected(location: selectedLocation));
        }
        if (selectedFamilyMember != null) {
          bloc.add(FamilyMemberSelected(member: selectedFamilyMember));
        }
      }

      // Submit the medicine
      bloc.add(MedicineSubmitted(context: context));
    }
  }

  Widget _buildDosageFormSelection() {
    return BlocBuilder<AddMedicineBloc, AddMedicineState>(
      builder: (context, state) {
        if (state is! AddMedicineFormState) {
          return const SizedBox.shrink();
        }

        // Only show dosage/form selection if a medicine name is selected or in custom mode
        if (state.selectedMedicineName == null && !state.isCustomMode) {
          return const SizedBox.shrink();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Show dropdowns for database medicines or when final selection is made
            if (state.availableDosageForms.isNotEmpty ||
                state.selectedMedicine != null) ...[
              // 2-column layout: Labels column and Dropdowns column
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Column 1: Field titles
                  Expanded(
                    flex: 1,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(top: 16, bottom: 8),
                          child: Text(
                            'Dosage *',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.navy,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        const SizedBox(height: 48), // Space for dropdown height
                        // Form label - aligned with dropdown
                        Container(
                          height: 56, // Match dropdown height
                          alignment: Alignment.centerLeft,
                          child: Text(
                            'Forme *',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.navy,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        // Presentation label - aligned with dropdown
                        Container(
                          height: 56, // Match dropdown height
                          alignment: Alignment.centerLeft,
                          child: Text(
                            'Présentation *',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.navy,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),

                  // Column 2: Dropdowns
                  Expanded(
                    flex: 2,
                    child: Column(
                      children: [
                        // Dosage dropdown
                        ModernDropdown<String>(
                          hint: 'Sélectionner dosage',
                          value: _dosageController.text.isNotEmpty
                              ? _dosageController.text
                              : null,
                          items: state.availableDosageForms.isNotEmpty
                              ? state.availableDosageForms
                                  .map((medicine) => medicine.dosage)
                                  .where((dosage) =>
                                      dosage != null && dosage.isNotEmpty)
                                  .toSet()
                                  .map((dosage) => DropdownMenuItem<String>(
                                        value: dosage,
                                        child: Text(dosage!),
                                      ))
                                  .toList()
                              : [
                                  DropdownMenuItem<String>(
                                    value: _dosageController.text,
                                    child: Text(_dosageController.text),
                                  )
                                ],
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _dosageController.text = value;
                              });
                              if (state.availableDosageForms.isNotEmpty) {
                                _updateFormSelection(state);
                              }
                            }
                          },
                        ),
                        const SizedBox(height: 16),

                        // Form dropdown
                        ModernDropdown<String>(
                          hint: 'Sélectionner forme',
                          value: _selectedForm,
                          items: state.availableDosageForms.isNotEmpty
                              ? () {
                                  final availableForms = state
                                      .availableDosageForms
                                      .map((medicine) => medicine.forme)
                                      .where((forme) =>
                                          forme != null && forme.isNotEmpty)
                                      .toSet()
                                      .toList();

                                  // Ensure selected form is in the list to prevent assertion error
                                  if (_selectedForm != null &&
                                      !availableForms.contains(_selectedForm)) {
                                    availableForms.add(_selectedForm!);
                                  }

                                  return availableForms
                                      .map((forme) => DropdownMenuItem<String>(
                                            value: forme,
                                            child: Text(forme!),
                                          ))
                                      .toList();
                                }()
                              : [
                                  if (_selectedForm != null)
                                    DropdownMenuItem<String>(
                                      value: _selectedForm,
                                      child: Text(_selectedForm!),
                                    )
                                ],
                          onChanged: (value) {
                            setState(() {
                              _selectedForm = value;
                            });
                            if (state.availableDosageForms.isNotEmpty) {
                              _updateFormSelection(state);
                            }
                          },
                        ),
                        const SizedBox(height: 16),

                        // Presentation dropdown
                        ModernDropdown<String>(
                          hint: 'Sélectionner présentation',
                          value: _selectedPresentation,
                          items: state.availableDosageForms.isNotEmpty
                              ? state.availableDosageForms
                                  .map((medicine) => medicine.presentation)
                                  .where((presentation) =>
                                      presentation != null &&
                                      presentation.isNotEmpty)
                                  .toSet()
                                  .map((presentation) =>
                                      DropdownMenuItem<String>(
                                        value: presentation,
                                        child: Text(presentation!),
                                      ))
                                  .toList()
                              : _getAvailablePresentations(),
                          onChanged: (value) {
                            setState(() {
                              _selectedPresentation = value;
                            });
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ] else if (state.isCustomMode) ...[
              // Custom mode - 2-column layout: Labels column and Fields column
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Column 1: Field titles
                  Expanded(
                    flex: 1,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(top: 16, bottom: 8),
                          child: Text(
                            'Dosage *',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.navy,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        const SizedBox(height: 48), // Space for field height
                        Padding(
                          padding: const EdgeInsets.only(top: 8, bottom: 8),
                          child: Text(
                            'Forme *',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.navy,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        const SizedBox(height: 48), // Space for dropdown height
                        Padding(
                          padding: const EdgeInsets.only(top: 8, bottom: 8),
                          child: Text(
                            'Présentation *',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.navy,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),

                  // Column 2: Fields
                  Expanded(
                    flex: 2,
                    child: Column(
                      children: [
                        // Dosage text field
                        ModernTextField(
                          controller: _dosageController,
                          label: 'Dosage *',
                          hint: 'Ex: 500mg, 10ml',
                          prefixIcon: const Icon(Icons.science),
                        ),
                        const SizedBox(height: 16),

                        // Form dropdown
                        ModernDropdown<String>(
                          label: 'Forme *',
                          hint: 'Sélectionner forme',
                          value: _selectedForm,
                          items: const [
                            DropdownMenuItem(
                                value: 'comprimé', child: Text('Comprimé')),
                            DropdownMenuItem(
                                value: 'gélule', child: Text('Gélule')),
                            DropdownMenuItem(
                                value: 'sirop', child: Text('Sirop')),
                            DropdownMenuItem(
                                value: 'injection', child: Text('Injection')),
                            DropdownMenuItem(
                                value: 'pommade', child: Text('Pommade')),
                            DropdownMenuItem(
                                value: 'gouttes', child: Text('Gouttes')),
                            DropdownMenuItem(
                                value: 'spray', child: Text('Spray')),
                            DropdownMenuItem(
                                value: 'patch', child: Text('Patch')),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedForm = value;
                            });
                          },
                        ),
                        const SizedBox(height: 16),

                        // Presentation dropdown
                        ModernDropdown<String>(
                          label: 'Présentation *',
                          hint: 'Sélectionner présentation',
                          value: _selectedPresentation,
                          items: _getAvailablePresentations(),
                          onChanged: (value) {
                            setState(() {
                              _selectedPresentation = value;
                            });
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ],
        );
      },
    );
  }

  Widget _buildMedicineSearchField() {
    return BlocBuilder<AddMedicineBloc, AddMedicineState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search field
            ModernTextField(
              label: 'Nom du médicament *',
              hint: 'Ex: Paracétamol, Doliprane...',
              controller: _nameController,
              keyboardType: TextInputType.text,
              onChanged: _onSearchChanged,
              suffixIcon: state is AddMedicineFormState && state.isSearching
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: Padding(
                        padding: EdgeInsets.all(12),
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    )
                  : _nameController.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: _clearSearch,
                        )
                      : null,
            ),

            // Search results or custom medicine option
            if (state is AddMedicineFormState &&
                _nameController.text.isNotEmpty &&
                state.selectedMedicineName == null)
              Container(
                margin: const EdgeInsets.only(top: 8),
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: AppColors.grey200),
                ),
                child: Column(
                  children: [
                    // Show search results if available
                    if (state.searchResults.isNotEmpty) ...[
                      ...state.searchResults.take(5).map(
                          (medicine) => _buildMedicineSearchResult(medicine)),
                      if (state.searchResults.length > 5)
                        Container(
                          padding: const EdgeInsets.all(12),
                          child: Text(
                            '${state.searchResults.length - 5} autres résultats...',
                            style: AppTextStyles.bodySmall.copyWith(
                              color: AppColors.grey500,
                            ),
                          ),
                        ),
                    ] else ...[
                      // Only show custom medicine option when no search results are found
                      _buildCustomMedicineOption(),
                    ],
                  ],
                ),
              ),

            // Compact selected medicine name display
            if (state is AddMedicineFormState &&
                state.selectedMedicineName != null)
              Container(
                margin: const EdgeInsets.only(top: 8),
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: AppColors.tealExtraLight,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.teal, width: 1),
                ),
                child: Row(
                  children: [
                    Icon(Icons.check_circle, color: AppColors.teal, size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '${state.selectedMedicineName!} - A été sélectionné',
                        style: AppTextStyles.bodySmall.copyWith(
                          fontWeight: FontWeight.w500,
                          color: AppColors.navy,
                        ),
                      ),
                    ),
                    InkWell(
                      onTap: _clearSelection,
                      child: Padding(
                        padding: const EdgeInsets.all(4),
                        child: Icon(
                          Icons.close,
                          size: 16,
                          color: AppColors.grey600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildMedicineSearchResult(TunisiaMedicine medicine) {
    return InkWell(
      onTap: () => _selectMedicine(medicine),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(color: AppColors.grey100),
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    medicine.nom,
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (medicine.laboratoire != null)
                    Text(
                      medicine.laboratoire!,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.grey500,
                      ),
                    ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: AppColors.grey400,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomMedicineOption() {
    return InkWell(
      onTap: _switchToCustomMode,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border(
            top: BorderSide(color: AppColors.grey200, width: 2),
          ),
          color: AppColors.grey50,
        ),
        child: Row(
          children: [
            Icon(
              Icons.add_circle_outline,
              color: AppColors.teal,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Ajouter "${_nameController.text}" comme médicament personnalisé',
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w500,
                      color: AppColors.teal,
                    ),
                  ),
                  Text(
                    'Ce médicament ne sera pas lié à la base de données officielle',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.grey600,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: AppColors.teal,
            ),
          ],
        ),
      ),
    );
  }

  void _switchToCustomMode() {
    // Use the BLoC to handle custom mode properly
    context.read<AddMedicineBloc>().add(
          ModeToggled(
            isCustomMode: true,
            customName:
                _nameController.text.isNotEmpty ? _nameController.text : null,
          ),
        );

    setState(() {
      _isCustomMode = true;
      _selectedMedicine = null;
    });
  }

  void _onSearchChanged(String query) {
    _searchDebounceTimer?.cancel();

    if (query.trim().isEmpty) {
      context.read<AddMedicineBloc>().add(const SearchCleared());
      return;
    }

    if (query.length >= 2) {
      _searchDebounceTimer = Timer(const Duration(milliseconds: 500), () {
        context.read<AddMedicineBloc>().add(
              MedicinesSearched(query: query),
            );
      });
    }
  }

  void _clearSearch() {
    _nameController.clear();
    context.read<AddMedicineBloc>().add(const SearchCleared());
    context.read<AddMedicineBloc>().add(const ModeToggled(isCustomMode: false));
    setState(() {
      _selectedMedicine = null;
      _isCustomMode = false;
    });
  }

  void _selectMedicine(TunisiaMedicine medicine) {
    setState(() {
      _selectedMedicine = null; // Clear selected medicine to show dropdowns
      _nameController.text = medicine.nom;
      _isCustomMode = false;

      // Clear dosage, form, and presentation so user can select from dropdowns
      _dosageController.clear();
      _selectedForm = null;
      _selectedPresentation = null;
    });

    // Switch to database mode and trigger medicine name selection in BLoC
    context.read<AddMedicineBloc>().add(const ModeToggled(isCustomMode: false));
    context.read<AddMedicineBloc>().add(
          MedicineNameSelected(medicineName: medicine.nom),
        );
  }

  void _clearSelection() {
    setState(() {
      _selectedMedicine = null;
      _nameController.clear();
      _dosageController.clear();
      _selectedForm = null;
      _selectedPresentation = null;
    });
    context.read<AddMedicineBloc>().add(const SearchCleared());
  }

  List<DropdownMenuItem<String>> _getAvailablePresentations() {
    // Comprehensive list of presentation types from Tunisia medicines database
    const presentations = [
      'Boîte',
      'Flacon',
      'Tube',
      'Ampoule',
      'Sachet',
      'Plaquette',
      'Comprimé',
      'Gélule',
      'Capsule',
      'Suppositoire',
      'Ovule',
      'Pommade',
      'Crème',
      'Gel',
      'Solution',
      'Suspension',
      'Sirop',
      'Gouttes',
      'Spray',
      'Inhalateur',
      'Patch',
      'Seringue',
      'Stylo',
      'Cartouche',
    ];

    return presentations
        .map((presentation) => DropdownMenuItem<String>(
              value: presentation,
              child: Text(presentation),
            ))
        .toList();
  }
}
