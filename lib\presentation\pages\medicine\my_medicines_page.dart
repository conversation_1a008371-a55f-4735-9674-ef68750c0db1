import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/debug_logger.dart';
import '../../../domain/entities/medicine.dart';
import '../../bloc/my_medicines/my_medicines_bloc.dart';
import '../../bloc/my_medicines/my_medicines_event.dart';
import '../../bloc/my_medicines/my_medicines_state.dart';

import '../../widgets/medicine/medicine_grid_item.dart';
import '../../widgets/medicine/medicine_filter_bar.dart';
import '../../widgets/medicine/medicine_sort_dialog.dart';
import '../../widgets/medicine/bulk_actions_bar.dart';
import '../../widgets/common/category_card.dart' show MedicineCard;

class MyMedicinesPage extends StatefulWidget {
  final String householdId;
  final String? initialFilter; // Add support for initial filter from URL

  const MyMedicinesPage({
    super.key,
    required this.householdId,
    this.initialFilter,
  });

  @override
  State<MyMedicinesPage> createState() => _MyMedicinesPageState();
}

class _MyMedicinesPageState extends State<MyMedicinesPage> {
  final TextEditingController _searchController = TextEditingController();
  MedicineFilter? _pendingFilter;

  @override
  void initState() {
    super.initState();

    DebugLogger.logUI('MyMedicinesPage', 'initState', data: {
      'householdId': widget.householdId,
      'initialFilter': widget.initialFilter,
    });

    // Store pending filter if provided from URL
    if (widget.initialFilter != null) {
      _pendingFilter = _parseFilterFromString(widget.initialFilter!);

      DebugLogger.logUI('MyMedicinesPage', 'Storing pending filter', data: {
        'filterString': widget.initialFilter!,
        'parsedFilter': _pendingFilter.toString(),
        'isAllFilter': _pendingFilter == MedicineFilter.all,
      });
    }

    // Initialize medicines
    context.read<MyMedicinesBloc>().add(
          MyMedicinesInitialized(householdId: widget.householdId),
        );
  }

  /// Parse filter string from URL parameter to MedicineFilter enum
  MedicineFilter _parseFilterFromString(String filterString) {
    switch (filterString.toLowerCase()) {
      case 'expired':
        return MedicineFilter.expired;
      case 'expiring_soon':
      case 'expiring':
        return MedicineFilter.expiringSoon;
      case 'low_stock':
        return MedicineFilter.lowStock;
      case 'out_of_stock':
        return MedicineFilter.outOfStock;
      case 'custom':
        return MedicineFilter.custom;
      case 'prescription':
        return MedicineFilter.prescription;
      case 'all':
      default:
        return MedicineFilter.all;
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.teal,
      body: BlocListener<MyMedicinesBloc, MyMedicinesState>(
        listener: (context, state) {
          // Apply pending filter when medicines are loaded
          if (state is MyMedicinesLoaded &&
              _pendingFilter != null &&
              _pendingFilter != MedicineFilter.all) {
            DebugLogger.logUI(
                'MyMedicinesPage', 'Applying pending filter after load',
                data: {
                  'pendingFilter': _pendingFilter.toString(),
                  'medicinesCount': state.medicines.length,
                });

            // Apply the pending filter
            context.read<MyMedicinesBloc>().add(
                  MyMedicinesFiltered(filter: _pendingFilter!),
                );

            // Clear the pending filter so it's only applied once
            _pendingFilter = null;
          }

          if (state is MyMedicinesOperationSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.success,
                behavior: SnackBarBehavior.floating,
                duration: const Duration(seconds: 3),
              ),
            );
          } else if (state is MyMedicinesError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
                behavior: SnackBarBehavior.floating,
                duration: const Duration(seconds: 5),
                action: SnackBarAction(
                  label: 'Fermer',
                  textColor: Colors.white,
                  onPressed: () {
                    ScaffoldMessenger.of(context).hideCurrentSnackBar();
                  },
                ),
              ),
            );
          } else if (state is MyMedicinesDeletionLoading) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(state.message),
                          const SizedBox(height: 4),
                          Text(
                            '${state.currentIndex}/${state.totalCount}',
                            style: const TextStyle(
                                fontSize: 12, color: Colors.white70),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                backgroundColor: AppColors.teal,
                behavior: SnackBarBehavior.floating,
                duration: const Duration(seconds: 1),
              ),
            );
          }
        },
        child: Column(
          children: [
            // Header with teal background
            Container(
              padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () => context.pop(),
                    child: Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.arrow_back,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      'Mes Médicaments',
                      style: AppTextStyles.headlineMedium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Content container with white background
            Expanded(
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(24),
                    topRight: Radius.circular(24),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, -4),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // Search bar
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: TextField(
                        controller: _searchController,
                        decoration: InputDecoration(
                          hintText: 'Rechercher un médicament...',
                          prefixIcon: const Icon(Icons.search),
                          suffixIcon: _searchController.text.isNotEmpty
                              ? IconButton(
                                  onPressed: () {
                                    _searchController.clear();
                                    context.read<MyMedicinesBloc>().add(
                                          const MyMedicinesSearchCleared(),
                                        );
                                  },
                                  icon: const Icon(Icons.clear),
                                )
                              : null,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: AppColors.grey300),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: AppColors.teal),
                          ),
                        ),
                        onChanged: (value) {
                          context.read<MyMedicinesBloc>().add(
                                MyMedicinesSearched(query: value),
                              );
                        },
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Filter bar
                    BlocBuilder<MyMedicinesBloc, MyMedicinesState>(
                      builder: (context, state) {
                        if (state is MyMedicinesLoaded) {
                          return MedicineFilterBar(
                            currentFilter: state.currentFilter,
                            onFilterChanged: (filter) {
                              context.read<MyMedicinesBloc>().add(
                                    MyMedicinesFiltered(filter: filter),
                                  );
                            },
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),

                    const SizedBox(height: 16),

                    // Bulk actions bar (when in selection mode)
                    BlocBuilder<MyMedicinesBloc, MyMedicinesState>(
                      builder: (context, state) {
                        if (state is MyMedicinesLoaded &&
                            state.isSelectionMode) {
                          return BulkActionsBar(
                            selectedCount: state.selectedCount,
                            onSelectAll: () {
                              context.read<MyMedicinesBloc>().add(
                                    AllMedicinesSelected(
                                        selectAll: !state.areAllSelected),
                                  );
                            },
                            onClearSelection: () {
                              context.read<MyMedicinesBloc>().add(
                                    const MedicineSelectionCleared(),
                                  );
                            },
                            onDeleteSelected: () {
                              _showBulkDeleteConfirmation(
                                  context, state.selectedCount);
                            },
                            areAllSelected: state.areAllSelected,
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),

                    // Medicines list/grid
                    Expanded(
                      child: BlocBuilder<MyMedicinesBloc, MyMedicinesState>(
                        builder: (context, state) {
                          if (state is MyMedicinesLoading) {
                            return const Center(
                              child: CircularProgressIndicator(),
                            );
                          }

                          if (state is MyMedicinesDeletionLoading) {
                            return Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  CircularProgressIndicator(
                                    color: AppColors.teal,
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    state.message,
                                    style: AppTextStyles.bodyMedium,
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Progression: ${state.currentIndex}/${state.totalCount}',
                                    style: AppTextStyles.bodySmall.copyWith(
                                      color: AppColors.grey600,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }

                          if (state is MyMedicinesError) {
                            return _buildErrorState(context, state.message);
                          }

                          if (state is MyMedicinesLoaded) {
                            if (state.isEmpty) {
                              return _buildEmptyState(context);
                            }

                            if (state.isFilteredEmpty) {
                              return _buildNoResultsState(context, state);
                            }

                            return RefreshIndicator(
                              onRefresh: () async {
                                context.read<MyMedicinesBloc>().add(
                                      const MyMedicinesRefreshed(),
                                    );
                              },
                              child: state.viewMode == MedicineViewMode.list
                                  ? _buildListView(context, state)
                                  : _buildGridView(context, state),
                            );
                          }

                          return const SizedBox.shrink();
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }


 Widget _buildListView(BuildContext context, MyMedicinesLoaded state) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: state.displayMedicines.length,
      itemBuilder: (context, index) {
        final medicine = state.displayMedicines[index];
        final isSelected = state.selectedMedicineIds.contains(medicine.id);

        // Use the standardized MedicineCard from common/category_card.dart
        // to ensure visual consistency with Today's card specs.
        return MedicineCard(
          medicineName: medicine.name,
          dosage: _composeDosageForm(medicine),
          // Optional legacy info retained
          frequency: 'Selon prescription',
          nextDose: _getNextDoseTextForMyMedicines(medicine),
          // Dynamic status color preserved
          statusColor: _statusColorFromEntity(medicine),
          // New standardized fields
          expiryText: _composeExpiry(medicine),
          locationText: _composeLocation(medicine),
          onTap: () => _handleMedicineTap(context, medicine, state),
          onEditTap: () => _editMedicine(context, medicine),
          onDeleteTap: () => _deleteMedicine(context, medicine),
        );
      },
    );
  }

  Widget _buildGridView(BuildContext context, MyMedicinesLoaded state) {
    return GridView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: state.displayMedicines.length,
      itemBuilder: (context, index) {
        final medicine = state.displayMedicines[index];
        final isSelected = state.selectedMedicineIds.contains(medicine.id);

        // For grid view keep existing grid item until standardized separately
        return MedicineGridItem(
          medicine: medicine,
          isSelected: isSelected,
          isSelectionMode: state.isSelectionMode,
          onTap: () => _handleMedicineTap(context, medicine, state),
          onLongPress: () => _handleMedicineLongPress(context, medicine),
        );
      },
    );
  }

  Widget _buildErrorState(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Erreur de chargement',
            style: AppTextStyles.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.grey600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              context.read<MyMedicinesBloc>().add(
                    MyMedicinesInitialized(householdId: widget.householdId),
                  );
            },
            child: const Text('Réessayer'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.medication_outlined,
            size: 64,
            color: AppColors.grey400,
          ),
          const SizedBox(height: 16),
          Text(
            'Aucun médicament',
            style: AppTextStyles.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'Commencez par ajouter vos premiers médicaments',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.grey600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => context.push('/medicines/add'),
            icon: const Icon(Icons.add),
            label: const Text('Ajouter un médicament'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.teal,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoResultsState(BuildContext context, MyMedicinesLoaded state) {
    String title = 'Aucun résultat';
    String subtitle = '';

    if (state.hasSearchQuery && state.hasActiveFilter) {
      subtitle =
          'Aucun médicament trouvé pour "${state.searchQuery}" avec le filtre "${state.currentFilter.displayName}"';
    } else if (state.hasSearchQuery) {
      subtitle = 'Aucun médicament trouvé pour "${state.searchQuery}"';
    } else if (state.hasActiveFilter) {
      subtitle =
          'Aucun médicament avec le filtre "${state.currentFilter.displayName}"';
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: AppColors.grey400,
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: AppTextStyles.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.grey600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _handleMedicineTap(
    BuildContext context,
    Medicine medicine,
    MyMedicinesLoaded state,
  ) {
    if (state.isSelectionMode) {
      final isSelected = state.selectedMedicineIds.contains(medicine.id);
      context.read<MyMedicinesBloc>().add(
            MedicineSelected(
              medicineId: medicine.id,
              isSelected: !isSelected,
            ),
          );
    } else {
      // Navigate to medicine details
      context.push('/medicines/${medicine.id}');
    }
  }

  void _handleMedicineLongPress(BuildContext context, Medicine medicine) {
    context.read<MyMedicinesBloc>().add(
          MedicineSelected(
            medicineId: medicine.id,
            isSelected: true,
          ),
        );
  }

  void _editMedicine(BuildContext context, Medicine medicine) {
    context.push('/medicines/${medicine.id}/edit');
  }

  void _deleteMedicine(BuildContext context, Medicine medicine) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer le médicament'),
        content: Text(
          'Êtes-vous sûr de vouloir supprimer "${medicine.name}" ?\n\n'
          'Cette action est irréversible.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<MyMedicinesBloc>().add(
                    MedicineDeleted(medicineId: medicine.id),
                  );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }

  void _showSortDialog(BuildContext context, MyMedicinesLoaded state) {
    showDialog(
      context: context,
      builder: (context) => MedicineSortDialog(
        currentSort: state.currentSort,
        onSortChanged: (sortOption) {
          context.read<MyMedicinesBloc>().add(
                MyMedicinesSorted(sortOption: sortOption),
              );
        },
      ),
    );
  }

  // Helpers to compose standardized fields for MedicineCard
  String _composeDosageForm(Medicine medicine) {
    final parts = <String>[];
    if ((medicine.dosage ?? '').trim().isNotEmpty) parts.add(medicine.dosage!.trim());
    if ((medicine.form ?? '').trim().isNotEmpty) parts.add(medicine.form!.trim());
    return parts.isEmpty ? 'Non spécifié' : parts.join(' ');
  }

  String? _composeExpiry(Medicine medicine) {
    if (medicine.expiration == null) return null;
    final dd = medicine.expiration!.day.toString().padLeft(2, '0');
    final mm = medicine.expiration!.month.toString().padLeft(2, '0');
    final yyyy = medicine.expiration!.year.toString();
    return 'Expiration: $dd/$mm/$yyyy';
  }

  String? _composeLocation(Medicine medicine) {
    final label = (medicine.locationName ?? medicine.location ?? '').trim();
    return label.isEmpty ? null : 'Lieu: $label';
  }

  // Localized helper to compute next dose placeholder for MyMedicines page context
  String? _getNextDoseTextForMyMedicines(Medicine medicine) {
    // TODO: Replace with real next dose computation when reminder data is wired here
    return null; // keep hidden if unknown
  }

  Color _statusColorFromEntity(Medicine medicine) {
    switch (medicine.status) {
      case MedicineStatus.expired:
        return AppColors.error;
      case MedicineStatus.expiringSoon:
        return AppColors.info;
      case MedicineStatus.lowStock:
        return AppColors.warning;
      case MedicineStatus.outOfStock:
        return AppColors.grey600;
      case MedicineStatus.normal:
        return AppColors.success;
    }
  }

  void _showBulkDeleteConfirmation(BuildContext context, int count) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer les médicaments'),
        content: Text(
          'Êtes-vous sûr de vouloir supprimer $count médicament${count > 1 ? 's' : ''} ?\n\n'
          'Cette action est irréversible.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<MyMedicinesBloc>().add(
                    const SelectedMedicinesDeleted(),
                  );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }
}
