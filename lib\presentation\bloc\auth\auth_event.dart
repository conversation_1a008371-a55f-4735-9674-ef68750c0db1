import 'package:equatable/equatable.dart';

/// Authentication events matching web app AuthContext functionality
abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

/// Initialize authentication - check for existing session
class AuthInitializeRequested extends AuthEvent {}

/// Sign in with email and password
class AuthSignInRequested extends AuthEvent {
  final String email;
  final String password;
  final bool rememberMe;

  const AuthSignInRequested({
    required this.email,
    required this.password,
    this.rememberMe = false,
  });

  @override
  List<Object?> get props => [email, password, rememberMe];
}

/// Sign up with email, password, and name
class AuthSignUpRequested extends AuthEvent {
  final String email;
  final String password;
  final String name;

  const AuthSignUpRequested({
    required this.email,
    required this.password,
    required this.name,
  });

  @override
  List<Object?> get props => [email, password, name];
}

/// Sign out current user
class AuthSignOutRequested extends AuthEvent {}

/// Refresh user profile and household data
class AuthRefreshProfileRequested extends AuthEvent {}

/// Create a new household for the user
class AuthCreateHouseholdRequested extends AuthEvent {
  final String householdName;

  const AuthCreateHouseholdRequested({required this.householdName});

  @override
  List<Object?> get props => [householdName];
}

/// Update household name
class AuthUpdateHouseholdRequested extends AuthEvent {
  final String householdName;

  const AuthUpdateHouseholdRequested({required this.householdName});

  @override
  List<Object?> get props => [householdName];
}

/// Reset password for email
class AuthPasswordResetRequested extends AuthEvent {
  final String email;

  const AuthPasswordResetRequested({required this.email});

  @override
  List<Object?> get props => [email];
}

/// Handle auth state change from Supabase
class AuthStateChanged extends AuthEvent {
  final bool isAuthenticated;
  final String? userId;

  const AuthStateChanged({
    required this.isAuthenticated,
    this.userId,
  });

  @override
  List<Object?> get props => [isAuthenticated, userId];
}
