import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';

/// Calendar picker component matching the mockup design
class CalendarPicker extends StatefulWidget {
  final DateTime? selectedDate;
  final Function(DateTime)? onDateSelected;
  final DateTime? minDate;
  final DateTime? maxDate;
  final List<DateTime>? disabledDates;

  const CalendarPicker({
    super.key,
    this.selectedDate,
    this.onDateSelected,
    this.minDate,
    this.maxDate,
    this.disabledDates,
  });

  @override
  State<CalendarPicker> createState() => _CalendarPickerState();
}

class _CalendarPickerState extends State<CalendarPicker> {
  late DateTime _currentMonth;
  DateTime? _selectedDate;

  @override
  void initState() {
    super.initState();
    _currentMonth = widget.selectedDate ?? DateTime.now();
    _selectedDate = widget.selectedDate;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 16,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            _buildHeader(),
            const SizedBox(height: 24),
            
            // Month navigation
            _buildMonthNavigation(),
            const SizedBox(height: 20),
            
            // Weekday headers
            _buildWeekdayHeaders(),
            const SizedBox(height: 12),
            
            // Calendar grid
            _buildCalendarGrid(),
            const SizedBox(height: 24),
            
            // Confirm button
            _buildConfirmButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Text(
      'Book your Date',
      style: AppTextStyles.headlineSmall.copyWith(
        color: AppColors.navy,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildMonthNavigation() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        IconButton(
          onPressed: () {
            setState(() {
              _currentMonth = DateTime(_currentMonth.year, _currentMonth.month - 1);
            });
          },
          icon: Icon(
            Icons.chevron_left,
            color: AppColors.navy,
            size: 28,
          ),
        ),
        
        Text(
          _getMonthYearText(_currentMonth),
          style: AppTextStyles.titleLarge.copyWith(
            color: AppColors.navy,
            fontWeight: FontWeight.w600,
          ),
        ),
        
        IconButton(
          onPressed: () {
            setState(() {
              _currentMonth = DateTime(_currentMonth.year, _currentMonth.month + 1);
            });
          },
          icon: Icon(
            Icons.chevron_right,
            color: AppColors.navy,
            size: 28,
          ),
        ),
      ],
    );
  }

  Widget _buildWeekdayHeaders() {
    const weekdays = ['M', 'T', 'W', 'T', 'F', 'S', 'S'];
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: weekdays.map((day) => 
        SizedBox(
          width: 40,
          child: Text(
            day,
            style: AppTextStyles.labelMedium.copyWith(
              color: AppColors.grey600,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ).toList(),
    );
  }

  Widget _buildCalendarGrid() {
    final daysInMonth = DateTime(_currentMonth.year, _currentMonth.month + 1, 0).day;
    final firstDayOfMonth = DateTime(_currentMonth.year, _currentMonth.month, 1);
    final firstWeekday = firstDayOfMonth.weekday;
    
    // Calculate previous month days to show
    final previousMonth = DateTime(_currentMonth.year, _currentMonth.month - 1);
    final daysInPreviousMonth = DateTime(previousMonth.year, previousMonth.month + 1, 0).day;
    
    List<Widget> dayWidgets = [];
    
    // Previous month days
    for (int i = firstWeekday - 1; i > 0; i--) {
      final day = daysInPreviousMonth - i + 1;
      dayWidgets.add(_buildDayCell(
        day,
        isCurrentMonth: false,
        date: DateTime(previousMonth.year, previousMonth.month, day),
      ));
    }
    
    // Current month days
    for (int day = 1; day <= daysInMonth; day++) {
      final date = DateTime(_currentMonth.year, _currentMonth.month, day);
      dayWidgets.add(_buildDayCell(
        day,
        isCurrentMonth: true,
        date: date,
      ));
    }
    
    // Next month days to fill the grid
    final remainingCells = 42 - dayWidgets.length; // 6 rows × 7 days
    final nextMonth = DateTime(_currentMonth.year, _currentMonth.month + 1);
    for (int day = 1; day <= remainingCells; day++) {
      dayWidgets.add(_buildDayCell(
        day,
        isCurrentMonth: false,
        date: DateTime(nextMonth.year, nextMonth.month, day),
      ));
    }
    
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 7,
      mainAxisSpacing: 8,
      crossAxisSpacing: 8,
      children: dayWidgets,
    );
  }

  Widget _buildDayCell(int day, {required bool isCurrentMonth, required DateTime date}) {
    final isSelected = _selectedDate != null &&
        date.year == _selectedDate!.year &&
        date.month == _selectedDate!.month &&
        date.day == _selectedDate!.day;
    
    final isToday = date.year == DateTime.now().year &&
        date.month == DateTime.now().month &&
        date.day == DateTime.now().day;
    
    final isDisabled = widget.disabledDates?.any((disabledDate) =>
        date.year == disabledDate.year &&
        date.month == disabledDate.month &&
        date.day == disabledDate.day) ?? false;

    return GestureDetector(
      onTap: isCurrentMonth && !isDisabled
          ? () {
              setState(() {
                _selectedDate = date;
              });
              widget.onDateSelected?.call(date);
            }
          : null,
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.teal
              : isToday
                  ? AppColors.tealLight.withValues(alpha: 0.3)
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          border: isToday && !isSelected
              ? Border.all(color: AppColors.teal, width: 1)
              : null,
        ),
        child: Center(
          child: Text(
            day.toString(),
            style: AppTextStyles.bodyMedium.copyWith(
              color: isSelected
                  ? Colors.white
                  : isCurrentMonth
                      ? isDisabled
                          ? AppColors.grey400
                          : AppColors.navy
                      : AppColors.grey400,
              fontWeight: isSelected || isToday
                  ? FontWeight.w600
                  : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildConfirmButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _selectedDate != null
            ? () {
                Navigator.of(context).pop(_selectedDate);
              }
            : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.teal,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
        ),
        child: Text(
          'CONFIRM',
          style: AppTextStyles.titleMedium.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
            letterSpacing: 0.5,
          ),
        ),
      ),
    );
  }

  String _getMonthYearText(DateTime date) {
    const months = [
      'JANUARY', 'FEBRUARY', 'MARCH', 'APRIL', 'MAY', 'JUNE',
      'JULY', 'AUGUST', 'SEPTEMBER', 'OCTOBER', 'NOVEMBER', 'DECEMBER'
    ];
    return '${months[date.month - 1]} ${date.year}';
  }
}

/// Time slot picker component
class TimeSlotPicker extends StatefulWidget {
  final List<String>? selectedTimes;
  final Function(List<String>)? onTimesSelected;
  final bool allowMultipleSelection;

  const TimeSlotPicker({
    super.key,
    this.selectedTimes,
    this.onTimesSelected,
    this.allowMultipleSelection = true,
  });

  @override
  State<TimeSlotPicker> createState() => _TimeSlotPickerState();
}

class _TimeSlotPickerState extends State<TimeSlotPicker> {
  List<String> _selectedTimes = [];

  @override
  void initState() {
    super.initState();
    _selectedTimes = widget.selectedTimes ?? [];
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 16,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              'Your Date',
              style: AppTextStyles.headlineSmall.copyWith(
                color: AppColors.navy,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 8),
            
            Text(
              '00 Month',
              style: AppTextStyles.titleMedium.copyWith(
                color: AppColors.grey600,
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Morning section
            _buildTimeSection('Morning', _morningTimes),
            
            const SizedBox(height: 24),
            
            // Afternoon section
            _buildTimeSection('Afternoon', _afternoonTimes),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeSection(String title, List<String> times) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTextStyles.titleMedium.copyWith(
            color: AppColors.navy,
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const SizedBox(height: 12),
        
        Wrap(
          spacing: 12,
          runSpacing: 8,
          children: times.map((time) => _buildTimeChip(time)).toList(),
        ),
      ],
    );
  }

  Widget _buildTimeChip(String time) {
    final isSelected = _selectedTimes.contains(time);
    
    return GestureDetector(
      onTap: () {
        setState(() {
          if (widget.allowMultipleSelection) {
            if (isSelected) {
              _selectedTimes.remove(time);
            } else {
              _selectedTimes.add(time);
            }
          } else {
            _selectedTimes = [time];
          }
        });
        widget.onTimesSelected?.call(_selectedTimes);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.teal : AppColors.grey100,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          time,
          style: AppTextStyles.bodyMedium.copyWith(
            color: isSelected ? Colors.white : AppColors.navy,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  static const List<String> _morningTimes = [
    '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30'
  ];

  static const List<String> _afternoonTimes = [
    '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30',
    '16:00', '16:30', '17:00', '17:30', '18:00'
  ];
}
