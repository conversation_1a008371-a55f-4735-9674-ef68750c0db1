import 'package:equatable/equatable.dart';
import '../../../domain/entities/household.dart';

/// Base class for household states
abstract class HouseholdState extends Equatable {
  const HouseholdState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class HouseholdInitial extends HouseholdState {
  const HouseholdInitial();
}

/// Loading state
class HouseholdLoading extends HouseholdState {
  const HouseholdLoading();
}

/// Success state for household creation
class HouseholdCreated extends HouseholdState {
  final Household household;

  const HouseholdCreated(this.household);

  @override
  List<Object?> get props => [household];
}

/// Success state for household joining
class HouseholdJoined extends HouseholdState {
  final HouseholdMember member;
  final Household household;

  const HouseholdJoined({
    required this.member,
    required this.household,
  });

  @override
  List<Object?> get props => [member, household];
}

/// Success state for household found by invite code
class HouseholdFound extends HouseholdState {
  final Household household;

  const HouseholdFound(this.household);

  @override
  List<Object?> get props => [household];
}

/// Success state for invite code validation
class InviteCodeValid extends HouseholdState {
  final Household household;

  const InviteCodeValid(this.household);

  @override
  List<Object?> get props => [household];
}

/// Error state for invalid invite code
class InviteCodeInvalid extends HouseholdState {
  final String message;

  const InviteCodeInvalid(this.message);

  @override
  List<Object?> get props => [message];
}

/// Error state
class HouseholdError extends HouseholdState {
  final String message;

  const HouseholdError(this.message);

  @override
  List<Object?> get props => [message];
}
