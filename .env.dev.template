# MedyTrack Mobile - Development Environment Template
# Copy this file to .env.dev and fill in your actual values
# NEVER commit the actual .env.dev file to version control!

# Supabase Configuration (Development)
SUPABASE_URL=https://your-dev-project.supabase.co
SUPABASE_ANON_KEY=your-dev-anon-key-here
SUPABASE_SERVICE_KEY=your-dev-service-key-here

# App Configuration
APP_NAME=MedyTrack Mobile (Dev)
APP_VERSION=0.5.1
APP_BUILD_NUMBER=1

# Feature Flags (Development)
ENABLE_DEBUG_PAGE=true
ENABLE_VERBOSE_LOGGING=true
ENABLE_NOTIFICATIONS=true
ENABLE_BIOMETRIC_AUTH=true
ENABLE_OFFLINE_MODE=true
ENABLE_ANALYTICS=false

# API Configuration
API_TIMEOUT_SECONDS=30
MAX_RETRY_ATTEMPTS=3

# Development-specific Settings
ENABLE_MOCK_DATA=false
ENABLE_PERFORMANCE_OVERLAY=false
ENABLE_WIDGET_INSPECTOR=true
