import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';
import '../../utils/logger.dart';

abstract class AuthRemoteDataSource {
  Future<UserModel> signIn(String email, String password);
  Future<void> signUp(String email, String password, String name);
  Future<void> signOut();
  Future<UserModel> getCurrentUser();
  Future<UserModel> refreshProfile();
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final SupabaseClient supabaseClient;

  AuthRemoteDataSourceImpl({required this.supabaseClient});

  @override
  Future<UserModel> signIn(String email, String password) async {
    try {
      final response = await supabaseClient.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user == null) {
        throw Exception('Échec de la connexion');
      }

      // Fetch user profile
      return await _fetchUserProfile(response.user!.id);
    } catch (e) {
      throw Exception('Erreur de connexion: ${e.toString()}');
    }
  }

  @override
  Future<void> signUp(String email, String password, String name) async {
    try {
      final response = await supabaseClient.auth.signUp(
        email: email,
        password: password,
        data: {'name': name},
      );

      if (response.user == null) {
        throw Exception('Échec de l\'inscription');
      }
    } catch (e) {
      throw Exception('Erreur d\'inscription: ${e.toString()}');
    }
  }

  @override
  Future<void> signOut() async {
    try {
      await supabaseClient.auth.signOut();
    } catch (e) {
      throw Exception('Erreur de déconnexion: ${e.toString()}');
    }
  }

  @override
  Future<UserModel> getCurrentUser() async {
    try {
      final user = supabaseClient.auth.currentUser;
      if (user == null) {
        throw Exception('Aucun utilisateur connecté');
      }

      // Light session validation - only check if user object exists
      AppLogger.auth(
          '✅ AuthRemoteDataSource: User session found, proceeding with profile fetch');

      return await _fetchUserProfile(user.id);
    } catch (e) {
      throw Exception(
          'Erreur de récupération de l\'utilisateur: ${e.toString()}');
    }
  }

  @override
  Future<UserModel> refreshProfile() async {
    try {
      final user = supabaseClient.auth.currentUser;
      if (user == null) {
        throw Exception('Aucun utilisateur connecté');
      }

      return await _fetchUserProfile(user.id);
    } catch (e) {
      throw Exception('Erreur de rafraîchissement du profil: ${e.toString()}');
    }
  }

  Future<UserModel> _fetchUserProfile(String userId) async {
    try {
      AppLogger.auth('🔍 Fetching user profile for: $userId');

      // Fetch profile data
      final profileResponse = await supabaseClient
          .from('profiles')
          .select('*')
          .eq('id', userId)
          .maybeSingle();

      AppLogger.auth(
          '📋 Profile data: ${profileResponse != null ? 'found' : 'not found'}');

      // Fetch user data
      final userResponse = await supabaseClient
          .from('users')
          .select('household_id, expiry_warning_days')
          .eq('id', userId)
          .maybeSingle();

      AppLogger.auth(
          '👤 User data: ${userResponse != null ? 'found' : 'not found'}');
      AppLogger.auth(
          '🏠 Household ID from DB: ${userResponse?['household_id']}');

      // Fetch household data if available
      String? householdName;
      if (userResponse?['household_id'] != null) {
        final householdResponse = await supabaseClient
            .from('households')
            .select('name')
            .eq('id', userResponse!['household_id'])
            .maybeSingle();

        householdName = householdResponse?['name'];
      }

      final user = supabaseClient.auth.currentUser!;

      final userModel = UserModel(
        id: userId,
        email: user.email!,
        name: profileResponse?['name'],
        householdId: userResponse?['household_id'],
        householdName: householdName,
        isOnboardingCompleted:
            profileResponse?['onboarding_completed'] ?? false,
        expiryWarningDays: userResponse?['expiry_warning_days'] ?? 1,
        createdAt: DateTime.tryParse(user.createdAt),
        updatedAt: DateTime.tryParse(user.updatedAt ?? ''),
      );

      AppLogger.auth(
          '✅ UserModel created - Household ID: ${userModel.householdId}, Onboarding: ${userModel.isOnboardingCompleted}');

      return userModel;
    } catch (e) {
      throw Exception('Erreur de récupération du profil: ${e.toString()}');
    }
  }
}
