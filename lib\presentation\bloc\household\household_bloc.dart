import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../domain/entities/household.dart';
import '../../../domain/usecases/household/create_household_usecase.dart';
import '../../../domain/usecases/household/join_household_usecase.dart';
import '../../../domain/usecases/household/get_household_by_invite_code_usecase.dart';
import 'household_event.dart';
import 'household_state.dart';

/// BLoC for managing household operations
class HouseholdBloc extends Bloc<HouseholdEvent, HouseholdState> {
  final CreateHouseholdUseCase _createHouseholdUseCase;
  final JoinHouseholdUseCase _joinHouseholdUseCase;
  final GetHouseholdByInviteCodeUseCase _getHouseholdByInviteCodeUseCase;

  HouseholdBloc({
    required CreateHouseholdUseCase createHouseholdUseCase,
    required JoinHouseholdUseCase joinHouseholdUseCase,
    required GetHouseholdByInviteCodeUseCase getHouseholdByInviteCodeUseCase,
  })  : _createHouseholdUseCase = createHouseholdUseCase,
        _joinHouseholdUseCase = joinHouseholdUseCase,
        _getHouseholdByInviteCodeUseCase = getHouseholdByInviteCodeUseCase,
        super(const HouseholdInitial()) {
    on<CreateHouseholdEvent>(_onCreateHousehold);
    on<JoinHouseholdEvent>(_onJoinHousehold);
    on<GetHouseholdByInviteCodeEvent>(_onGetHouseholdByInviteCode);
    on<ValidateInviteCodeEvent>(_onValidateInviteCode);
    on<ResetHouseholdEvent>(_onResetHousehold);
  }

  Future<void> _onCreateHousehold(
    CreateHouseholdEvent event,
    Emitter<HouseholdState> emit,
  ) async {
    emit(const HouseholdLoading());

    final result = await _createHouseholdUseCase(
      CreateHouseholdParams(
        name: event.name,
        description: event.description,
        ownerId: event.ownerId,
      ),
    );

    result.fold(
      (failure) => emit(HouseholdError(failure.message)),
      (household) => emit(HouseholdCreated(household)),
    );
  }

  Future<void> _onJoinHousehold(
    JoinHouseholdEvent event,
    Emitter<HouseholdState> emit,
  ) async {
    emit(const HouseholdLoading());

    final result = await _joinHouseholdUseCase(
      JoinHouseholdParams(
        userId: event.userId,
        inviteCode: event.inviteCode,
      ),
    );

    result.fold(
      (failure) => emit(HouseholdError(failure.message)),
      (member) {
        // TODO: Get household details for the joined household
        // For now, we'll emit a simple success state
        emit(HouseholdJoined(
          member: member,
          household: Household(
            id: 'joined-household-id',
            name: 'Joined Household',
            ownerId: 'owner-id',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ));
      },
    );
  }

  Future<void> _onGetHouseholdByInviteCode(
    GetHouseholdByInviteCodeEvent event,
    Emitter<HouseholdState> emit,
  ) async {
    emit(const HouseholdLoading());

    final result = await _getHouseholdByInviteCodeUseCase(event.inviteCode);

    result.fold(
      (failure) => emit(HouseholdError(failure.message)),
      (household) => emit(HouseholdFound(household)),
    );
  }

  Future<void> _onValidateInviteCode(
    ValidateInviteCodeEvent event,
    Emitter<HouseholdState> emit,
  ) async {
    if (event.inviteCode.isEmpty) {
      emit(const InviteCodeInvalid(
          'Le code d\'invitation ne peut pas être vide'));
      return;
    }

    if (event.inviteCode.length < 6 || event.inviteCode.length > 8) {
      emit(const InviteCodeInvalid(
          'Le code d\'invitation doit contenir entre 6 et 8 caractères'));
      return;
    }

    emit(const HouseholdLoading());

    final result = await _getHouseholdByInviteCodeUseCase(event.inviteCode);

    result.fold(
      (failure) => emit(InviteCodeInvalid(failure.message)),
      (household) => emit(InviteCodeValid(household)),
    );
  }

  Future<void> _onResetHousehold(
    ResetHouseholdEvent event,
    Emitter<HouseholdState> emit,
  ) async {
    emit(const HouseholdInitial());
  }
}
