import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:medytrack_mobile_v2/data/models/medicine_model.dart';
import 'package:medytrack_mobile_v2/data/datasources/medicine_remote_data_source.dart';
import 'package:medytrack_mobile_v2/core/services/supabase_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

// Generate mocks
@GenerateMocks([SupabaseClient, SupabaseService])
import 'medicine_location_rendering_test.mocks.dart';

void main() {
  group('Medicine Location Rendering Tests', () {
    late MedicineRemoteDataSource dataSource;
    late MockSupabaseService mockSupabaseService;
    late MockSupabaseClient mockSupabaseClient;

    setUp(() {
      mockSupabaseService = MockSupabaseService();
      mockSupabaseClient = MockSupabaseClient();
      dataSource = MedicineRemoteDataSource();
      
      // Setup mock to return the mocked client
      when(mockSupabaseService.client).thenReturn(mockSupabaseClient);
    });

    group('getMedicineById Location Field Mapping', () {
      test('should correctly map location field to human-readable name', () async {
        // Arrange
        const medicineId = 'test-medicine-id';
        const expectedLocationName = 'Armoire';
        
        final mockResponse = {
          'id': medicineId,
          'name': 'Test Medicine',
          'dosage': '500',
          'form': 'comprimé',
          'location': expectedLocationName, // This should be mapped, not location_id
          'user_id': 'test-user-id',
          'created_at': '2025-09-12T10:00:00Z',
        };

        // Mock the Supabase query chain
        final mockQuery = MockPostgrestFilterBuilder();
        when(mockSupabaseClient.from('user_medicines')).thenReturn(mockQuery);
        when(mockQuery.select()).thenReturn(mockQuery);
        when(mockQuery.eq('id', medicineId)).thenReturn(mockQuery);
        when(mockQuery.single()).thenAnswer((_) async => mockResponse);

        // Act
        final result = await dataSource.getMedicineById(medicineId);

        // Assert
        expect(result, isA<MedicineModel>());
        expect(result.location, equals(expectedLocationName));
        expect(result.location, isNot(contains('-'))); // Should not be UUID format
        expect(result.location, isNot(matches(RegExp(r'^[0-9a-fA-F-]{36}$')))); // Not UUID
      });

      test('should handle null location gracefully', () async {
        // Arrange
        const medicineId = 'test-medicine-id';
        
        final mockResponse = {
          'id': medicineId,
          'name': 'Test Medicine',
          'dosage': '500',
          'form': 'comprimé',
          'location': null, // Null location
          'user_id': 'test-user-id',
          'created_at': '2025-09-12T10:00:00Z',
        };

        // Mock the Supabase query chain
        final mockQuery = MockPostgrestFilterBuilder();
        when(mockSupabaseClient.from('user_medicines')).thenReturn(mockQuery);
        when(mockQuery.select()).thenReturn(mockQuery);
        when(mockQuery.eq('id', medicineId)).thenReturn(mockQuery);
        when(mockQuery.single()).thenAnswer((_) async => mockResponse);

        // Act
        final result = await dataSource.getMedicineById(medicineId);

        // Assert
        expect(result.location, isNull);
      });

      test('should handle empty location string', () async {
        // Arrange
        const medicineId = 'test-medicine-id';
        
        final mockResponse = {
          'id': medicineId,
          'name': 'Test Medicine',
          'dosage': '500',
          'form': 'comprimé',
          'location': '', // Empty location
          'user_id': 'test-user-id',
          'created_at': '2025-09-12T10:00:00Z',
        };

        // Mock the Supabase query chain
        final mockQuery = MockPostgrestFilterBuilder();
        when(mockSupabaseClient.from('user_medicines')).thenReturn(mockQuery);
        when(mockQuery.select()).thenReturn(mockQuery);
        when(mockQuery.eq('id', medicineId)).thenReturn(mockQuery);
        when(mockQuery.single()).thenAnswer((_) async => mockResponse);

        // Act
        final result = await dataSource.getMedicineById(medicineId);

        // Assert
        expect(result.location, equals(''));
      });
    });

    group('Location Display Validation', () {
      test('should display common French location names correctly', () async {
        final commonLocations = [
          'Armoire',
          'Cuisine',
          'Salle de bain',
          'Chambre',
          'Salon',
          'Bureau',
          'Réfrigérateur',
          'Trousse de secours',
        ];

        for (final location in commonLocations) {
          // Arrange
          const medicineId = 'test-medicine-id';
          
          final mockResponse = {
            'id': medicineId,
            'name': 'Test Medicine',
            'location': location,
            'user_id': 'test-user-id',
            'created_at': '2025-09-12T10:00:00Z',
          };

          // Mock the Supabase query chain
          final mockQuery = MockPostgrestFilterBuilder();
          when(mockSupabaseClient.from('user_medicines')).thenReturn(mockQuery);
          when(mockQuery.select()).thenReturn(mockQuery);
          when(mockQuery.eq('id', medicineId)).thenReturn(mockQuery);
          when(mockQuery.single()).thenAnswer((_) async => mockResponse);

          // Act
          final result = await dataSource.getMedicineById(medicineId);

          // Assert
          expect(result.location, equals(location), 
                 reason: 'Location "$location" should be preserved exactly');
          expect(result.location, isNot(matches(RegExp(r'^[0-9a-fA-F-]{36}$'))), 
                 reason: 'Location "$location" should not be a UUID');
        }
      });

      test('should reject UUID-like strings as invalid locations', () {
        final uuidLikeStrings = [
          '123e4567-e89b-12d3-a456-************',
          'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
          '00000000-0000-0000-0000-000000000000',
        ];

        for (final uuidString in uuidLikeStrings) {
          expect(uuidString, matches(RegExp(r'^[0-9a-fA-F-]{36}$')), 
                 reason: 'String "$uuidString" should match UUID pattern');
          
          // In a real application, these should be converted to human-readable names
          // or flagged as data integrity issues
        }
      });
    });

    group('Data Source Field Mapping Regression Tests', () {
      test('should use "location" field not "location_id" field', () async {
        // This test ensures the bug fix is maintained
        // The bug was using medicineData['location_id'] instead of medicineData['location']
        
        const medicineId = 'test-medicine-id';
        const correctLocationName = 'Cuisine';
        const incorrectLocationId = 'uuid-that-should-not-be-used';
        
        final mockResponse = {
          'id': medicineId,
          'name': 'Test Medicine',
          'location': correctLocationName,      // Correct field to use
          'location_id': incorrectLocationId,   // Incorrect field (should be ignored)
          'user_id': 'test-user-id',
          'created_at': '2025-09-12T10:00:00Z',
        };

        // Mock the Supabase query chain
        final mockQuery = MockPostgrestFilterBuilder();
        when(mockSupabaseClient.from('user_medicines')).thenReturn(mockQuery);
        when(mockQuery.select()).thenReturn(mockQuery);
        when(mockQuery.eq('id', medicineId)).thenReturn(mockQuery);
        when(mockQuery.single()).thenAnswer((_) async => mockResponse);

        // Act
        final result = await dataSource.getMedicineById(medicineId);

        // Assert
        expect(result.location, equals(correctLocationName), 
               reason: 'Should use "location" field value');
        expect(result.location, isNot(equals(incorrectLocationId)), 
               reason: 'Should NOT use "location_id" field value');
      });
    });
  });
}

// Mock class for Postgrest filter builder
class MockPostgrestFilterBuilder extends Mock implements PostgrestFilterBuilder {
  @override
  PostgrestFilterBuilder select([String columns = '*']) => this;
  
  @override
  PostgrestFilterBuilder eq(String column, Object value) => this;
  
  @override
  Future<Map<String, dynamic>> single() async => {};
}
