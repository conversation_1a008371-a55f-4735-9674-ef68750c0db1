import 'package:supabase_flutter/supabase_flutter.dart';
import '../../core/utils/supabase_utils.dart';
import '../../domain/repositories/dashboard_repository.dart';
import '../models/dashboard_stats_model.dart';
import '../../utils/logger.dart';

abstract class DashboardRemoteDataSource {
  Future<DashboardStatsModel> getDashboardStats(String householdId);
  Future<DashboardStatsModel> getFilteredDashboardStats(
      DashboardStatsParams params);
}

class DashboardRemoteDataSourceImpl implements DashboardRemoteDataSource {
  final SupabaseClient supabaseClient;

  DashboardRemoteDataSourceImpl({required this.supabaseClient});

  @override
  Future<DashboardStatsModel> getDashboardStats(String householdId) async {
    try {
      // Validate household ID
      if (!SupabaseUtils.isValidUUID(householdId)) {
        throw Exception('Invalid household ID format: $householdId');
      }

      // Get medicines directly to calculate accurate statistics
      final medicinesResponse = await supabaseClient
          .from('user_medicines')
          .select('expiration, quantity, low_stock_threshold')
          .eq('household_id', householdId);

      // Calculate statistics using the same logic as Medicine entity
      final now = DateTime.now();
      final expiringSoonThreshold = now.add(const Duration(days: 30));

      int total = medicinesResponse.length;
      int expired = 0;
      int expiringSoon = 0;
      int lowStock = 0;

      for (final medicine in medicinesResponse) {
        final expiration = medicine['expiration'] != null
            ? DateTime.parse(medicine['expiration'] as String)
            : null;
        final quantity = medicine['quantity'] as int? ?? 0;
        final lowStockThreshold = medicine['low_stock_threshold'] as int? ?? 0;

        // Check if expired
        if (expiration != null && expiration.isBefore(now)) {
          expired++;
        }
        // Check if expiring soon (and not already expired)
        else if (expiration != null &&
            expiration.isBefore(expiringSoonThreshold)) {
          expiringSoon++;
        }

        // Check if low stock (only if threshold > 0, meaning tracking is enabled)
        if (lowStockThreshold > 0 &&
            quantity <= lowStockThreshold &&
            quantity > 0) {
          lowStock++;
        }
      }

      // Get total available locations for the household
      final locationsResponse = await supabaseClient
          .from('locations')
          .select('id')
          .eq('household_id', householdId);

      final totalLocations = locationsResponse.length;

      // Get location usage count (medicines with location)
      final medicinesWithLocationResponse = await supabaseClient
          .from('user_medicines')
          .select('location')
          .eq('household_id', householdId)
          .not('location', 'is', null);

      final locationCount = medicinesWithLocationResponse.length;

      // Get tag statistics (mirror web app pattern)
      final tagResponse = await supabaseClient.from('medicine_tags').select('''
            tag_id,
            tags!inner(
              id,
              name,
              color,
              household_id
            )
          ''').eq('tags.household_id', householdId);

      final uniqueTagIds = <String>{};
      if (tagResponse.isNotEmpty) {
        for (final item in tagResponse) {
          final tagId = item['tag_id'] as String?;
          if (tagId != null) {
            uniqueTagIds.add(tagId);
          }
        }
      }

      // Calculate percentages like web app
      final locationUsagePercentage = totalLocations > 0
          ? (locationCount / totalLocations * 100).round()
          : 0;

      final tagUsagePercentage = uniqueTagIds.isNotEmpty
          ? (uniqueTagIds.length / 7 * 100).round() // 7 is system tags count
          : 0;

      return DashboardStatsModel(
        total: total,
        expired: expired,
        expiringSoon: expiringSoon,
        lowStock: lowStock,
        locationCount: locationCount,
        totalLocations: totalLocations,
        tagCount: uniqueTagIds.length,
        totalTags: 7, // System tags count
        locationUsagePercentage: locationUsagePercentage,
        tagUsagePercentage: tagUsagePercentage,
      );
    } catch (e) {
      throw Exception(SupabaseUtils.getErrorMessage(e));
    }
  }

  @override
  Future<DashboardStatsModel> getFilteredDashboardStats(
      DashboardStatsParams params) async {
    try {
      // Validate household ID
      if (!SupabaseUtils.isValidUUID(params.householdId)) {
        throw Exception('Invalid household ID format: ${params.householdId}');
      }

      AppLogger.database(
          'Getting filtered dashboard stats with params: ${params.filterDescription}');

      // Build base query for medicines
      var medicinesQuery = supabaseClient
          .from('user_medicines')
          .select(
              'expiration, quantity, low_stock_threshold, location_id, family_member_id')
          .eq('household_id', params.householdId);

      // Apply location filter if specified
      if (params.locationId != null) {
        if (!SupabaseUtils.isValidUUID(params.locationId!)) {
          throw Exception('Invalid location ID format: ${params.locationId}');
        }
        medicinesQuery = medicinesQuery.eq('location_id', params.locationId!);
      }

      // Apply family member filter if specified
      if (params.familyMemberId != null) {
        if (!SupabaseUtils.isValidUUID(params.familyMemberId!)) {
          throw Exception(
              'Invalid family member ID format: ${params.familyMemberId}');
        }
        medicinesQuery =
            medicinesQuery.eq('family_member_id', params.familyMemberId!);
      }

      final medicinesResponse = await medicinesQuery;

      // Calculate statistics using the same logic as Medicine entity
      final now = DateTime.now();
      final expiringSoonThreshold = now.add(const Duration(days: 30));

      int total = medicinesResponse.length;
      int expired = 0;
      int expiringSoon = 0;
      int lowStock = 0;

      for (final medicine in medicinesResponse) {
        final expiration = medicine['expiration'] != null
            ? DateTime.parse(medicine['expiration'])
            : null;
        final quantity = medicine['quantity'] as int? ?? 0;
        final lowStockThreshold = medicine['low_stock_threshold'] as int? ?? 0;

        // Check expiration status
        if (expiration != null) {
          if (expiration.isBefore(now)) {
            expired++;
          } else if (expiration.isBefore(expiringSoonThreshold)) {
            expiringSoon++;
          }
        }

        // Check stock status
        if (quantity > 0 &&
            lowStockThreshold > 0 &&
            quantity <= lowStockThreshold) {
          lowStock++;
        }
      }

      // Apply URL filter if specified (this filters the already filtered results)
      if (params.filter != null) {
        switch (params.filter!.toLowerCase()) {
          case 'expired':
            total = expired;
            expiringSoon = 0;
            lowStock = 0;
            break;
          case 'expiring_soon':
          case 'expiring':
            total = expiringSoon;
            expired = 0;
            lowStock = 0;
            break;
          case 'low_stock':
            total = lowStock;
            expired = 0;
            expiringSoon = 0;
            break;
          case 'all':
          default:
            // Keep all calculated values
            break;
        }
      }

      // For filtered stats, location and tag counts are less relevant
      // but we'll calculate them for consistency
      int locationCount = 0;
      int totalLocations = 0;
      int tagCount = 0;

      if (params.locationId == null) {
        // Get total available locations for the household
        final locationsResponse = await supabaseClient
            .from('locations')
            .select('id')
            .eq('household_id', params.householdId);
        totalLocations = locationsResponse.length;

        // Get location usage count (medicines with location)
        var medicinesWithLocationQuery = supabaseClient
            .from('user_medicines')
            .select('location_id')
            .eq('household_id', params.householdId)
            .not('location_id', 'is', null);

        if (params.familyMemberId != null) {
          medicinesWithLocationQuery = medicinesWithLocationQuery.eq(
              'family_member_id', params.familyMemberId!);
        }

        final medicinesWithLocationResponse = await medicinesWithLocationQuery;
        locationCount = medicinesWithLocationResponse.length;
      } else {
        // When filtering by location, all medicines have that location
        locationCount = total;
        totalLocations = 1;
      }

      final locationUsagePercentage = totalLocations > 0
          ? ((locationCount / totalLocations) * 100).round()
          : 0;

      AppLogger.database(
          'Filtered dashboard stats calculated: total=$total, expired=$expired, expiringSoon=$expiringSoon, lowStock=$lowStock');

      return DashboardStatsModel(
        total: total,
        expired: expired,
        expiringSoon: expiringSoon,
        lowStock: lowStock,
        locationCount: locationCount,
        totalLocations: totalLocations,
        tagCount: tagCount,
        totalTags: 7, // System tags count
        locationUsagePercentage: locationUsagePercentage,
        tagUsagePercentage: 0, // Simplified for filtered view
      );
    } catch (e) {
      throw Exception(SupabaseUtils.getErrorMessage(e));
    }
  }
}
