import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../l10n/generated/app_localizations.dart';
import '../../../domain/entities/medicine.dart';
import '../../../domain/entities/reminder.dart';
import '../../../core/utils/supabase_utils.dart';
import '../../bloc/medicine/medicine_bloc.dart';
import '../../bloc/reminder/reminder_bloc.dart';
import '../../bloc/reminder/reminder_state.dart';
import '../../bloc/reminder/reminder_event.dart';

class RemindersPage extends StatefulWidget {
  const RemindersPage({super.key});

  @override
  State<RemindersPage> createState() => _RemindersPageState();
}

class _RemindersPageState extends State<RemindersPage> {
  bool _isInitialLoad = true;
  int _selectedFilterIndex = 0; // 0: Active, 1: Paused, 2: Archived
  final List<bool> _filterSelections = [true, false, false];
  bool _isPerformingStatusChange =
      false; // Track when we're changing reminder status
  RemindersLoaded?
      _lastRemindersLoadedState; // Store the last RemindersLoaded state

  @override
  void initState() {
    super.initState();
    // Optimized loading: Load medicines first, then reminders automatically
    _loadData();
  }

  void _loadData() async {
    final householdId = SupabaseUtils.getHouseholdId(context);
    if (householdId != null) {
      // Load medicines first
      context
          .read<MedicineBloc>()
          .add(MedicineLoadRequested(householdId: householdId));
    }
  }

  void _loadReminders() {
    final medicineState = context.read<MedicineBloc>().state;
    if (medicineState is MedicineLoaded) {
      final medicineIds = medicineState.medicines.map((m) => m.id).toList();
      if (medicineIds.isNotEmpty) {
        context.read<ReminderBloc>().add(LoadBatchReminders(medicineIds));
      } else {
        context.read<ReminderBloc>().add(const ClearReminders());
      }
    }
    // Note: We don't automatically retry if medicines aren't loaded yet
    // The BlocBuilder for MedicineBloc will handle retries when state changes
  }

  void _handleReminderStatusChange(RemindersLoaded state,
      {bool isUserAction = false}) {
    // Check if current tab has any reminders
    final reminders = state.reminders;
    bool hasRemindersInCurrentTab = false;

    // Count reminders in each tab for debugging
    final activeCount = reminders.where((r) => r.isCurrentlyActive).length;
    final pausedCount = reminders.where((r) => r.isPaused).length;
    final archivedCount = reminders.where((r) => r.isArchived).length;

    if (kDebugMode) {
      print(
          '[RemindersPage] Status change detected - Active: $activeCount, Paused: $pausedCount, Archived: $archivedCount');
      print(
          '[RemindersPage] Current tab: $_selectedFilterIndex, isUserAction: $isUserAction');
      print(
          '[RemindersPage] Reminder statuses: ${reminders.map((r) => '${r.id}: ${r.status}').join(', ')}');
    }

    switch (_selectedFilterIndex) {
      case 0: // Active
        hasRemindersInCurrentTab = activeCount > 0;
        break;
      case 1: // Paused
        hasRemindersInCurrentTab = pausedCount > 0;
        break;
      case 2: // Archived
        hasRemindersInCurrentTab = archivedCount > 0;
        break;
    }

    // Switch tabs if:
    // 1. We're in the initial load and current tab is empty, OR
    // 2. It's a user action (always switch to show the result of the action)
    if ((_isInitialLoad && !hasRemindersInCurrentTab) || isUserAction) {
      if (kDebugMode) {
        print(
            '[RemindersPage] Switching tabs - isInitialLoad: $_isInitialLoad, isUserAction: $isUserAction, currentTabEmpty: ${!hasRemindersInCurrentTab}');
      }

      // Smart tab switching based on the most logical destination
      int targetTab = 0; // Default to Active

      if (isUserAction) {
        // For user actions, switch to the tab with the most reminders
        // This ensures the user sees the result of their action
        if (pausedCount >= activeCount &&
            pausedCount >= archivedCount &&
            pausedCount > 0) {
          targetTab = 1; // Paused
        } else if (archivedCount >= activeCount &&
            archivedCount >= pausedCount &&
            archivedCount > 0) {
          targetTab = 2; // Archived
        } else if (activeCount > 0) {
          targetTab = 0; // Active
        }
      } else {
        // For initial load, use the original logic
        // If we're on Active tab and it's empty, prefer the tab with most reminders
        if (_selectedFilterIndex == 0) {
          if (pausedCount > archivedCount && pausedCount > 0) {
            targetTab = 1; // Paused
          } else if (archivedCount > 0) {
            targetTab = 2; // Archived
          }
        }
        // If we're on Paused tab and it's empty, prefer Active then Archived
        else if (_selectedFilterIndex == 1) {
          if (activeCount > 0) {
            targetTab = 0; // Active
          } else if (archivedCount > 0) {
            targetTab = 2; // Archived
          }
        }
        // If we're on Archived tab and it's empty, prefer Active then Paused
        else if (_selectedFilterIndex == 2) {
          if (activeCount > 0) {
            targetTab = 0; // Active
          } else if (pausedCount > 0) {
            targetTab = 1; // Paused
          }
        }
      }

      if (kDebugMode) print('[RemindersPage] Switching to tab: $targetTab');
      setState(() {
        _selectedFilterIndex = targetTab;
        _filterSelections[0] = targetTab == 0;
        _filterSelections[1] = targetTab == 1;
        _filterSelections[2] = targetTab == 2;
      });
    } else if (_isInitialLoad) {
      // Mark initial load as complete after first check
      _isInitialLoad = false;
      if (kDebugMode) {
        print('[RemindersPage] Initial load completed');
      }
    } else {
      if (kDebugMode) {
        print(
            '[RemindersPage] Current tab has reminders or not initial load, no switch needed');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.teal,
      body: Column(
        children: [
          // Header with teal background
          Container(
            padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () => context.go('/dashboard'),
                  child: Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.arrow_back,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Rappels',
                    style: AppTextStyles.headlineMedium.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Content container with white background
          Expanded(
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, -4),
                  ),
                ],
              ),
              child: BlocListener<ReminderBloc, ReminderState>(
                listenWhen: (previous, current) {
                  return current is ReminderOperationSuccess ||
                      current is ReminderError ||
                      current is RemindersLoaded;
                },
                listener: (context, state) {
                  if (state is ReminderOperationSuccess) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(state.message),
                        backgroundColor: AppColors.success,
                      ),
                    );
                    // After a successful operation, check if we need to switch tabs
                    // Add a small delay to ensure the state has been updated
                    Future.microtask(() {
                      final reminderState = context.read<ReminderBloc>().state;
                      if (reminderState is RemindersLoaded) {
                        _handleReminderStatusChange(reminderState,
                            isUserAction: true);
                      }
                    });
                  } else if (state is ReminderError) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(state.message),
                        backgroundColor: AppColors.error,
                      ),
                    );
                  } else if (state is RemindersLoaded &&
                      _isPerformingStatusChange) {
                    // Update stored state with the new data immediately
                    _lastRemindersLoadedState = state;
                    if (kDebugMode) {
                      print(
                          '[RemindersPage] Updated stored state during status change with ${state.reminders.length} reminders');
                      print(
                          '[RemindersPage] New stored reminder statuses: ${state.reminders.map((r) => '${r.id}: ${r.status}').join(', ')}');
                    }

                    // Auto-switch tabs when reminder status changes
                    // Use post-frame callback to ensure UI is ready
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (mounted) {
                        _handleReminderStatusChange(state, isUserAction: true);
                      }
                    });
                    _isPerformingStatusChange = false; // Reset flag
                  } else if (state is ReminderOperationSuccess &&
                      _isPerformingStatusChange) {
                    // For operation success, use the updated stored state
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (mounted && _lastRemindersLoadedState != null) {
                        if (kDebugMode) {
                          print(
                              '[RemindersPage] Using updated stored state for tab switching');
                        }
                        _handleReminderStatusChange(_lastRemindersLoadedState!,
                            isUserAction: true);
                      }
                    });
                    _isPerformingStatusChange = false; // Reset flag
                  } else if (state is RemindersLoaded) {
                    _handleReminderStatusChange(state);
                  }
                },
                child: BlocBuilder<MedicineBloc, MedicineState>(
                  buildWhen: (previous, current) {
                    // Only rebuild when state actually changes
                    return previous != current;
                  },
                  builder: (context, medicineState) {
                    if (medicineState is MedicineLoading && _isInitialLoad) {
                      return const Center(child: CircularProgressIndicator());
                    } else if (medicineState is MedicineError) {
                      return _buildErrorState(medicineState.message);
                    } else if (medicineState is MedicineLoaded) {
                      // Auto-load reminders when medicines are loaded
                      if (_isInitialLoad) {
                        _isInitialLoad = false;
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          _loadReminders();
                        });
                      }

                      return BlocBuilder<ReminderBloc, ReminderState>(
                        buildWhen: (previous, current) {
                          // Only rebuild when reminder state changes meaningfully
                          // Always rebuild when we have a RemindersLoaded state
                          final shouldRebuild =
                              previous != current || current is RemindersLoaded;
                          if (kDebugMode) {
                            print(
                                '[RemindersPage] buildWhen - previous: ${previous.runtimeType}, current: ${current.runtimeType}, shouldRebuild: $shouldRebuild');
                          }
                          return shouldRebuild;
                        },
                        builder: (context, reminderState) {
                          if (kDebugMode) {
                            print(
                                '[RemindersPage] Building reminders content with state: ${reminderState.runtimeType}');
                            if (reminderState is RemindersLoaded) {
                              print(
                                  '[RemindersPage] Loaded ${reminderState.reminders.length} reminders');
                            }
                          }

                          // Store RemindersLoaded state when we receive it
                          if (reminderState is RemindersLoaded) {
                            _lastRemindersLoadedState = reminderState;
                            if (kDebugMode) {
                              print(
                                  '[RemindersPage] Stored new RemindersLoaded state with ${reminderState.reminders.length} reminders');
                              print(
                                  '[RemindersPage] Updated reminder statuses: ${reminderState.reminders.map((r) => '${r.id}: ${r.status}').join(', ')}');
                            }
                          }

                          // Only build with RemindersLoaded state
                          // For other states, use the last stored RemindersLoaded state
                          ReminderState stateToUse = reminderState;
                          if (reminderState is! RemindersLoaded) {
                            if (kDebugMode) {
                              print(
                                  '[RemindersPage] Current state is ${reminderState.runtimeType}, checking for stored state...');
                              print(
                                  '[RemindersPage] Stored state available: ${_lastRemindersLoadedState != null}');
                            }
                            if (_lastRemindersLoadedState != null) {
                              stateToUse = _lastRemindersLoadedState!;
                              if (kDebugMode) {
                                print(
                                    '[RemindersPage] Using stored RemindersLoaded state instead: ${stateToUse.runtimeType} with ${(_lastRemindersLoadedState as RemindersLoaded).reminders.length} reminders');
                              }
                            } else {
                              if (kDebugMode) {
                                print(
                                    '[RemindersPage] No stored state available, using current state');
                              }
                            }
                          }

                          if (kDebugMode) {
                            print(
                                '[RemindersPage] Final stateToUse: ${stateToUse.runtimeType}');
                          }
                          return _buildRemindersContent(
                              medicineState.medicines, stateToUse);
                        },
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRemindersContent(
      List<Medicine> medicines, ReminderState reminderState) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Filter toggle buttons
          _buildFilterToggleButtons(),

          const SizedBox(height: 24),

          // Filtered reminders section
          _buildFilteredReminders(medicines, reminderState),
        ],
      ),
    );
  }

  Widget _buildFilterToggleButtons() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: ToggleButtons(
        isSelected: _filterSelections,
        onPressed: (int index) {
          setState(() {
            // Clear all selections
            for (int i = 0; i < _filterSelections.length; i++) {
              _filterSelections[i] = i == index;
            }
            _selectedFilterIndex = index;
          });
        },
        borderRadius: BorderRadius.circular(12),
        selectedBorderColor: AppColors.teal,
        selectedColor: Colors.white,
        fillColor: AppColors.teal,
        color: AppColors.grey600,
        constraints: const BoxConstraints(
          minHeight: 48,
          minWidth: 100,
        ),
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.check_circle_outline,
                  size: 18,
                  color:
                      _filterSelections[0] ? Colors.white : AppColors.grey600,
                ),
                const SizedBox(width: 8),
                Text(
                  'Actifs',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                    color:
                        _filterSelections[0] ? Colors.white : AppColors.grey600,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.pause_circle_outline,
                  size: 18,
                  color:
                      _filterSelections[1] ? Colors.white : AppColors.grey600,
                ),
                const SizedBox(width: 8),
                Text(
                  'En pause',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                    color:
                        _filterSelections[1] ? Colors.white : AppColors.grey600,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.archive_outlined,
                  size: 18,
                  color:
                      _filterSelections[2] ? Colors.white : AppColors.grey600,
                ),
                const SizedBox(width: 8),
                Text(
                  'Archivés',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                    color:
                        _filterSelections[2] ? Colors.white : AppColors.grey600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilteredReminders(
      List<Medicine> medicines, ReminderState reminderState) {
    if (reminderState is ReminderLoading && _isInitialLoad) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_selectedFilterIndex == 0) {
      return _buildActiveReminderCards(medicines, reminderState);
    } else if (_selectedFilterIndex == 1) {
      return _buildPausedReminderCards(medicines, reminderState);
    } else {
      return _buildArchivedReminderCards(medicines, reminderState);
    }
  }

  Widget _buildActiveReminderCards(
      List<Medicine> medicines, ReminderState reminderState) {
    // Extract active reminders from the state
    List<Reminder> activeReminders = [];
    if (reminderState is RemindersLoaded) {
      activeReminders =
          reminderState.reminders.where((r) => r.isCurrentlyActive).toList();
    }

    if (activeReminders.isEmpty) {
      return _buildEmptyReminderState();
    }

    return Column(
      children: activeReminders.map((reminder) {
        // Find the medicine for this reminder
        Medicine? foundMedicine;
        try {
          foundMedicine = medicines.firstWhere(
            (m) => m.id == reminder.userMedicineId,
          );
        } catch (e) {
          // Medicine not found, create a placeholder
          foundMedicine = Medicine.unknown();
        }
        final medicine = foundMedicine;

        return _buildReminderCard(reminder, medicine);
      }).toList(),
    );
  }

  Widget _buildEmptyReminderState() {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Icon(
              Icons.alarm_add,
              size: 48,
              color: AppColors.grey400,
            ),
            const SizedBox(height: 16),
            Text(
              'Aucun rappel configuré',
              style: AppTextStyles.titleMedium.copyWith(
                color: AppColors.grey600,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Commencez par ajouter des rappels pour vos médicaments',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.grey500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPausedReminderCards(
      List<Medicine> medicines, ReminderState reminderState) {
    // Extract paused reminders from the state
    List<Reminder> pausedReminders = [];
    if (reminderState is RemindersLoaded) {
      pausedReminders =
          reminderState.reminders.where((r) => r.isPaused).toList();
    }

    if (pausedReminders.isEmpty) {
      return _buildEmptyState(
        icon: Icons.pause_circle_outline,
        title: 'Aucun rappel en pause',
        subtitle: 'Les rappels mis en pause apparaîtront ici',
      );
    }

    return Column(
      children: pausedReminders.map((reminder) {
        // Find the medicine for this reminder
        Medicine? foundMedicine;
        try {
          foundMedicine = medicines.firstWhere(
            (m) => m.id == reminder.userMedicineId,
          );
        } catch (e) {
          foundMedicine = Medicine.unknown();
        }

        return _buildReminderCard(reminder, foundMedicine, isPaused: true);
      }).toList(),
    );
  }

  Widget _buildArchivedReminderCards(
      List<Medicine> medicines, ReminderState reminderState) {
    // Extract archived reminders from the state
    List<Reminder> archivedReminders = [];
    if (reminderState is RemindersLoaded) {
      archivedReminders =
          reminderState.reminders.where((r) => r.isArchived).toList();
    }

    if (archivedReminders.isEmpty) {
      return _buildEmptyState(
        icon: Icons.archive_outlined,
        title: 'Aucun rappel archivé',
        subtitle: 'Les rappels supprimés apparaîtront ici',
      );
    }

    return Column(
      children: archivedReminders.map((reminder) {
        // Find the medicine for this reminder
        Medicine? foundMedicine;
        try {
          foundMedicine = medicines.firstWhere(
            (m) => m.id == reminder.userMedicineId,
          );
        } catch (e) {
          foundMedicine = Medicine.unknown();
        }

        return _buildReminderCard(reminder, foundMedicine, isArchived: true);
      }).toList(),
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Icon(
              icon,
              size: 48,
              color: AppColors.grey400,
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: AppTextStyles.titleMedium.copyWith(
                color: AppColors.grey600,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.grey500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReminderCard(Reminder reminder, Medicine medicine,
      {bool isPaused = false, bool isArchived = false}) {
    // Standardized card to match Today's Medicine card visual language
    return Card(
      elevation: 1,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: const BorderSide(color: AppColors.grey200, width: 1),
      ),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          InkWell(
            borderRadius: BorderRadius.circular(12),
            onTap: () {
              context.pushNamed(
                'reminder-detail',
                pathParameters: {'id': reminder.id ?? ''},
                extra: {
                  'reminder': reminder,
                  'medicine': medicine,
                },
              );
            },
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Grid-like content: Left text block; status pill moved to bottom-right
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Left: Two-row text block (name on first row, dosage/form on second row)
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Medicine name (adjusted font to mirror today's card emphasis)
                            Text(
                              medicine.name,
                              style: AppTextStyles.titleMedium.copyWith(
                                color: AppColors.navy,
                                fontWeight: FontWeight.bold,
                                fontSize:
                                    AppTextStyles.titleMedium.fontSize! + 2,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 4),
                            // Dosage/form on its own row with reduced size
                            Text(
                              _composeDosageForm(medicine),
                              style: AppTextStyles.bodySmall.copyWith(
                                color: AppColors.black,
                                fontWeight: FontWeight.normal,
                                fontSize: AppTextStyles.bodySmall.fontSize! - 2,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 12),
                      // Intentionally leave right-side free to avoid conflict with top-right Modify icon
                      const SizedBox.shrink(),
                    ],
                  ),
                  const SizedBox(height: 8),
                  // Schedule line (kept as-is under the two-row name + dosage/form)
                  Text(
                    _formatReminderSchedule(reminder),
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.grey600,
                    ),
                  ),
                  const SizedBox(height: 12),
                  // Actions row - use dynamic buttons based on reminder status
                  Row(
                    children: _buildStatusManagementButtons(reminder, medicine),
                  ),
                ],
              ),
            ),
          ),
          // Modify icon at card corner (outside the content to avoid overlapping the status pill)
          Positioned(
            top: 0,
            right: 0,
            child: PopupMenuButton<String>(
              onSelected: (value) {
                if (value == 'modify') {
                  _editReminder(reminder, medicine);
                } else if (value == 'cancel') {
                  context
                      .read<ReminderBloc>()
                      .add(DeleteReminder(reminder.id!));
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content:
                          Text('Rappel pour ${medicine.displayName} supprimé'),
                      backgroundColor: AppColors.error,
                    ),
                  );
                }
              },
              itemBuilder: (context) => [
                PopupMenuItem<String>(
                  value: 'modify',
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.edit, color: Colors.black),
                      const SizedBox(width: 8),
                      Text('Modifier'),
                    ],
                  ),
                ),
                PopupMenuItem<String>(
                  value: 'cancel',
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.delete_outline,
                          size: 18, color: AppColors.error),
                      const SizedBox(width: 8),
                      Text('Supprimer',
                          style: TextStyle(color: AppColors.error)),
                    ],
                  ),
                ),
              ],
              icon: Container(
                width: 35,
                height: 35,
                decoration: BoxDecoration(
                  color: AppColors.navy,
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(12),
                    bottomLeft: Radius.circular(12),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.08),
                      blurRadius: 6,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                alignment: Alignment.center,
                child:
                    const Icon(Icons.more_vert, color: Colors.white, size: 18),
              ),
              padding: EdgeInsets.zero,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(8)),
              ),
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _editReminder(Reminder reminder, Medicine medicine) {
    // Navigate to edit reminder page (similar to add reminder but with pre-filled data)
    context.push('/reminders/edit/${reminder.id}', extra: {
      'reminder': reminder,
      'medicine': medicine,
    });
  }

  // Compose dosage/form safely for second-row details
  String _composeDosageForm(Medicine medicine) {
    final parts = <String>[];
    if ((medicine.dosage ?? '').trim().isNotEmpty) {
      parts.add(medicine.dosage!.trim());
    }
    if ((medicine.form ?? '').trim().isNotEmpty) {
      parts.add(medicine.form!.trim());
    }
    return parts.isEmpty ? '' : parts.join(' ');
  }

  List<Widget> _buildStatusManagementButtons(
      Reminder reminder, Medicine medicine) {
    final List<Widget> buttons = [];

    switch (reminder.status) {
      case ReminderStatus.active:
        // Active reminders show "Pause" and "Archive" buttons
        buttons.addAll([
          Expanded(
            child: _textOnlyActionButtonBold(
              label: 'Pause',
              color: AppColors.grey600,
              onTap: () => _pauseReminder(reminder, medicine),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _textOnlyActionButtonBold(
              label: 'Archive',
              color: AppColors.grey600,
              onTap: () => _archiveReminder(reminder, medicine),
            ),
          ),
          const SizedBox(width: 8),
          // Spacer to maintain layout
          const Expanded(child: SizedBox(height: 36)),
          const SizedBox(width: 8),
          // Status pill
          Expanded(
            child: Align(
              alignment: Alignment.centerRight,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color:
                      _getReminderStatusColor(reminder).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _getReminderStatusText(reminder),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: _getReminderStatusColor(reminder),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ]);
        break;

      case ReminderStatus.paused:
        // For paused, keep actions consistent: "Pause" becomes "Activer" and "Archive"
        buttons.addAll([
          Expanded(
            child: _textOnlyActionButtonBold(
              label: 'Activer',
              color: AppColors.success,
              onTap: () => _resumeReminder(reminder, medicine),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _textOnlyActionButtonBold(
              label: 'Archive',
              color: AppColors.grey600,
              onTap: () => _archiveReminder(reminder, medicine),
            ),
          ),
          const SizedBox(width: 8),
          // Spacer to maintain layout
          const Expanded(child: SizedBox(height: 36)),
          const SizedBox(width: 8),
          // Status pill
          Expanded(
            child: Align(
              alignment: Alignment.centerRight,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color:
                      _getReminderStatusColor(reminder).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _getReminderStatusText(reminder),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: _getReminderStatusColor(reminder),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ]);
        break;

      case ReminderStatus.archived:
        // Archived: "Activer" + "Supprimer"
        buttons.addAll([
          Expanded(
            child: _textOnlyActionButtonBold(
              label: 'Activer',
              color: AppColors.success,
              onTap: () => _resumeReminder(reminder, medicine),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _textOnlyActionButtonBold(
              label: 'Supprimer',
              color: AppColors.error,
              onTap: () => _deleteReminder(reminder, medicine),
            ),
          ),
          const SizedBox(width: 8),
          // Spacer to maintain layout
          const Expanded(child: SizedBox(height: 36)),
          const SizedBox(width: 8),
          // Status pill
          Expanded(
            child: Align(
              alignment: Alignment.centerRight,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color:
                      _getReminderStatusColor(reminder).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _getReminderStatusText(reminder),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: _getReminderStatusColor(reminder),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ]);
        break;
    }

    return buttons;
  }

  // Bold text-only action button builder matching today's card characteristics
  Widget _textOnlyActionButtonBold({
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 36,
        alignment: Alignment.center,
        child: Text(
          label,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: AppTextStyles.labelSmall.copyWith(
            color: color,
            fontSize: 12,
            fontWeight: FontWeight.w700, // bold as required
          ),
        ),
      ),
    );
  }

  // Placeholder to preserve 4-column grid without adding extra CTA
  Widget _transparentHoldPlace() {
    return Container(
      height: 36,
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(8),
      ),
    );
  }

  void _pauseReminder(Reminder reminder, Medicine medicine) {
    _isPerformingStatusChange = true; // Set flag before status change
    context.read<ReminderBloc>().add(PauseReminder(reminder.id!));
    // Success message handled by BLoC
  }

  void _resumeReminder(Reminder reminder, Medicine medicine) {
    _isPerformingStatusChange = true; // Set flag before status change
    context.read<ReminderBloc>().add(ResumeReminder(reminder.id!));
    // Success message handled by BLoC
  }

  void _archiveReminder(Reminder reminder, Medicine medicine) {
    _isPerformingStatusChange = true; // Set flag before status change
    context.read<ReminderBloc>().add(ArchiveReminder(reminder.id!));
    // Success message handled by BLoC
  }

  void _deleteReminder(Reminder reminder, Medicine medicine) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        icon: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.error.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.delete_forever,
            color: AppColors.error,
            size: 32,
          ),
        ),
        title: Text(
          'Supprimer le rappel',
          style: AppTextStyles.titleLarge.copyWith(
            color: AppColors.grey900,
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Êtes-vous sûr de vouloir supprimer ce rappel pour ${medicine.displayName} ?',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.grey600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Cette action est irréversible.',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.error,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Annuler',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.grey600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Delete the reminder
              context.read<ReminderBloc>().add(DeleteReminder(reminder.id!));
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Supprimer',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatReminderSchedule(Reminder reminder) {
    switch (reminder.frequencyType) {
      case 'DAILY':
        final times = reminder.times.join(', ');
        return 'Quotidien à $times';
      case 'WEEKLY':
        final days = reminder.frequencyDays.isNotEmpty
            ? reminder.frequencyDays.map((day) => _getDayName(day)).join(', ')
            : '';
        final times = reminder.times.join(', ');
        return '$days à $times';
      case 'HOURLY_INTERVAL':
        return 'Toutes les ${reminder.frequencyValue ?? 8} heures';
      case 'SPECIFIC_DATES':
        final times = reminder.times.join(', ');
        return 'Dates spécifiques à $times';
      default:
        final times = reminder.times.join(', ');
        return 'Rappel à $times';
    }
  }

  String _getDayName(int day) {
    switch (day) {
      case 1:
        return 'Lun';
      case 2:
        return 'Mar';
      case 3:
        return 'Mer';
      case 4:
        return 'Jeu';
      case 5:
        return 'Ven';
      case 6:
        return 'Sam';
      case 7:
        return 'Dim';
      default:
        return '';
    }
  }

  Widget _buildMedicinesWithoutReminders(List<Medicine> medicines) {
    if (medicines.isEmpty) {
      return Card(
        elevation: 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Icon(
                Icons.medication_outlined,
                size: 48,
                color: AppColors.grey400,
              ),
              const SizedBox(height: 16),
              Text(
                'Aucun médicament',
                style: AppTextStyles.titleMedium.copyWith(
                  color: AppColors.grey600,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Ajoutez des médicaments pour configurer des rappels',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.grey500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      children: medicines
          .take(3)
          .map((medicine) => _buildMedicineCard(medicine))
          .toList(),
    );
  }

  Widget _buildMedicineCard(Medicine medicine) {
    return Card(
      elevation: 1,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          // TODO: Navigate to add reminder for this medicine
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Configurer rappel pour ${medicine.displayName}'),
              behavior: SnackBarBehavior.floating,
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: AppColors.teal.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: const Icon(
                  Icons.medication,
                  color: AppColors.teal,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      medicine.displayName,
                      style: AppTextStyles.titleMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Aucun rappel configuré',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.grey600,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.add_alarm,
                color: AppColors.teal,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Erreur de chargement',
            style: AppTextStyles.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.grey600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('Réessayer'),
          ),
        ],
      ),
    );
  }

  // Snooze: record dose history entry (SNOOZED) with RLS-compliant user_id
  void _snoozeReminder(Reminder reminder, Medicine medicine) {
    final userId = SupabaseUtils.getUserId(context);
    if (userId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Erreur: Utilisateur non authentifié'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }
    final now = DateTime.now();
    final dose = DoseHistory(
      userId: userId,
      userMedicineId: reminder.userMedicineId,
      reminderId: reminder.id,
      scheduledAt: now,
      actionAt: now,
      status: 'SNOOZED',
    );
    context.read<ReminderBloc>().add(AddDoseHistory(dose));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Rappel pour ${medicine.displayName} reporté'),
        backgroundColor: AppColors.warning,
      ),
    );
  }

  Widget _buildAddReminderButton() {
    final l10n = AppLocalizations.of(context);

    return Center(
      child: Container(
        width: double.infinity,
        margin: const EdgeInsets.symmetric(horizontal: 40),
        child: ElevatedButton.icon(
          onPressed: () {
            context.go('/reminders/add');
          },
          icon: const Icon(Icons.add_alarm),
          label: Text(l10n.addReminder),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.teal,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 2,
          ),
        ),
      ),
    );
  }

  /// Get the display text for reminder status
  String _getReminderStatusText(Reminder reminder) {
    switch (reminder.status) {
      case ReminderStatus.active:
        return 'Actif';
      case ReminderStatus.paused:
        return 'En pause';
      case ReminderStatus.archived:
        return 'Archivé';
    }
  }

  /// Get the color for reminder status
  Color _getReminderStatusColor(Reminder reminder) {
    switch (reminder.status) {
      case ReminderStatus.active:
        return AppColors.success;
      case ReminderStatus.paused:
        return AppColors.warning;
      case ReminderStatus.archived:
        return AppColors.grey600;
    }
  }
}
