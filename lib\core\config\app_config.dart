import 'environment_config.dart';

/// Application configuration constants
/// Now uses EnvironmentConfig for environment-aware settings
class AppConfig {
  // Supabase Configuration - Environment aware
  static String get supabaseUrl => EnvironmentConfig.supabaseUrl;
  static String get supabaseAnonKey => EnvironmentConfig.supabaseAnonKey;

  // App Information - Environment aware
  static String get appName => EnvironmentConfig.appName;
  static String get appVersion => EnvironmentConfig.appVersion;
  static String get appBuildNumber => EnvironmentConfig.appBuildNumber;

  // API Configuration - Environment aware
  static int get apiTimeoutSeconds => EnvironmentConfig.apiTimeoutSeconds;
  static int get maxRetryAttempts => EnvironmentConfig.maxRetryAttempts;

  // Local Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userProfileKey = 'user_profile';
  static const String onboardingCompletedKey = 'onboarding_completed';
  static const String themePreferenceKey = 'theme_preference';

  // Feature Flags - Environment aware
  static bool get enableNotifications => EnvironmentConfig.enableNotifications;
  static bool get enableBiometricAuth => EnvironmentConfig.enableBiometricAuth;
  static bool get enableOfflineMode => EnvironmentConfig.enableOfflineMode;
  static bool get enableAnalytics => EnvironmentConfig.enableAnalytics;

  // Medicine Configuration
  static const int defaultExpiryWarningMonths = 1;
  static const int defaultLowStockThreshold = 5;
  static const int maxMedicineNameLength = 100;
  static const int maxNotesLength = 500;

  // UI Configuration
  static const double defaultBorderRadius = 16.0;
  static const double defaultPadding = 20.0;
  static const double defaultMargin = 16.0;
  static const int animationDurationMs = 300;

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // Cache Configuration
  static const int cacheExpirationHours = 24;
  static const int maxCacheSize = 100; // MB

  // Validation Rules
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 128;
  static const int minNameLength = 2;
  static const int maxNameLength = 50;

  // Environment Detection - Environment aware
  static bool get isDebug => EnvironmentConfig.isDebug;
  static bool get isProduction => EnvironmentConfig.isProduction;

  // Logging Configuration - Environment aware
  static bool get enableLogging => EnvironmentConfig.enableLogging;
  static bool get enableVerboseLogging =>
      EnvironmentConfig.enableVerboseLogging;
}
