import 'package:dartz/dartz.dart';
import '../entities/household.dart';
import '../entities/household_invitation.dart';
import '../../core/error/failures.dart';

/// Repository interface for household operations
abstract class HouseholdRepository {
  /// Create a new household
  Future<Either<Failure, Household>> createHousehold({
    required String name,
    String? description,
    required String ownerId,
  });

  /// Get household by ID
  Future<Either<Failure, Household>> getHouseholdById(String householdId);

  /// Get household by invite code
  Future<Either<Failure, Household>> getHouseholdByInviteCode(
      String inviteCode);

  /// Update household information
  Future<Either<Failure, Household>> updateHousehold({
    required String householdId,
    String? name,
    String? description,
  });

  /// Generate new invite code for household
  Future<Either<Failure, String>> generateInviteCode(String householdId);

  /// Deactivate household
  Future<Either<Failure, void>> deactivateHousehold(String householdId);

  /// Join household using invite code
  Future<Either<Failure, HouseholdMember>> joinHousehold({
    required String userId,
    required String inviteCode,
  });

  /// Leave household
  Future<Either<Failure, void>> leaveHousehold({
    required String userId,
    required String householdId,
  });

  /// Get household members
  Future<Either<Failure, List<HouseholdMember>>> getHouseholdMembers(
      String householdId);

  /// Update member role
  Future<Either<Failure, HouseholdMember>> updateMemberRole({
    required String memberId,
    required String newRole,
  });

  /// Remove member from household
  Future<Either<Failure, void>> removeMember(String memberId);

  /// Get user's households
  Future<Either<Failure, List<Household>>> getUserHouseholds(String userId);

  /// Check if user is member of household
  Future<Either<Failure, bool>> isUserMemberOfHousehold({
    required String userId,
    required String householdId,
  });

  /// Validate invite code
  Future<Either<Failure, bool>> validateInviteCode(String inviteCode);

  /// Get active invitations for a household
  Future<Either<Failure, List<HouseholdInvitation>>> getActiveInvitations(
      String householdId);
}
