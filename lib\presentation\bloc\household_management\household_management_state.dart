import 'package:equatable/equatable.dart';
import '../../../domain/entities/household.dart';
import '../../../domain/entities/household_invitation.dart';

/// Base class for household management states
abstract class HouseholdManagementState extends Equatable {
  const HouseholdManagementState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class HouseholdManagementInitial extends HouseholdManagementState {
  const HouseholdManagementInitial();
}

/// Loading state
class HouseholdManagementLoading extends HouseholdManagementState {
  const HouseholdManagementLoading();
}

/// State when households are loaded successfully
class HouseholdsLoaded extends HouseholdManagementState {
  final List<Household> households;
  final List<HouseholdMember> memberships;
  final Map<String, List<HouseholdInvitation>> invitationsByHousehold;

  const HouseholdsLoaded({
    required this.households,
    required this.memberships,
    this.invitationsByHousehold = const {},
  });

  @override
  List<Object?> get props => [households, memberships, invitationsByHousehold];

  /// Check if user has any households
  bool get isEmpty => households.isEmpty;

  /// Get household by ID
  Household? getHouseholdById(String id) {
    try {
      return households.firstWhere((h) => h.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get membership for household
  HouseholdMember? getMembershipForHousehold(String householdId) {
    try {
      return memberships.firstWhere((m) => m.householdId == householdId);
    } catch (e) {
      return null;
    }
  }

  /// Check if user is owner of household
  bool isOwner(String householdId) {
    final membership = getMembershipForHousehold(householdId);
    return membership?.role == 'owner';
  }

  /// Check if user is admin of household
  bool isAdmin(String householdId) {
    final membership = getMembershipForHousehold(householdId);
    return membership?.role == 'admin' || membership?.role == 'owner';
  }

  /// Get invitations for a specific household
  List<HouseholdInvitation> getInvitationsForHousehold(String householdId) {
    return invitationsByHousehold[householdId] ?? [];
  }

  /// Check if household has active invitations
  bool hasInvitationsForHousehold(String householdId) {
    return getInvitationsForHousehold(householdId).isNotEmpty;
  }

  /// Create a copy with updated invitations for a household
  HouseholdsLoaded copyWithInvitations(
      String householdId, List<HouseholdInvitation> invitations) {
    final updatedInvitations =
        Map<String, List<HouseholdInvitation>>.from(invitationsByHousehold);
    updatedInvitations[householdId] = invitations;

    return HouseholdsLoaded(
      households: households,
      memberships: memberships,
      invitationsByHousehold: updatedInvitations,
    );
  }
}

/// State when invite code is generated
class InviteCodeGenerated extends HouseholdManagementState {
  final String inviteCode;
  final String householdId;

  const InviteCodeGenerated({
    required this.inviteCode,
    required this.householdId,
  });

  @override
  List<Object?> get props => [inviteCode, householdId];
}

/// State when invite code is validated
class InviteCodeValidated extends HouseholdManagementState {
  final Household household;
  final String inviteCode;

  const InviteCodeValidated({
    required this.household,
    required this.inviteCode,
  });

  @override
  List<Object?> get props => [household, inviteCode];
}

/// State when household is created successfully
class HouseholdCreated extends HouseholdManagementState {
  final Household household;

  const HouseholdCreated({required this.household});

  @override
  List<Object?> get props => [household];
}

/// State when user joins household successfully
class HouseholdJoined extends HouseholdManagementState {
  final Household household;
  final HouseholdMember membership;

  const HouseholdJoined({
    required this.household,
    required this.membership,
  });

  @override
  List<Object?> get props => [household, membership];
}

/// State when user leaves household successfully
class HouseholdLeft extends HouseholdManagementState {
  final String householdId;

  const HouseholdLeft({required this.householdId});

  @override
  List<Object?> get props => [householdId];
}

/// State when active invitations are loaded
class ActiveInvitationsLoaded extends HouseholdManagementState {
  final List<HouseholdInvitation> invitations;
  final String householdId;

  const ActiveInvitationsLoaded({
    required this.invitations,
    required this.householdId,
  });

  @override
  List<Object?> get props => [invitations, householdId];

  /// Check if there are any active invitations
  bool get hasInvitations => invitations.isNotEmpty;

  /// Get invitation by token
  HouseholdInvitation? getInvitationByToken(String token) {
    try {
      return invitations.firstWhere((inv) => inv.token == token);
    } catch (e) {
      return null;
    }
  }
}

/// Error state
class HouseholdManagementError extends HouseholdManagementState {
  final String message;
  final String? errorCode;

  const HouseholdManagementError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
}
