import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';

import '../../../core/error/failures.dart';
import '../../../domain/entities/reminder.dart';
import '../../../domain/entities/medicine.dart';
import '../../../core/services/reminder_notification_integration_service.dart';
import '../../../domain/usecases/medicine/get_medicines_usecase.dart';

import '../../../domain/usecases/reminder/add_dose_history_usecase.dart';
import '../../../domain/usecases/reminder/add_reminder_usecase.dart';
import '../../../domain/usecases/reminder/delete_reminder_usecase.dart';
import '../../../domain/usecases/reminder/get_dose_history_usecase.dart';
import '../../../domain/usecases/reminder/get_reminders_usecase.dart';
import '../../../domain/usecases/reminder/get_batch_reminders_usecase.dart';
import '../../../domain/usecases/reminder/update_reminder_usecase.dart';
import 'reminder_event.dart';
import 'reminder_state.dart';

@injectable
class ReminderBloc extends Bloc<ReminderEvent, ReminderState> {
  final GetRemindersUseCase getRemindersUseCase;
  final GetBatchRemindersUseCase getBatchRemindersUseCase;
  final AddReminderUseCase addReminderUseCase;
  final UpdateReminderUseCase updateReminderUseCase;
  final DeleteReminderUseCase deleteReminderUseCase;
  final GetDoseHistoryUseCase getDoseHistoryUseCase;
  final AddDoseHistoryUseCase addDoseHistoryUseCase;
  final GetMedicinesUseCase getMedicinesUseCase;
  final ReminderNotificationIntegrationService notificationIntegrationService;

  // Keep track of all loaded reminders
  final List<Reminder> _allReminders = [];

  ReminderBloc({
    required this.getRemindersUseCase,
    required this.getBatchRemindersUseCase,
    required this.addReminderUseCase,
    required this.updateReminderUseCase,
    required this.deleteReminderUseCase,
    required this.getDoseHistoryUseCase,
    required this.addDoseHistoryUseCase,
    required this.getMedicinesUseCase,
    required this.notificationIntegrationService,
  }) : super(ReminderInitial()) {
    on<ReminderEvent>((event, emit) {
      if (kDebugMode) {
        print('[ReminderBloc] Event: $event');
      }
    });
    on<LoadReminders>(_onLoadReminders);
    on<LoadBatchReminders>(_onLoadBatchReminders);
    on<ClearReminders>(_onClearReminders);
    on<AddReminder>(_onAddReminder);
    on<UpdateReminder>(_onUpdateReminder);
    on<DeleteReminder>(_onDeleteReminder);
    on<LoadDoseHistory>(_onLoadDoseHistory);
    on<AddDoseHistory>(_onAddDoseHistory);
    on<ToggleReminderActive>(_onToggleReminderActive);
    on<PauseReminder>(_onPauseReminder);
    on<ResumeReminder>(_onResumeReminder);
    on<ArchiveReminder>(_onArchiveReminder);
  }

  @override
  void onTransition(Transition<ReminderEvent, ReminderState> transition) {
    super.onTransition(transition);
    if (kDebugMode) {
      print('[ReminderBloc] Transition: $transition');
    }
  }

  Future<void> _onLoadReminders(
    LoadReminders event,
    Emitter<ReminderState> emit,
  ) async {
    // Don't emit loading if we're accumulating reminders
    if (_allReminders.isEmpty) {
      emit(ReminderLoading());
    }

    final result = await getRemindersUseCase(
      GetRemindersParams(userMedicineId: event.userMedicineId),
    );

    result.fold(
      (failure) => emit(ReminderError(_mapFailureToMessage(failure))),
      (reminders) {
        // Add new reminders to the accumulated list, avoiding duplicates
        for (final reminder in reminders) {
          if (!_allReminders.any((r) => r.id == reminder.id)) {
            _allReminders.add(reminder);
          }
        }
        emit(RemindersLoaded(List.from(_allReminders)));
      },
    );
  }

  Future<void> _onLoadBatchReminders(
    LoadBatchReminders event,
    Emitter<ReminderState> emit,
  ) async {
    emit(ReminderLoading());

    final result = await getBatchRemindersUseCase(
      GetBatchRemindersParams(userMedicineIds: event.userMedicineIds),
    );

    result.fold(
      (failure) => emit(ReminderError(_mapFailureToMessage(failure))),
      (reminders) {
        _allReminders.clear();
        _allReminders.addAll(reminders);
        emit(RemindersLoaded(List.from(_allReminders)));
      },
    );
  }

  Future<void> _onClearReminders(
    ClearReminders event,
    Emitter<ReminderState> emit,
  ) async {
    _allReminders.clear();
    emit(RemindersLoaded([]));
  }

  Future<void> _onAddReminder(
    AddReminder event,
    Emitter<ReminderState> emit,
  ) async {
    emit(ReminderLoading());

    final result = await addReminderUseCase(
      AddReminderParams(reminder: event.reminder),
    );

    result.fold(
      (failure) => emit(ReminderError(_mapFailureToMessage(failure))),
      (reminder) async {
        // Schedule notifications for the new reminder
        await _scheduleNotificationsForReminder(reminder);

        emit(ReminderAdded(reminder));
        emit(const ReminderOperationSuccess('Rappel ajouté avec succès'));
      },
    );
  }

  Future<void> _onUpdateReminder(
    UpdateReminder event,
    Emitter<ReminderState> emit,
  ) async {
    emit(ReminderLoading());

    final result = await updateReminderUseCase(
      UpdateReminderParams(reminder: event.reminder),
    );

    result.fold(
      (failure) => emit(ReminderError(_mapFailureToMessage(failure))),
      (reminder) {
        emit(ReminderUpdated(reminder));
        emit(const ReminderOperationSuccess('Rappel mis à jour avec succès'));
      },
    );
  }

  Future<void> _onDeleteReminder(
    DeleteReminder event,
    Emitter<ReminderState> emit,
  ) async {
    final result = await deleteReminderUseCase(
      DeleteReminderParams(reminderId: event.reminderId),
    );

    result.fold(
      (failure) => emit(ReminderError(_mapFailureToMessage(failure))),
      (_) {
        _allReminders.removeWhere((r) => r.id == event.reminderId);
        emit(RemindersLoaded(List.from(_allReminders)));
        emit(const ReminderOperationSuccess('Rappel supprimé avec succès'));
      },
    );
  }

  Future<void> _onLoadDoseHistory(
    LoadDoseHistory event,
    Emitter<ReminderState> emit,
  ) async {
    emit(ReminderLoading());

    final result = await getDoseHistoryUseCase(
      GetDoseHistoryParams(userMedicineId: event.userMedicineId),
    );

    result.fold(
      (failure) => emit(ReminderError(_mapFailureToMessage(failure))),
      (doseHistory) => emit(DoseHistoryLoaded(doseHistory)),
    );
  }

  Future<void> _onAddDoseHistory(
    AddDoseHistory event,
    Emitter<ReminderState> emit,
  ) async {
    final result = await addDoseHistoryUseCase(
      AddDoseHistoryParams(doseHistory: event.doseHistory),
    );

    result.fold(
      (failure) => emit(ReminderError(_mapFailureToMessage(failure))),
      (_) {
        // Emit success message first
        emit(const ReminderOperationSuccess('Dose enregistrée avec succès'));

        // Then, re-emit the full list of reminders to ensure the UI rebuilds correctly
        emit(RemindersLoaded(List.from(_allReminders)));
      },
    );
  }

  Future<void> _onToggleReminderActive(
    ToggleReminderActive event,
    Emitter<ReminderState> emit,
  ) async {
    // This would typically load the current reminder, update its isActive status,
    // and then call UpdateReminder. For simplicity, we'll emit a success message.
    emit(ReminderOperationSuccess(
      event.isActive ? 'Rappel activé' : 'Rappel désactivé',
    ));
  }

  Future<void> _onPauseReminder(
    PauseReminder event,
    Emitter<ReminderState> emit,
  ) async {
    // Find the reminder in our local cache
    final reminderIndex =
        _allReminders.indexWhere((r) => r.id == event.reminderId);
    if (reminderIndex == -1) {
      emit(const ReminderError('Rappel non trouvé'));
      return;
    }

    final reminder = _allReminders[reminderIndex];
    final updatedReminder = reminder.copyWith(status: ReminderStatus.paused);

    // Update the reminder via the use case
    final result = await updateReminderUseCase(
      UpdateReminderParams(reminder: updatedReminder),
    );

    result.fold(
      (failure) => emit(ReminderError(_mapFailureToMessage(failure))),
      (updatedReminder) {
        // Update local cache
        _allReminders[reminderIndex] = updatedReminder;
        emit(RemindersLoaded(List.from(_allReminders)));
        emit(const ReminderOperationSuccess('Rappel mis en pause'));
      },
    );
  }

  Future<void> _onResumeReminder(
    ResumeReminder event,
    Emitter<ReminderState> emit,
  ) async {
    // Find the reminder in our local cache
    final reminderIndex =
        _allReminders.indexWhere((r) => r.id == event.reminderId);
    if (reminderIndex == -1) {
      emit(const ReminderError('Rappel non trouvé'));
      return;
    }

    final reminder = _allReminders[reminderIndex];
    final updatedReminder = reminder.copyWith(
      status: ReminderStatus.active,
      isActive: true, // Ensure isActive is set to true when resuming
    );

    // Update the reminder via the use case
    final result = await updateReminderUseCase(
      UpdateReminderParams(reminder: updatedReminder),
    );

    result.fold(
      (failure) => emit(ReminderError(_mapFailureToMessage(failure))),
      (updatedReminder) {
        // Update local cache
        _allReminders[reminderIndex] = updatedReminder;
        emit(RemindersLoaded(List.from(_allReminders)));
        emit(const ReminderOperationSuccess('Rappel activé'));
      },
    );
  }

  Future<void> _onArchiveReminder(
    ArchiveReminder event,
    Emitter<ReminderState> emit,
  ) async {
    // Find the reminder in our local cache
    final reminderIndex =
        _allReminders.indexWhere((r) => r.id == event.reminderId);
    if (reminderIndex == -1) {
      emit(const ReminderError('Rappel non trouvé'));
      return;
    }

    final reminder = _allReminders[reminderIndex];
    final updatedReminder = reminder.copyWith(status: ReminderStatus.archived);

    // Update the reminder via the use case
    final result = await updateReminderUseCase(
      UpdateReminderParams(reminder: updatedReminder),
    );

    result.fold(
      (failure) => emit(ReminderError(_mapFailureToMessage(failure))),
      (updatedReminder) {
        // Update local cache
        _allReminders[reminderIndex] = updatedReminder;
        emit(RemindersLoaded(List.from(_allReminders)));
        emit(const ReminderOperationSuccess('Rappel archivé'));
      },
    );
  }

  String _mapFailureToMessage(Failure failure) {
    switch (failure.runtimeType) {
      case ServerFailure _:
        return 'Erreur du serveur. Veuillez réessayer plus tard.';
      case NetworkFailure _:
        return 'Erreur de connexion. Vérifiez votre connexion internet.';
      default:
        return 'Une erreur inattendue s\'est produite.';
    }
  }

  /// Helper method to schedule notifications for a reminder
  Future<void> _scheduleNotificationsForReminder(Reminder reminder) async {
    try {
      // For now, create a simple test notification to verify the system works
      // TODO: Implement proper medicine lookup and notification scheduling

      // Create a mock medicine for testing
      final testMedicine = Medicine(
        id: reminder.userMedicineId,
        householdId: 'test-household',
        customName: reminder.name ?? 'Test Medicine',
        isCustom: true,
        quantity: 10,
        lowStockThreshold: 5,
        tags: const [],
        createdAt: DateTime.now(),
      );

      await notificationIntegrationService.onReminderCreated(
        reminder: reminder,
        medicine: testMedicine,
      );

      if (kDebugMode) {
        print(
            'Successfully scheduled test notifications for reminder ${reminder.id}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error scheduling notifications for reminder ${reminder.id}: $e');
      }
    }
  }
}
