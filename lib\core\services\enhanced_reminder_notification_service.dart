import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:timezone/timezone.dart' as tz;
import '../../../domain/entities/reminder.dart';
import '../../../domain/entities/medicine.dart';
import 'notification_service.dart';
import '../../utils/logger.dart';

/// Enhanced notification service specifically for reminder management
/// Supports pre-reminder, main reminder, and post-reminder notifications
/// with configurable snooze functionality
class EnhancedReminderNotificationService {
  static final EnhancedReminderNotificationService _instance =
      EnhancedReminderNotificationService._internal();
  factory EnhancedReminderNotificationService() => _instance;
  EnhancedReminderNotificationService._internal();

  final NotificationService _notificationService = NotificationService();

  // Notification ID ranges to avoid conflicts
  static const int _preReminderIdOffset = 100000;
  static const int _mainReminderIdOffset = 200000;
  static const int _postReminderIdOffset = 300000;
  static const int _snoozeIdOffset = 400000;

  // Default configuration
  static const Duration _preReminderDuration = Duration(minutes: 15);
  static const Duration _postReminderDelay = Duration(minutes: 30);
  static const List<Duration> _snoozeDurations = [
    Duration(minutes: 5),
    Duration(minutes: 15),
    Duration(minutes: 30),
    Duration(hours: 1),
  ];

  /// Initialize the enhanced notification service
  Future<bool> initialize() async {
    return await _notificationService.initialize();
  }

  /// Request notification permissions
  Future<bool> requestPermissions() async {
    return await _notificationService.requestPermissions();
  }

  /// Schedule comprehensive notification system for a reminder
  Future<void> scheduleReminderNotifications({
    required Reminder reminder,
    required Medicine medicine,
    Duration preReminderDuration = _preReminderDuration,
    Duration postReminderDelay = _postReminderDelay,
  }) async {
    if (!reminder.isActive) return;

    // Schedule notifications based on frequency type
    switch (reminder.frequencyType) {
      case 'DAILY':
        await _scheduleDailyReminderNotifications(
            reminder, medicine, preReminderDuration, postReminderDelay);
        break;
      case 'WEEKLY':
        await _scheduleWeeklyReminderNotifications(
            reminder, medicine, preReminderDuration, postReminderDelay);
        break;
      case 'HOURLY_INTERVAL':
        await _scheduleIntervalReminderNotifications(
            reminder, medicine, preReminderDuration, postReminderDelay);
        break;
      case 'SPECIFIC_DATES':
        await _scheduleSpecificDateReminderNotifications(
            reminder, medicine, preReminderDuration, postReminderDelay);
        break;
    }
  }

  /// Schedule snooze notification
  Future<void> scheduleSnoozeNotification({
    required Reminder reminder,
    required Medicine medicine,
    required Duration snoozeDuration,
    int snoozeIndex = 0,
  }) async {
    final notificationId =
        _generateSnoozeNotificationId(reminder.id!, snoozeIndex);
    final snoozeTime = DateTime.now().add(snoozeDuration);

    await _notificationService.scheduleNotification(
      id: notificationId,
      title: '⏰ Rappel reporté - ${medicine.name}',
      body:
          'Il est temps de prendre votre médicament (reporté de ${_formatDuration(snoozeDuration)})',
      scheduledDate: snoozeTime,
      payload: 'snooze_${reminder.id}_${medicine.id}_$snoozeIndex',
    );

    if (kDebugMode) {
      AppLogger.database(
          'Scheduled snooze notification for ${medicine.name} in ${_formatDuration(snoozeDuration)}');
    }
  }

  /// Cancel all notifications for a reminder
  Future<void> cancelReminderNotifications(String reminderId) async {
    // Cancel pre-reminder notifications
    for (int i = 0; i < 10; i++) {
      // Assume max 10 times per day
      await _notificationService.cancelNotification(
          _generatePreReminderNotificationId(reminderId, i));
    }

    // Cancel main reminder notifications
    for (int i = 0; i < 10; i++) {
      await _notificationService.cancelNotification(
          _generateMainReminderNotificationId(reminderId, i));
    }

    // Cancel post-reminder notifications
    for (int i = 0; i < 10; i++) {
      await _notificationService.cancelNotification(
          _generatePostReminderNotificationId(reminderId, i));
    }

    // Cancel snooze notifications
    for (int i = 0; i < 20; i++) {
      // Assume max 20 snooze instances
      await _notificationService
          .cancelNotification(_generateSnoozeNotificationId(reminderId, i));
    }
  }

  /// Schedule daily reminder notifications
  Future<void> _scheduleDailyReminderNotifications(
    Reminder reminder,
    Medicine medicine,
    Duration preReminderDuration,
    Duration postReminderDelay,
  ) async {
    for (int i = 0; i < reminder.times.length; i++) {
      final timeString = reminder.times[i];
      final timeParts = timeString.split(':');
      final hour = int.parse(timeParts[0]);
      final minute = int.parse(timeParts[1]);

      final now = tz.TZDateTime.now(tz.local);
      var scheduledTime = now.copyWith(
        hour: hour,
        minute: minute,
        second: 0,
        millisecond: 0,
      );

      // If the time has passed today, schedule for tomorrow
      if (scheduledTime.isBefore(now)) {
        scheduledTime = scheduledTime.add(const Duration(days: 1));
      }

      // Schedule pre-reminder notification
      final preReminderTime = scheduledTime.subtract(preReminderDuration);
      if (preReminderTime.isAfter(now)) {
        await _notificationService.scheduleNotification(
          id: _generatePreReminderNotificationId(reminder.id!, i),
          title: '⏰ Rappel dans ${preReminderDuration.inMinutes} minutes',
          body: 'Préparez-vous à prendre ${medicine.name}',
          scheduledDate: preReminderTime,
          payload: 'pre_reminder_${reminder.id}_${medicine.id}_$i',
        );
      }

      // Schedule main reminder notification
      await _notificationService.scheduleNotification(
        id: _generateMainReminderNotificationId(reminder.id!, i),
        title: '💊 Il est temps de prendre votre médicament',
        body:
            '${medicine.name} - ${reminder.dosageAmount} ${reminder.dosageUnit}',
        scheduledDate: scheduledTime,
        payload: 'main_reminder_${reminder.id}_${medicine.id}_$i',
      );

      // Schedule post-reminder notification (for missed doses)
      final postReminderTime = scheduledTime.add(postReminderDelay);
      await _notificationService.scheduleNotification(
        id: _generatePostReminderNotificationId(reminder.id!, i),
        title: '⚠️ Médicament non pris',
        body:
            'Avez-vous pris ${medicine.displayName} ? Marquez-le comme pris ou reportez-le.',
        scheduledDate: postReminderTime,
        payload: 'post_reminder_${reminder.id}_${medicine.id}_$i',
      );
    }
  }

  /// Schedule weekly reminder notifications
  Future<void> _scheduleWeeklyReminderNotifications(
    Reminder reminder,
    Medicine medicine,
    Duration preReminderDuration,
    Duration postReminderDelay,
  ) async {
    // Implementation for weekly reminders
    // Similar to daily but with weekly scheduling logic
    for (int dayIndex = 0;
        dayIndex < reminder.frequencyDays.length;
        dayIndex++) {
      final dayOfWeek = reminder.frequencyDays[dayIndex];

      for (int timeIndex = 0; timeIndex < reminder.times.length; timeIndex++) {
        final timeString = reminder.times[timeIndex];
        final timeParts = timeString.split(':');
        final hour = int.parse(timeParts[0]);
        final minute = int.parse(timeParts[1]);

        final now = tz.TZDateTime.now(tz.local);
        var scheduledDate = now.copyWith(
          hour: hour,
          minute: minute,
          second: 0,
          millisecond: 0,
        );

        // Adjust to the correct day of week
        final daysUntilTarget = (dayOfWeek - scheduledDate.weekday) % 7;
        scheduledDate = scheduledDate.add(Duration(days: daysUntilTarget));

        if (scheduledDate.isBefore(now)) {
          scheduledDate = scheduledDate.add(const Duration(days: 7));
        }

        final notificationIndex = dayIndex * reminder.times.length + timeIndex;

        // Schedule pre-reminder, main reminder, and post-reminder
        await _scheduleTripleNotification(reminder, medicine, scheduledDate,
            notificationIndex, preReminderDuration, postReminderDelay);
      }
    }
  }

  /// Schedule interval-based reminder notifications
  Future<void> _scheduleIntervalReminderNotifications(
    Reminder reminder,
    Medicine medicine,
    Duration preReminderDuration,
    Duration postReminderDelay,
  ) async {
    // Implementation for hourly interval reminders
    final intervalHours = reminder.frequencyValue ?? 1;
    final now = DateTime.now();

    // Schedule first notification
    var nextTime = now.add(Duration(hours: intervalHours));

    for (int i = 0; i < 24; i++) {
      // Schedule for next 24 occurrences
      await _scheduleTripleNotification(reminder, medicine, nextTime, i,
          preReminderDuration, postReminderDelay);

      nextTime = nextTime.add(Duration(hours: intervalHours));
    }
  }

  /// Schedule specific date reminder notifications
  Future<void> _scheduleSpecificDateReminderNotifications(
    Reminder reminder,
    Medicine medicine,
    Duration preReminderDuration,
    Duration postReminderDelay,
  ) async {
    final now = DateTime.now();

    for (int dateIndex = 0;
        dateIndex < reminder.specificDates.length;
        dateIndex++) {
      final specificDate = reminder.specificDates[dateIndex];

      // Skip past dates
      if (specificDate.isBefore(now)) continue;

      for (int timeIndex = 0; timeIndex < reminder.times.length; timeIndex++) {
        final timeString = reminder.times[timeIndex];
        final timeParts = timeString.split(':');
        final hour = int.parse(timeParts[0]);
        final minute = int.parse(timeParts[1]);

        final scheduledDateTime = DateTime(
          specificDate.year,
          specificDate.month,
          specificDate.day,
          hour,
          minute,
        );

        final notificationIndex = dateIndex * reminder.times.length + timeIndex;

        await _scheduleTripleNotification(reminder, medicine, scheduledDateTime,
            notificationIndex, preReminderDuration, postReminderDelay);
      }
    }
  }

  /// Helper method to schedule pre, main, and post reminder notifications
  Future<void> _scheduleTripleNotification(
    Reminder reminder,
    Medicine medicine,
    DateTime scheduledTime,
    int index,
    Duration preReminderDuration,
    Duration postReminderDelay,
  ) async {
    final now = DateTime.now();

    // Pre-reminder
    final preReminderTime = scheduledTime.subtract(preReminderDuration);
    if (preReminderTime.isAfter(now)) {
      await _notificationService.scheduleNotification(
        id: _generatePreReminderNotificationId(reminder.id!, index),
        title: '⏰ Rappel dans ${preReminderDuration.inMinutes} minutes',
        body: 'Préparez-vous à prendre ${medicine.displayName}',
        scheduledDate: preReminderTime,
        payload: 'pre_reminder_${reminder.id}_${medicine.id}_$index',
      );
    }

    // Main reminder
    if (scheduledTime.isAfter(now)) {
      await _notificationService.scheduleNotification(
        id: _generateMainReminderNotificationId(reminder.id!, index),
        title: '💊 Il est temps de prendre votre médicament',
        body:
            '${medicine.name} - ${reminder.dosageAmount} ${reminder.dosageUnit}',
        scheduledDate: scheduledTime,
        payload: 'main_reminder_${reminder.id}_${medicine.id}_$index',
      );
    }

    // Post-reminder
    final postReminderTime = scheduledTime.add(postReminderDelay);
    if (postReminderTime.isAfter(now)) {
      await _notificationService.scheduleNotification(
        id: _generatePostReminderNotificationId(reminder.id!, index),
        title: '⚠️ Médicament non pris',
        body:
            'Avez-vous pris ${medicine.displayName} ? Marquez-le comme pris ou reportez-le.',
        scheduledDate: postReminderTime,
        payload: 'post_reminder_${reminder.id}_${medicine.id}_$index',
      );
    }
  }

  /// Generate notification IDs
  int _generatePreReminderNotificationId(String reminderId, int index) {
    return _preReminderIdOffset + reminderId.hashCode.abs() % 10000 + index;
  }

  int _generateMainReminderNotificationId(String reminderId, int index) {
    return _mainReminderIdOffset + reminderId.hashCode.abs() % 10000 + index;
  }

  int _generatePostReminderNotificationId(String reminderId, int index) {
    return _postReminderIdOffset + reminderId.hashCode.abs() % 10000 + index;
  }

  int _generateSnoozeNotificationId(String reminderId, int snoozeIndex) {
    return _snoozeIdOffset + reminderId.hashCode.abs() % 10000 + snoozeIndex;
  }

  /// Format duration for display
  String _formatDuration(Duration duration) {
    if (duration.inHours > 0) {
      return '${duration.inHours}h${duration.inMinutes % 60 > 0 ? ' ${duration.inMinutes % 60}min' : ''}';
    } else {
      return '${duration.inMinutes}min';
    }
  }

  /// Get available snooze durations
  static List<Duration> get availableSnoozeDurations => _snoozeDurations;

  /// Get snooze duration labels
  static List<String> get snoozeDurationLabels => [
        '5 minutes',
        '15 minutes',
        '30 minutes',
        '1 heure',
      ];
}
