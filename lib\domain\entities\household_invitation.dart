import 'package:equatable/equatable.dart';

/// Household invitation entity representing an invitation to join a household
class HouseholdInvitation extends Equatable {
  final String id;
  final String householdId;
  final String invitedBy;
  final String email;
  final String token;
  final DateTime expiresAt;
  final String status;
  final Map<String, dynamic>? permissions;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const HouseholdInvitation({
    required this.id,
    required this.householdId,
    required this.invitedBy,
    required this.email,
    required this.token,
    required this.expiresAt,
    required this.status,
    this.permissions,
    required this.createdAt,
    this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        householdId,
        invitedBy,
        email,
        token,
        expiresAt,
        status,
        permissions,
        createdAt,
        updatedAt,
      ];

  /// Check if invitation is expired
  bool get isExpired => DateTime.now().isAfter(expiresAt);

  /// Check if invitation is pending
  bool get isPending => status == 'pending';

  /// Check if invitation is accepted
  bool get isAccepted => status == 'accepted';

  /// Check if invitation is active (pending and not expired)
  bool get isActive => isPending && !isExpired;

  /// Get time remaining until expiration
  Duration get timeUntilExpiration => expiresAt.difference(DateTime.now());

  /// Get formatted expiration date
  String get formattedExpirationDate {
    final now = DateTime.now();
    final difference = expiresAt.difference(now);
    
    if (difference.isNegative) {
      return 'Expiré';
    }
    
    if (difference.inDays > 0) {
      return '${difference.inDays} jour${difference.inDays > 1 ? 's' : ''}';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} heure${difference.inHours > 1 ? 's' : ''}';
    } else {
      return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''}';
    }
  }

  /// Create a copy with updated fields
  HouseholdInvitation copyWith({
    String? id,
    String? householdId,
    String? invitedBy,
    String? email,
    String? token,
    DateTime? expiresAt,
    String? status,
    Map<String, dynamic>? permissions,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return HouseholdInvitation(
      id: id ?? this.id,
      householdId: householdId ?? this.householdId,
      invitedBy: invitedBy ?? this.invitedBy,
      email: email ?? this.email,
      token: token ?? this.token,
      expiresAt: expiresAt ?? this.expiresAt,
      status: status ?? this.status,
      permissions: permissions ?? this.permissions,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'household_id': householdId,
      'invited_by': invitedBy,
      'email': email,
      'token': token,
      'expires_at': expiresAt.toIso8601String(),
      'status': status,
      'permissions': permissions,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// Create from JSON
  factory HouseholdInvitation.fromJson(Map<String, dynamic> json) {
    return HouseholdInvitation(
      id: json['id'] as String,
      householdId: json['household_id'] as String,
      invitedBy: json['invited_by'] as String,
      email: json['email'] as String,
      token: json['token'] as String,
      expiresAt: DateTime.parse(json['expires_at'] as String),
      status: json['status'] as String,
      permissions: json['permissions'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
    );
  }
}
