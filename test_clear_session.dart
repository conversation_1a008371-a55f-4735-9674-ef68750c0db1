import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Supabase with dev environment
  await Supabase.initialize(
    url: 'https://ixqjqjqjqjqjqjqjqjqj.supabase.co', // Replace with actual dev URL
    anonKey: 'your-anon-key', // Replace with actual dev anon key
  );
  
  final supabase = Supabase.instance.client;
  
  print('Current user: ${supabase.auth.currentUser?.email}');
  
  // Clear the session
  await supabase.auth.signOut();
  
  print('Session cleared. Current user: ${supabase.auth.currentUser?.email}');
  
  print('Test completed. You can now test the registration flow with a fresh session.');
}
