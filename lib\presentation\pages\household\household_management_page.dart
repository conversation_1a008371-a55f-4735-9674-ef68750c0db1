import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '../../../core/theme/app_colors.dart';
import '../../../l10n/generated/app_localizations.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../domain/entities/household.dart';
import '../../../domain/entities/household_invitation.dart';
import '../../bloc/household_management/household_management_bloc.dart';
import '../../bloc/household_management/household_management_event.dart';
import '../../bloc/household_management/household_management_state.dart';

/// Page for managing user's households
class HouseholdManagementPage extends StatefulWidget {
  const HouseholdManagementPage({super.key});

  @override
  State<HouseholdManagementPage> createState() =>
      _HouseholdManagementPageState();
}

class _HouseholdManagementPageState extends State<HouseholdManagementPage> {
  @override
  void initState() {
    super.initState();
    // Load user's households when page opens
    context.read<HouseholdManagementBloc>().add(const LoadUserHouseholds());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.teal,
      body: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.only(
              top: 60,
              left: 20,
              right: 20,
              bottom: 20,
            ),
            child: Row(
              children: [
                IconButton(
                  onPressed: () => context.pop(),
                  icon: const Icon(
                    Icons.arrow_back,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Gestion des Foyers',
                    style: AppTextStyles.headlineMedium.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Content
          Expanded(
            child: Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(24),
                ),
              ),
              child: BlocConsumer<HouseholdManagementBloc,
                  HouseholdManagementState>(
                listener: (context, state) {
                  if (state is HouseholdManagementError) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(state.message),
                        backgroundColor: AppColors.error,
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  } else if (state is HouseholdCreated) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                            'Foyer "${state.household.name}" créé avec succès'),
                        backgroundColor: AppColors.success,
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  } else if (state is HouseholdJoined) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                            'Vous avez rejoint le foyer "${state.household.name}"'),
                        backgroundColor: AppColors.success,
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  } else if (state is HouseholdLeft) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Vous avez quitté le foyer'),
                        backgroundColor: AppColors.warning,
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  } else if (state is InviteCodeGenerated) {
                    // Show QR code dialog when invite code is generated
                    _showQRCodeDialog(
                        context, state.inviteCode, state.householdId);
                  }
                },
                builder: (context, state) {
                  print('🔍 [MainBuilder] Current state: ${state.runtimeType}');

                  if (state is HouseholdManagementLoading) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }

                  if (state is HouseholdsLoaded) {
                    return _buildHouseholdsList(context, state);
                  }

                  // ActiveInvitationsLoaded state is no longer used
                  // Invitations are now part of HouseholdsLoaded state

                  if (state is HouseholdManagementError) {
                    return _buildErrorState(context, state);
                  }

                  // Initial or other states
                  return _buildEmptyState(context);
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHouseholdsList(BuildContext context, HouseholdsLoaded state) {
    if (state.isEmpty) {
      return _buildEmptyState(context);
    }

    return RefreshIndicator(
      onRefresh: () async {
        context.read<HouseholdManagementBloc>().add(const RefreshHouseholds());
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(20),
        itemCount: state.households.length + 1, // +1 for action buttons
        itemBuilder: (context, index) {
          if (index == state.households.length) {
            // Last item: action buttons
            return Padding(
              padding: const EdgeInsets.only(top: 24),
              child: _buildActionButtons(context),
            );
          }

          final household = state.households[index];
          final membership = state.getMembershipForHousehold(household.id);

          return _buildHouseholdCard(context, household, membership, state);
        },
      ),
    );
  }

  Widget _buildHouseholdCard(
    BuildContext context,
    Household household,
    HouseholdMember? membership,
    HouseholdsLoaded state,
  ) {
    final isOwner = state.isOwner(household.id);
    final isAdmin = state.isAdmin(household.id);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: AppColors.teal.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.home,
                    color: AppColors.teal,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        household.name,
                        style: AppTextStyles.titleMedium.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (household.description != null)
                        Text(
                          household.description!,
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.grey600,
                          ),
                        ),
                    ],
                  ),
                ),
                _buildRoleBadge(membership?.role ?? 'member'),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                if (isAdmin) ...[
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _showInviteDialog(context, household),
                      icon: const Icon(Icons.share, size: 18),
                      label: const Text('Inviter'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.teal,
                        side: BorderSide(color: AppColors.teal),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () =>
                        context.push('/family?householdId=${household.id}'),
                    icon: const Icon(Icons.people, size: 18),
                    label: const Text('Membres'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppColors.navy,
                      side: BorderSide(color: AppColors.navy),
                    ),
                  ),
                ),
                if (!isOwner) ...[
                  const SizedBox(width: 8),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () =>
                          _showLeaveConfirmation(context, household),
                      icon: const Icon(Icons.exit_to_app, size: 18),
                      label: const Text('Quitter'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.error,
                        side: BorderSide(color: AppColors.error),
                      ),
                    ),
                  ),
                ],
              ],
            ),
            // Active invitations section - visible to all household members
            _buildActiveInvitationsSection(context, household),
          ],
        ),
      ),
    );
  }

  Widget _buildRoleBadge(String role) {
    Color color;
    String label;

    switch (role) {
      case 'owner':
        color = AppColors.success;
        label = 'Propriétaire';
        break;
      case 'admin':
        color = AppColors.info;
        label = 'Admin';
        break;
      default:
        color = AppColors.grey400;
        label = 'Membre';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        label,
        style: AppTextStyles.bodySmall.copyWith(
          color: color,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildActiveInvitationsSection(
      BuildContext context, Household household) {
    return BlocBuilder<HouseholdManagementBloc, HouseholdManagementState>(
      builder: (context, state) {
        print('🔍 [ActiveInvitations] Current state: ${state.runtimeType}');

        // Only work with HouseholdsLoaded state
        if (state is! HouseholdsLoaded) {
          print(
              '🔍 [ActiveInvitations] State is not HouseholdsLoaded, hiding section');
          return const SizedBox.shrink();
        }

        // Check if invitations are already loaded for this household
        final invitations = state.getInvitationsForHousehold(household.id);
        final hasLoadedInvitations =
            state.invitationsByHousehold.containsKey(household.id);

        if (!hasLoadedInvitations) {
          print(
              '🔍 [ActiveInvitations] Need to load invitations for household: ${household.id}');
          // Trigger loading of active invitations
          WidgetsBinding.instance.addPostFrameCallback((_) {
            context.read<HouseholdManagementBloc>().add(
                  LoadActiveInvitations(householdId: household.id),
                );
          });
          return const SizedBox.shrink(); // Don't show anything while loading
        }

        print(
            '🔍 [ActiveInvitations] Found ${invitations.length} invitations for household');

        if (invitations.isEmpty) {
          print('🔍 [ActiveInvitations] No invitations to show');
          return const SizedBox
              .shrink(); // Don't show section if no invitations
        }

        print(
            '🔍 [ActiveInvitations] Showing ${invitations.length} invitations');

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 12),
            Text(
              'Invitations actives',
              style: AppTextStyles.titleSmall.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.grey700,
              ),
            ),
            const SizedBox(height: 8),
            ...invitations
                .map((invitation) => _buildInvitationTile(context, invitation)),
          ],
        );
      },
    );
  }

  Widget _buildInvitationTile(
      BuildContext context, HouseholdInvitation invitation) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.grey50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.grey200),
      ),
      child: Row(
        children: [
          Icon(
            Icons.qr_code,
            color: AppColors.teal,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  invitation.token,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontFamily: 'monospace',
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'Expire dans ${invitation.formattedExpirationDate}',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.grey600,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => _copyInvitationToken(context, invitation.token),
            icon: Icon(
              Icons.copy,
              color: AppColors.teal,
              size: 20,
            ),
            tooltip: 'Copier le code',
          ),
        ],
      ),
    );
  }

  void _copyInvitationToken(BuildContext context, String token) {
    Clipboard.setData(ClipboardData(text: token));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Code d\'invitation copié!'),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.home_outlined,
              size: 80,
              color: AppColors.grey400,
            ),
            const SizedBox(height: 24),
            Text(
              'Aucun foyer',
              style: AppTextStyles.headlineSmall.copyWith(
                color: AppColors.grey600,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Créez votre premier foyer ou rejoignez un foyer existant pour commencer',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.grey500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  /// Build centered action buttons for joining and creating households
  Widget _buildActionButtons(BuildContext context) {
    return Column(
      children: [
        // Join Household Button
        SizedBox(
          width: double.infinity,
          height: 48,
          child: ElevatedButton.icon(
            onPressed: () => _showJoinHouseholdDialog(context),
            icon: const Icon(Icons.qr_code_scanner),
            label: const Text('Rejoindre un foyer'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.teal,
              foregroundColor: Colors.white,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.zero, // Flat design
              ),
              elevation: 0,
            ),
          ),
        ),
        const SizedBox(height: 16),
        // Share Household Button
        SizedBox(
          width: double.infinity,
          height: 48,
          child: OutlinedButton.icon(
            onPressed: () => _showShareHouseholdDialog(context),
            icon: const Icon(Icons.share),
            label: const Text('Partager un foyer'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.teal,
              side: const BorderSide(color: AppColors.teal),
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.zero, // Flat design
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorState(
      BuildContext context, HouseholdManagementError state) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80,
              color: AppColors.error,
            ),
            const SizedBox(height: 24),
            Text(
              'Erreur',
              style: AppTextStyles.headlineSmall.copyWith(
                color: AppColors.error,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              state.message,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.grey600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                context
                    .read<HouseholdManagementBloc>()
                    .add(const LoadUserHouseholds());
              },
              child: const Text('Réessayer'),
            ),
          ],
        ),
      ),
    );
  }

  void _showShareHouseholdDialog(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    // Get current households from the BLoC state
    final state = context.read<HouseholdManagementBloc>().state;
    if (state is! HouseholdsLoaded || state.households.isEmpty) {
      // Show error if no households available
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Aucun foyer disponible pour partage'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    String? selectedHouseholdId;

    showDialog(
      context: context,
      builder: (dialogContext) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(l10n.inviteToHousehold),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Sélectionnez le foyer à partager:',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 16),
              // Household selection dropdown
              DropdownButtonFormField<String>(
                initialValue: selectedHouseholdId,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  labelText: 'Foyer',
                ),
                items: state.households.map((household) {
                  return DropdownMenuItem<String>(
                    value: household.id,
                    child: Text(
                      household.name,
                      overflow: TextOverflow.ellipsis,
                    ),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    selectedHouseholdId = value;
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Veuillez sélectionner un foyer';
                  }
                  return null;
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: Text(l10n.cancel),
            ),
            ElevatedButton(
              onPressed: selectedHouseholdId == null
                  ? null
                  : () {
                      // Generate invite code for selected household
                      context.read<HouseholdManagementBloc>().add(
                            GenerateInviteCode(
                                householdId: selectedHouseholdId!),
                          );
                      Navigator.of(dialogContext).pop();
                    },
              child: Text(l10n.generateInviteCode),
            ),
          ],
        ),
      ),
    );
  }

  void _showQRCodeDialog(
      BuildContext context, String inviteCode, String householdId) {
    final l10n = AppLocalizations.of(context);

    // Get household name from current state
    final state = context.read<HouseholdManagementBloc>().state;
    String householdName = 'Foyer';
    if (state is HouseholdsLoaded) {
      final household = state.households.firstWhere(
        (h) => h.id == householdId,
        orElse: () => state.households.first,
      );
      householdName = household.name;
    }

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: Text(l10n.inviteCodeGenerated),
        content: SizedBox(
          width: 300,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Code d\'invitation pour "$householdName"',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              // QR Code
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: AppColors.grey300),
                ),
                child: QrImageView(
                  data: inviteCode,
                  version: QrVersions.auto,
                  size: 200.0,
                  backgroundColor: Colors.white,
                  eyeStyle: const QrEyeStyle(
                    eyeShape: QrEyeShape.square,
                    color: Colors.black,
                  ),
                  dataModuleStyle: const QrDataModuleStyle(
                    dataModuleShape: QrDataModuleShape.square,
                    color: Colors.black,
                  ),
                ),
              ),
              const SizedBox(height: 16),
              // Invite code text
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.grey100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.grey300),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        inviteCode,
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontFamily: 'monospace',
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        Clipboard.setData(ClipboardData(text: inviteCode));
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Code copié dans le presse-papiers'),
                            backgroundColor: AppColors.success,
                            behavior: SnackBarBehavior.floating,
                          ),
                        );
                      },
                      icon: const Icon(Icons.copy),
                      tooltip: 'Copier le code',
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              Text(
                'Partagez ce code QR ou le code texte avec les personnes que vous souhaitez inviter.',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.grey600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: Text(l10n.cancel),
          ),
          ElevatedButton.icon(
            onPressed: () {
              // TODO: Implement native sharing functionality
              Clipboard.setData(ClipboardData(text: inviteCode));
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Code copié pour partage'),
                  backgroundColor: AppColors.success,
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
            icon: const Icon(Icons.share),
            label: Text(l10n.shareCode),
          ),
        ],
      ),
    );
  }

  void _showJoinHouseholdDialog(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: Text(l10n.joinHousehold),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // QR Scanner Option
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.of(dialogContext).pop();
                  _showQRScanner(context);
                },
                icon: const Icon(Icons.qr_code_scanner),
                label: Text(l10n.scanQRCode),
              ),
            ),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 16),
            // Manual Code Entry Option
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () {
                  Navigator.of(dialogContext).pop();
                  _showManualCodeEntry(context);
                },
                icon: const Icon(Icons.edit),
                label: Text(l10n.enterInviteCode),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: Text(l10n.cancel),
          ),
        ],
      ),
    );
  }

  void _showQRScanner(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: Text(l10n.scanQRCode),
        content: SizedBox(
          width: 300,
          height: 300,
          child: MobileScanner(
            onDetect: (capture) {
              final List<Barcode> barcodes = capture.barcodes;
              for (final barcode in barcodes) {
                if (barcode.rawValue != null) {
                  Navigator.of(dialogContext).pop();
                  _joinWithCode(context, barcode.rawValue!);
                  break;
                }
              }
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: Text(l10n.cancel),
          ),
        ],
      ),
    );
  }

  void _showManualCodeEntry(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final codeController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: Text(l10n.enterInviteCode),
        content: Form(
          key: formKey,
          child: TextFormField(
            controller: codeController,
            decoration: InputDecoration(
              labelText: l10n.inviteCode,
              hintText: l10n.enterInviteCodeHint,
              border: const OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return l10n.inviteCodeRequired;
              }
              return null;
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: Text(l10n.cancel),
          ),
          ElevatedButton(
            onPressed: () {
              if (formKey.currentState!.validate()) {
                Navigator.of(dialogContext).pop();
                _joinWithCode(context, codeController.text.trim());
              }
            },
            child: Text(l10n.join),
          ),
        ],
      ),
    );
  }

  void _joinWithCode(BuildContext context, String code) {
    context.read<HouseholdManagementBloc>().add(
          JoinHouseholdWithCode(inviteCode: code),
        );
  }

  void _showInviteDialog(BuildContext context, Household household) {
    final l10n = AppLocalizations.of(context);

    // First generate the invite code
    context.read<HouseholdManagementBloc>().add(
          GenerateInviteCode(householdId: household.id),
        );

    showDialog(
      context: context,
      builder: (dialogContext) =>
          BlocListener<HouseholdManagementBloc, HouseholdManagementState>(
        listener: (context, state) {
          if (state is InviteCodeGenerated) {
            // Update the dialog content when code is generated
          }
        },
        child: BlocBuilder<HouseholdManagementBloc, HouseholdManagementState>(
          builder: (context, state) {
            if (state is InviteCodeGenerated) {
              return AlertDialog(
                title: Text(l10n.inviteToHousehold),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // QR Code
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.grey300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: QrImageView(
                        data: state.inviteCode,
                        version: QrVersions.auto,
                        size: 200.0,
                      ),
                    ),
                    const SizedBox(height: 16),
                    // Invite Code Text
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppColors.grey100,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              state.inviteCode,
                              style: AppTextStyles.bodyMedium.copyWith(
                                fontFamily: 'monospace',
                              ),
                            ),
                          ),
                          IconButton(
                            onPressed: () {
                              Clipboard.setData(
                                  ClipboardData(text: state.inviteCode));
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(content: Text('${l10n.copyCode} ✓')),
                              );
                            },
                            icon: const Icon(Icons.copy),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(dialogContext).pop(),
                    child: Text(l10n.cancel),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      // Share functionality could be implemented here
                      Navigator.of(dialogContext).pop();
                    },
                    child: Text(l10n.shareCode),
                  ),
                ],
              );
            }

            // Loading state
            return AlertDialog(
              title: Text(l10n.generateInviteCode),
              content: const Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Génération du code d\'invitation...'),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  void _showLeaveConfirmation(BuildContext context, Household household) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Quitter le foyer'),
        content: Text(
            'Êtes-vous sûr de vouloir quitter le foyer "${household.name}" ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<HouseholdManagementBloc>().add(
                    LeaveHousehold(householdId: household.id),
                  );
            },
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('Quitter'),
          ),
        ],
      ),
    );
  }
}
