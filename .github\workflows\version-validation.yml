name: 📋 Version & Documentation Validation

on:
  pull_request:
    branches: [ develop, main ]
    types: [opened, synchronize, reopened]
  push:
    branches: [ develop, main ]

# Prevent concurrent validation runs
concurrency:
  group: version-validation-${{ github.ref }}
  cancel-in-progress: true

jobs:
  # ============================================================================
  # VERSION VALIDATION
  # ============================================================================
  version-validation:
    name: 📋 Version Validation
    runs-on: ubuntu-latest
    outputs:
      version_changed: ${{ steps.version-check.outputs.version_changed }}
      current_version: ${{ steps.version-check.outputs.current_version }}
      previous_version: ${{ steps.version-check.outputs.previous_version }}
      version_type: ${{ steps.version-check.outputs.version_type }}
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Full history for version comparison

      - name: 🔍 Check version consistency
        id: version-check
        run: |
          echo "🔍 Validating version consistency across files..."
          
          # Extract version from pubspec.yaml
          PUBSPEC_VERSION=$(grep "^version:" pubspec.yaml | cut -d' ' -f2 | cut -d'+' -f1)
          PUBSPEC_BUILD=$(grep "^version:" pubspec.yaml | cut -d' ' -f2 | cut -d'+' -f2)
          
          echo "Pubspec version: $PUBSPEC_VERSION+$PUBSPEC_BUILD"
          
          # Check if app_config.dart exists and has version
          if [[ -f "lib/core/config/app_config.dart" ]]; then
            APP_CONFIG_VERSION=$(grep -o "version.*=.*['\"].*['\"]" lib/core/config/app_config.dart | cut -d'"' -f2 || echo "")
            echo "App config version: $APP_CONFIG_VERSION"
            
            if [[ "$PUBSPEC_VERSION" != "$APP_CONFIG_VERSION" ]]; then
              echo "❌ Version mismatch between pubspec.yaml ($PUBSPEC_VERSION) and app_config.dart ($APP_CONFIG_VERSION)"
              exit 1
            fi
          fi
          
          # Check if version changed compared to base branch
          if [[ "${{ github.event_name }}" == "pull_request" ]]; then
            git fetch origin ${{ github.base_ref }}
            PREVIOUS_VERSION=$(git show origin/${{ github.base_ref }}:pubspec.yaml | grep "^version:" | cut -d' ' -f2 | cut -d'+' -f1 || echo "0.0.0")
          else
            PREVIOUS_VERSION=$(git show HEAD~1:pubspec.yaml | grep "^version:" | cut -d' ' -f2 | cut -d'+' -f1 || echo "0.0.0")
          fi
          
          echo "Previous version: $PREVIOUS_VERSION"
          echo "Current version: $PUBSPEC_VERSION"
          
          # Set outputs
          echo "current_version=$PUBSPEC_VERSION" >> $GITHUB_OUTPUT
          echo "previous_version=$PREVIOUS_VERSION" >> $GITHUB_OUTPUT
          
          if [[ "$PUBSPEC_VERSION" != "$PREVIOUS_VERSION" ]]; then
            echo "version_changed=true" >> $GITHUB_OUTPUT
            echo "✅ Version incremented: $PREVIOUS_VERSION → $PUBSPEC_VERSION"
            
            # Determine version type
            IFS='.' read -ra PREV_PARTS <<< "$PREVIOUS_VERSION"
            IFS='.' read -ra CURR_PARTS <<< "$PUBSPEC_VERSION"
            
            if [[ "${CURR_PARTS[0]}" != "${PREV_PARTS[0]}" ]]; then
              echo "version_type=major" >> $GITHUB_OUTPUT
              echo "🔴 MAJOR version change detected"
            elif [[ "${CURR_PARTS[1]}" != "${PREV_PARTS[1]}" ]]; then
              echo "version_type=minor" >> $GITHUB_OUTPUT
              echo "🟡 MINOR version change detected"
            elif [[ "${CURR_PARTS[2]}" != "${PREV_PARTS[2]}" ]]; then
              echo "version_type=patch" >> $GITHUB_OUTPUT
              echo "🟢 PATCH version change detected"
            else
              echo "version_type=unknown" >> $GITHUB_OUTPUT
              echo "⚠️ Unknown version change type"
            fi
          else
            echo "version_changed=false" >> $GITHUB_OUTPUT
            echo "ℹ️ Version unchanged: $PUBSPEC_VERSION"
            echo "version_type=none" >> $GITHUB_OUTPUT
          fi

      - name: 🔍 Validate version increment rules
        if: steps.version-check.outputs.version_changed == 'true'
        run: |
          echo "🔍 Validating version increment follows semantic versioning..."
          
          PREVIOUS="${{ steps.version-check.outputs.previous_version }}"
          CURRENT="${{ steps.version-check.outputs.current_version }}"
          VERSION_TYPE="${{ steps.version-check.outputs.version_type }}"
          
          # Parse versions
          IFS='.' read -ra PREV_PARTS <<< "$PREVIOUS"
          IFS='.' read -ra CURR_PARTS <<< "$CURRENT"
          
          PREV_MAJOR=${PREV_PARTS[0]}
          PREV_MINOR=${PREV_PARTS[1]}
          PREV_PATCH=${PREV_PARTS[2]}
          
          CURR_MAJOR=${CURR_PARTS[0]}
          CURR_MINOR=${CURR_PARTS[1]}
          CURR_PATCH=${CURR_PARTS[2]}
          
          # Validate increment rules
          case "$VERSION_TYPE" in
            "major")
              if [[ $CURR_MAJOR -ne $((PREV_MAJOR + 1)) ]] || [[ $CURR_MINOR -ne 0 ]] || [[ $CURR_PATCH -ne 0 ]]; then
                echo "❌ Invalid MAJOR version increment. Should be $((PREV_MAJOR + 1)).0.0"
                exit 1
              fi
              echo "✅ Valid MAJOR version increment"
              ;;
            "minor")
              if [[ $CURR_MAJOR -ne $PREV_MAJOR ]] || [[ $CURR_MINOR -ne $((PREV_MINOR + 1)) ]] || [[ $CURR_PATCH -ne 0 ]]; then
                echo "❌ Invalid MINOR version increment. Should be $PREV_MAJOR.$((PREV_MINOR + 1)).0"
                exit 1
              fi
              echo "✅ Valid MINOR version increment"
              ;;
            "patch")
              if [[ $CURR_MAJOR -ne $PREV_MAJOR ]] || [[ $CURR_MINOR -ne $PREV_MINOR ]] || [[ $CURR_PATCH -ne $((PREV_PATCH + 1)) ]]; then
                echo "❌ Invalid PATCH version increment. Should be $PREV_MAJOR.$PREV_MINOR.$((PREV_PATCH + 1))"
                exit 1
              fi
              echo "✅ Valid PATCH version increment"
              ;;
          esac

  # ============================================================================
  # CHANGELOG VALIDATION
  # ============================================================================
  changelog-validation:
    name: 📝 CHANGELOG Validation
    runs-on: ubuntu-latest
    needs: version-validation
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 📝 Validate CHANGELOG.md updates
        run: |
          echo "📝 Validating CHANGELOG.md updates..."
          
          if [[ ! -f "CHANGELOG.md" ]]; then
            echo "❌ CHANGELOG.md not found"
            exit 1
          fi
          
          CURRENT_VERSION="${{ needs.version-validation.outputs.current_version }}"
          VERSION_CHANGED="${{ needs.version-validation.outputs.version_changed }}"
          
          # Check if version changed
          if [[ "$VERSION_CHANGED" == "true" ]]; then
            echo "🔍 Version changed to $CURRENT_VERSION, checking CHANGELOG.md..."
            
            # Check if CHANGELOG contains current version
            if grep -q "## \[$CURRENT_VERSION\]" CHANGELOG.md; then
              echo "✅ CHANGELOG.md contains entry for version $CURRENT_VERSION"
            else
              echo "❌ CHANGELOG.md missing entry for version $CURRENT_VERSION"
              echo "Please add a section like:"
              echo "## [$CURRENT_VERSION] - $(date +%Y-%m-%d)"
              exit 1
            fi
            
            # Check if CHANGELOG was modified in this PR/commit
            if [[ "${{ github.event_name }}" == "pull_request" ]]; then
              git fetch origin ${{ github.base_ref }}
              if git diff --name-only origin/${{ github.base_ref }}...HEAD | grep -q "CHANGELOG.md"; then
                echo "✅ CHANGELOG.md was modified in this PR"
              else
                echo "❌ CHANGELOG.md was not updated despite version change"
                exit 1
              fi
            fi
          else
            echo "ℹ️ Version unchanged, CHANGELOG.md validation skipped"
          fi

      - name: 📝 Validate CHANGELOG format
        run: |
          echo "📝 Validating CHANGELOG.md format..."
          
          # Check for proper markdown structure
          if ! grep -q "# Changelog" CHANGELOG.md && ! grep -q "# CHANGELOG" CHANGELOG.md; then
            echo "❌ CHANGELOG.md should start with '# Changelog' or '# CHANGELOG'"
            exit 1
          fi
          
          # Check for version entries format
          if ! grep -q "## \[" CHANGELOG.md; then
            echo "❌ CHANGELOG.md should have version entries in format '## [version] - date'"
            exit 1
          fi
          
          echo "✅ CHANGELOG.md format validation passed"

  # ============================================================================
  # BREAKING CHANGE DETECTION
  # ============================================================================
  breaking-change-detection:
    name: 🔍 Breaking Change Detection
    runs-on: ubuntu-latest
    needs: version-validation
    if: needs.version-validation.outputs.version_changed == 'true'
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🔍 Analyze API changes
        run: |
          echo "🔍 Analyzing potential breaking changes..."
          
          VERSION_TYPE="${{ needs.version-validation.outputs.version_type }}"
          
          # Get changed files
          if [[ "${{ github.event_name }}" == "pull_request" ]]; then
            git fetch origin ${{ github.base_ref }}
            CHANGED_FILES=$(git diff --name-only origin/${{ github.base_ref }}...HEAD)
          else
            CHANGED_FILES=$(git diff --name-only HEAD~1...HEAD)
          fi
          
          echo "Changed files:"
          echo "$CHANGED_FILES"
          
          # Check for potential breaking changes
          BREAKING_INDICATORS=(
            "lib/data/models/"
            "lib/core/config/"
            "lib/data/datasources/"
            "pubspec.yaml"
          )
          
          BREAKING_FOUND=false
          
          for indicator in "${BREAKING_INDICATORS[@]}"; do
            if echo "$CHANGED_FILES" | grep -q "$indicator"; then
              echo "⚠️ Potential breaking change detected in: $indicator"
              BREAKING_FOUND=true
            fi
          done
          
          # Validate version type matches breaking changes
          if [[ "$BREAKING_FOUND" == "true" ]] && [[ "$VERSION_TYPE" != "major" ]]; then
            echo "❌ Breaking changes detected but version is not MAJOR"
            echo "Consider incrementing to a MAJOR version for breaking changes"
            # Don't fail for now, just warn
          elif [[ "$BREAKING_FOUND" == "false" ]] && [[ "$VERSION_TYPE" == "major" ]]; then
            echo "⚠️ MAJOR version increment but no obvious breaking changes detected"
            echo "Ensure this is intentional"
          fi
          
          if [[ "$BREAKING_FOUND" == "true" ]]; then
            echo "🔍 Breaking changes detected - ensure proper documentation"
          else
            echo "✅ No obvious breaking changes detected"
          fi

  # ============================================================================
  # DOCUMENTATION SYNC VALIDATION
  # ============================================================================
  documentation-sync:
    name: 📚 Documentation Sync
    runs-on: ubuntu-latest
    needs: version-validation
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📚 Validate documentation consistency
        run: |
          echo "📚 Validating documentation consistency..."
          
          CURRENT_VERSION="${{ needs.version-validation.outputs.current_version }}"
          
          # Check README.md mentions current version
          if [[ -f "README.md" ]]; then
            if grep -q "$CURRENT_VERSION" README.md; then
              echo "✅ README.md mentions current version"
            else
              echo "⚠️ README.md may need version update"
            fi
          fi
          
          # Check if documentation files exist
          REQUIRED_DOCS=(
            "README.md"
            "CHANGELOG.md"
            "docs/dev_prod_workflow.md"
          )
          
          for doc in "${REQUIRED_DOCS[@]}"; do
            if [[ -f "$doc" ]]; then
              echo "✅ $doc exists"
            else
              echo "❌ Required documentation missing: $doc"
              exit 1
            fi
          done
          
          echo "✅ Documentation consistency validation passed"

      - name: 📊 Generate documentation report
        run: |
          echo "📊 Generating documentation report..."
          
          cat > documentation_report.md << EOF
          # Documentation Validation Report
          
          **Version:** ${{ needs.version-validation.outputs.current_version }}
          **Generated:** $(date)
          **PR/Commit:** ${{ github.event_name }}
          
          ## Version Information
          - **Current Version:** ${{ needs.version-validation.outputs.current_version }}
          - **Previous Version:** ${{ needs.version-validation.outputs.previous_version }}
          - **Version Changed:** ${{ needs.version-validation.outputs.version_changed }}
          - **Change Type:** ${{ needs.version-validation.outputs.version_type }}
          
          ## Documentation Status
          - ✅ README.md exists
          - ✅ CHANGELOG.md exists and updated
          - ✅ Development workflow documentation exists
          - ✅ Version consistency validated
          
          ## Recommendations
          $(if [[ "${{ needs.version-validation.outputs.version_changed }}" == "true" ]]; then
            echo "- 🏷️ Create Git tag: \`git tag -a v${{ needs.version-validation.outputs.current_version }} -m 'Release v${{ needs.version-validation.outputs.current_version }}'\`"
            echo "- 📢 Update release notes in GitHub"
            echo "- 📊 Update project documentation with new features"
          else
            echo "- ℹ️ No version changes - continue development"
          fi)
          EOF
          
          cat documentation_report.md

      - name: 📤 Upload documentation report
        uses: actions/upload-artifact@v4
        with:
          name: documentation-report-${{ github.run_number }}
          path: documentation_report.md
          retention-days: 30

  # ============================================================================
  # RELEASE PREPARATION
  # ============================================================================
  release-preparation:
    name: 🚀 Release Preparation
    runs-on: ubuntu-latest
    needs: [version-validation, changelog-validation, breaking-change-detection, documentation-sync]
    if: needs.version-validation.outputs.version_changed == 'true' && github.ref == 'refs/heads/main'
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🏷️ Prepare release tag
        run: |
          echo "🏷️ Preparing release tag for version ${{ needs.version-validation.outputs.current_version }}"
          
          VERSION="${{ needs.version-validation.outputs.current_version }}"
          TAG_NAME="v$VERSION"
          
          # Extract release notes from CHANGELOG
          if [[ -f "CHANGELOG.md" ]]; then
            # Get content between current version and next version/end
            RELEASE_NOTES=$(awk "/## \[$VERSION\]/,/## \[/{if(/## \[/ && !/## \[$VERSION\]/) exit; if(!/## \[$VERSION\]/) print}" CHANGELOG.md | head -n -1)
            
            if [[ -n "$RELEASE_NOTES" ]]; then
              echo "Release notes extracted from CHANGELOG.md"
              echo "$RELEASE_NOTES" > release_notes.md
            else
              echo "No release notes found in CHANGELOG.md for version $VERSION"
              echo "# Release $TAG_NAME" > release_notes.md
              echo "See CHANGELOG.md for details." >> release_notes.md
            fi
          else
            echo "# Release $TAG_NAME" > release_notes.md
            echo "Release notes not available." >> release_notes.md
          fi
          
          echo "Release preparation completed for $TAG_NAME"

      - name: 📤 Upload release artifacts
        uses: actions/upload-artifact@v4
        with:
          name: release-preparation-${{ needs.version-validation.outputs.current_version }}
          path: |
            release_notes.md
          retention-days: 90

      - name: 📢 Release summary
        run: |
          echo "🚀 Release Summary"
          echo "================="
          echo "Version: ${{ needs.version-validation.outputs.current_version }}"
          echo "Type: ${{ needs.version-validation.outputs.version_type }}"
          echo "Branch: ${{ github.ref_name }}"
          echo ""
          echo "Next steps:"
          echo "1. Create GitHub release with tag v${{ needs.version-validation.outputs.current_version }}"
          echo "2. Deploy to production"
          echo "3. Monitor deployment for 24-48 hours"
          echo "4. Update project board and close related issues"
