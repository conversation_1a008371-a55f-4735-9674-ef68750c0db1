import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../presentation/bloc/auth/auth_bloc.dart';
import '../../presentation/bloc/auth/auth_state.dart' as auth_state;
import '../../utils/logger.dart';

/// Utility class for Supabase operations and validation
class SupabaseUtils {
  /// Validates if a string is a valid UUID format
  static bool isValidUUID(String? value) {
    if (value == null || value.isEmpty) return false;

    // UUID v4 regex pattern
    final uuidRegex = RegExp(
        r'^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$');

    return uuidRegex.hasMatch(value);
  }

  /// Safely retrieves household ID from authentication state
  /// Returns null if user is not authenticated or household ID is invalid
  static String? getHouseholdId(BuildContext context) {
    try {
      final authState = context.read<AuthBloc>().state;

      String? householdId;

      if (authState is auth_state.AuthAuthenticated) {
        householdId = authState.householdId;
      } else if (authState is auth_state.AuthProfileRefreshed) {
        householdId = authState.householdId;
      }

      if (householdId != null) {
        // Validate that household ID is a proper UUID
        if (isValidUUID(householdId)) {
          return householdId;
        } else {
          AppLogger.warning('Invalid household ID format: $householdId');
          return null;
        }
      }

      AppLogger.warning(
          'User not authenticated, cannot retrieve household ID. Current auth state: ${authState.runtimeType}');
      return null;
    } catch (e) {
      AppLogger.error('Error retrieving household ID', error: e);
      return null;
    }
  }

  /// Safely retrieves user ID from authentication state
  /// Returns null if user is not authenticated or user ID is invalid
  static String? getUserId(BuildContext context) {
    try {
      final authState = context.read<AuthBloc>().state;

      String? userId;

      if (authState is auth_state.AuthAuthenticated) {
        userId = authState.user.id;
      } else if (authState is auth_state.AuthProfileRefreshed) {
        userId = authState.user.id;
      }

      if (userId != null) {
        // Validate that user ID is a proper UUID
        if (isValidUUID(userId)) {
          return userId;
        } else {
          AppLogger.warning('Invalid user ID format: $userId');
          return null;
        }
      }

      AppLogger.warning('User not authenticated, cannot retrieve user ID');
      return null;
    } catch (e) {
      AppLogger.error('Error retrieving user ID', error: e);
      return null;
    }
  }

  /// Validates required parameters before making Supabase queries
  static bool validateRequiredParams(Map<String, dynamic> params) {
    for (final entry in params.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value == null || (value is String && value.isEmpty)) {
        AppLogger.warning('Required parameter $key is null or empty');
        return false;
      }

      // Special validation for UUID fields
      if (key.endsWith('_id') && value is String) {
        if (!isValidUUID(value)) {
          AppLogger.warning('Invalid UUID format for $key: $value');
          return false;
        }
      }
    }

    return true;
  }

  /// Formats Supabase query filters correctly
  static Map<String, dynamic> formatQueryFilters({
    required String householdId,
    Map<String, dynamic>? additionalFilters,
  }) {
    final filters = <String, dynamic>{
      'household_id': householdId,
    };

    if (additionalFilters != null) {
      filters.addAll(additionalFilters);
    }

    return filters;
  }

  /// Standard error messages for common Supabase errors
  static String getErrorMessage(dynamic error) {
    if (error == null) return 'Une erreur inconnue s\'est produite';

    final errorString = error.toString().toLowerCase();

    if (errorString.contains('400')) {
      return 'Paramètres de requête invalides';
    } else if (errorString.contains('401')) {
      return 'Non autorisé - veuillez vous reconnecter';
    } else if (errorString.contains('403')) {
      return 'Accès refusé';
    } else if (errorString.contains('404')) {
      return 'Ressource non trouvée';
    } else if (errorString.contains('network')) {
      return 'Erreur de connexion réseau';
    } else {
      return 'Erreur de base de données: ${error.toString()}';
    }
  }
}
