// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'MedyTrack';

  @override
  String get dashboard => 'لوحة التحكم';

  @override
  String get medicines => 'الأدوية';

  @override
  String get myMedicines => 'أدويتي';

  @override
  String get alerts => 'التنبيهات';

  @override
  String get reminders => 'التذكيرات';

  @override
  String get settings => 'الإعدادات';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get locations => 'المواقع';

  @override
  String get family => 'العائلة';

  @override
  String get notificationSettings => 'إعدادات الإشعارات';

  @override
  String get languageSettings => 'إعدادات اللغة';

  @override
  String get profileSecurity => 'الملف الشخصي والأمان';

  @override
  String get personalization => 'التخصيص';

  @override
  String get dataManagement => 'إدارة البيانات';

  @override
  String get expiryAlerts => 'تنبيهات انتهاء الصلاحية';

  @override
  String get lowStockAlerts => 'تنبيهات نفاد المخزون';

  @override
  String get medicationReminders => 'تذكيرات الأدوية';

  @override
  String get pushNotifications => 'الإشعارات الفورية';

  @override
  String get emailNotifications => 'إشعارات البريد الإلكتروني';

  @override
  String get enabled => 'مفعل';

  @override
  String get disabled => 'معطل';

  @override
  String get activated => 'مفعلة';

  @override
  String get deactivated => 'معطلة';

  @override
  String get language => 'اللغة';

  @override
  String get french => 'الفرنسية';

  @override
  String get english => 'الإنجليزية';

  @override
  String get arabic => 'العربية';

  @override
  String get darkMode => 'الوضع المظلم';

  @override
  String get dateFormat => 'تنسيق التاريخ';

  @override
  String get autoSync => 'المزامنة التلقائية';

  @override
  String get testNotifications => 'اختبار الإشعارات';

  @override
  String get sendTestNotification => 'إرسال إشعار تجريبي';

  @override
  String get test => 'اختبار';

  @override
  String get testNotificationSent => 'تم إرسال الإشعار التجريبي!';

  @override
  String get error => 'خطأ';

  @override
  String get expiryThreshold => 'حد انتهاء الصلاحية';

  @override
  String get lowStockThreshold => 'حد نفاد المخزون';

  @override
  String daysBeforeExpiry(int days) {
    return '$days أيام قبل انتهاء الصلاحية';
  }

  @override
  String unitsRemaining(int units) {
    return '$units وحدة أو أقل';
  }

  @override
  String get save => 'حفظ';

  @override
  String get cancel => 'إلغاء';

  @override
  String get ok => 'موافق';

  @override
  String get addReminder => 'إضافة تذكير';

  @override
  String get editReminder => 'تعديل التذكير';

  @override
  String get deleteReminder => 'حذف التذكير';

  @override
  String get reminderSettings => 'إعدادات التذكيرات';

  @override
  String get activeReminders => 'التذكيرات النشطة';

  @override
  String get medicinesWithoutReminders => 'الأدوية بدون تذكيرات';

  @override
  String get noActiveReminders => 'لا توجد تذكيرات نشطة';

  @override
  String get noMedicinesWithoutReminders => 'جميع الأدوية لديها تذكيرات';

  @override
  String get reminderTime => 'وقت التذكير';

  @override
  String get reminderFrequency => 'التكرار';

  @override
  String get daily => 'يومي';

  @override
  String get weekly => 'أسبوعي';

  @override
  String get hourlyInterval => 'فترة ساعية';

  @override
  String get selectDays => 'اختيار الأيام';

  @override
  String get intervalHours => 'الفترة (ساعات)';

  @override
  String get startDate => 'تاريخ البداية';

  @override
  String get endDate => 'تاريخ النهاية';

  @override
  String get isActive => 'نشط';

  @override
  String get reminderAdded => 'تم إضافة التذكير بنجاح';

  @override
  String get reminderUpdated => 'تم تحديث التذكير بنجاح';

  @override
  String get reminderDeleted => 'تم حذف التذكير بنجاح';

  @override
  String get medicineReminderTitle => '💊 تذكير الدواء';

  @override
  String timeToTakeMedicine(String medicineName) {
    return 'حان وقت تناول $medicineName';
  }

  @override
  String everyXHours(int hours) {
    return 'كل $hours ساعات';
  }

  @override
  String get loadingError => 'خطأ في التحميل';

  @override
  String get featureComingSoon => 'ستكون هذه الميزة متاحة في إصدار مستقبلي.';

  @override
  String get preferences => 'التفضيلات';

  @override
  String get familyMembers => 'أفراد العائلة';

  @override
  String get storageLocations => 'مواقع التخزين';

  @override
  String get tags => 'العلامات';

  @override
  String get dangerZone => 'منطقة الخطر';

  @override
  String get security => 'الأمان';

  @override
  String get minimumExpiryThreshold => 'الحد الأدنى لانتهاء الصلاحية';

  @override
  String get defaultLocation => 'الموقع الافتراضي';

  @override
  String get defaultFamilyMember => 'فرد العائلة الافتراضي';

  @override
  String get showExpiredMedicines => 'إظهار الأدوية منتهية الصلاحية';

  @override
  String get groupByLocation => 'تجميع حسب الموقع';

  @override
  String get addMember => 'إضافة عضو';

  @override
  String get editMember => 'تعديل العضو';

  @override
  String get addLocation => 'إضافة موقع';

  @override
  String get editLocation => 'تعديل الموقع';

  @override
  String get addTag => 'إضافة علامة';

  @override
  String get editTag => 'تعديل العلامة';

  @override
  String get name => 'الاسم';

  @override
  String get description => 'الوصف';

  @override
  String get relationship => 'العلاقة';

  @override
  String get category => 'الفئة';

  @override
  String get color => 'اللون';

  @override
  String get none => 'لا يوجد';

  @override
  String get days => 'أيام';

  @override
  String get units => 'وحدات';

  @override
  String get changePassword => 'تغيير كلمة المرور';

  @override
  String get currentPassword => 'كلمة المرور الحالية';

  @override
  String get newPassword => 'كلمة المرور الجديدة';

  @override
  String get confirmPassword => 'تأكيد كلمة المرور';

  @override
  String get updatePassword => 'تحديث كلمة المرور';

  @override
  String get deleteAccount => 'حذف الحساب';

  @override
  String get accountDeletion => 'حذف الحساب';

  @override
  String get personalInformation => 'المعلومات الشخصية';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get updateProfile => 'تحديث الملف الشخصي';

  @override
  String get avatar => 'الصورة الشخصية';

  @override
  String get uploadPhoto => 'رفع صورة';

  @override
  String get removePhoto => 'إزالة الصورة';

  @override
  String get inviteToHousehold => 'دعوة إلى المنزل';

  @override
  String get generateInviteCode => 'إنشاء رمز الدعوة';

  @override
  String get inviteCodeGenerated => 'تم إنشاء رمز الدعوة';

  @override
  String get shareCode => 'مشاركة الرمز';

  @override
  String get joinHousehold => 'الانضمام إلى المنزل';

  @override
  String get scanQRCode => 'مسح رمز الاستجابة السريعة';

  @override
  String get enterInviteCode => 'إدخال رمز الدعوة';

  @override
  String get inviteCode => 'رمز الدعوة';

  @override
  String get enterInviteCodeHint => 'أدخل رمز الدعوة';

  @override
  String get inviteCodeRequired => 'رمز الدعوة مطلوب';

  @override
  String get join => 'انضمام';

  @override
  String get copyCode => 'نسخ الرمز';
}
