# MedyTrack Database Migration Configuration Template
# Copy this file to db_sync_config.env and fill in your values
# NEVER commit the actual config file to version control

# Production Database Configuration
PROD_SUPABASE_URL=https://your-prod-project.supabase.co
PROD_SUPABASE_ANON_KEY=your-prod-anon-key-here
PROD_SUPABASE_SERVICE_KEY=your-prod-service-key-here

# Development Database Configuration  
DEV_SUPABASE_URL=https://your-dev-project.supabase.co
DEV_SUPABASE_ANON_KEY=your-dev-anon-key-here
DEV_SUPABASE_SERVICE_KEY=your-dev-service-key-here

# Migration Settings
BACKUP_RETENTION_DAYS=30
MAX_BACKUP_SIZE_MB=1000
MIGRATION_TIMEOUT_MINUTES=60

# Notification Settings (optional)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/webhook/url
EMAIL_NOTIFICATIONS=<EMAIL>

# Safety Settings
REQUIRE_MANUAL_APPROVAL=true
ENABLE_DRY_RUN=true
FORCE_BACKUP_VALIDATION=true

# Logging Configuration
LOG_LEVEL=INFO
LOG_RETENTION_DAYS=90
ENABLE_AUDIT_TRAIL=true
