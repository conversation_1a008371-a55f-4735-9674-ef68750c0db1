import 'package:flutter/foundation.dart';
import '../../domain/entities/reminder.dart';
import '../../domain/entities/medicine.dart';
import 'enhanced_reminder_notification_service.dart';

/// Service to integrate reminder notifications with the reminder management system
/// This service handles the lifecycle of notifications when reminders are created,
/// updated, paused, archived, or deleted
class ReminderNotificationIntegrationService {
  static final ReminderNotificationIntegrationService _instance =
      ReminderNotificationIntegrationService._internal();
  factory ReminderNotificationIntegrationService() => _instance;
  ReminderNotificationIntegrationService._internal();

  final EnhancedReminderNotificationService _notificationService =
      EnhancedReminderNotificationService();

  /// Initialize the notification integration service
  Future<bool> initialize() async {
    return await _notificationService.initialize();
  }

  /// Request notification permissions
  Future<bool> requestPermissions() async {
    return await _notificationService.requestPermissions();
  }

  /// Handle reminder creation - schedule all notifications
  Future<void> onReminderCreated({
    required Reminder reminder,
    required Medicine medicine,
  }) async {
    if (!reminder.isActive) {
      if (kDebugMode) {
        print(
            'Reminder ${reminder.id} is not active, skipping notification scheduling');
      }
      return;
    }

    try {
      await _notificationService.scheduleReminderNotifications(
        reminder: reminder,
        medicine: medicine,
      );

      if (kDebugMode) {
        print(
            'Successfully scheduled notifications for reminder ${reminder.id} - ${medicine.name}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error scheduling notifications for reminder ${reminder.id}: $e');
      }
    }
  }

  /// Handle reminder update - reschedule notifications
  Future<void> onReminderUpdated({
    required Reminder oldReminder,
    required Reminder newReminder,
    required Medicine medicine,
  }) async {
    // Cancel old notifications
    await _notificationService.cancelReminderNotifications(oldReminder.id!);

    // Schedule new notifications if the reminder is active
    if (newReminder.isActive) {
      await onReminderCreated(reminder: newReminder, medicine: medicine);
    }

    if (kDebugMode) {
      print(
          'Updated notifications for reminder ${newReminder.id} - ${medicine.displayName}');
    }
  }

  /// Handle reminder pause - cancel notifications
  Future<void> onReminderPaused({
    required Reminder reminder,
    required Medicine medicine,
  }) async {
    await _notificationService.cancelReminderNotifications(reminder.id!);

    if (kDebugMode) {
      print(
          'Paused notifications for reminder ${reminder.id} - ${medicine.displayName}');
    }
  }

  /// Handle reminder resume - reschedule notifications
  Future<void> onReminderResumed({
    required Reminder reminder,
    required Medicine medicine,
  }) async {
    await onReminderCreated(reminder: reminder, medicine: medicine);

    if (kDebugMode) {
      print(
          'Resumed notifications for reminder ${reminder.id} - ${medicine.displayName}');
    }
  }

  /// Handle reminder archive - cancel notifications
  Future<void> onReminderArchived({
    required Reminder reminder,
    required Medicine medicine,
  }) async {
    await _notificationService.cancelReminderNotifications(reminder.id!);

    if (kDebugMode) {
      print(
          'Archived notifications for reminder ${reminder.id} - ${medicine.displayName}');
    }
  }

  /// Handle reminder deletion - cancel notifications
  Future<void> onReminderDeleted({
    required Reminder reminder,
    required Medicine medicine,
  }) async {
    await _notificationService.cancelReminderNotifications(reminder.id!);

    if (kDebugMode) {
      print(
          'Deleted notifications for reminder ${reminder.id} - ${medicine.displayName}');
    }
  }

  /// Handle medicine update - update all related reminder notifications
  Future<void> onMedicineUpdated({
    required Medicine oldMedicine,
    required Medicine newMedicine,
    required List<Reminder> relatedReminders,
  }) async {
    for (final reminder in relatedReminders) {
      if (reminder.isActive) {
        // Cancel old notifications
        await _notificationService.cancelReminderNotifications(reminder.id!);

        // Schedule new notifications with updated medicine info
        await _notificationService.scheduleReminderNotifications(
          reminder: reminder,
          medicine: newMedicine,
        );
      }
    }

    if (kDebugMode) {
      print(
          'Updated notifications for medicine ${newMedicine.displayName} affecting ${relatedReminders.length} reminders');
    }
  }

  /// Handle dose taken - cancel post-reminder notification for this specific time
  Future<void> onDoseTaken({
    required Reminder reminder,
    required Medicine medicine,
    required DateTime scheduledTime,
  }) async {
    // This would require more sophisticated notification ID management
    // to cancel specific post-reminder notifications
    // For now, we'll leave the post-reminder notifications as they are
    // since they serve as a backup reminder system

    if (kDebugMode) {
      print(
          'Dose taken for ${medicine.displayName} at ${scheduledTime.toString()}');
    }
  }

  /// Schedule snooze notification
  Future<void> scheduleSnoozeNotification({
    required Reminder reminder,
    required Medicine medicine,
    required Duration snoozeDuration,
  }) async {
    await _notificationService.scheduleSnoozeNotification(
      reminder: reminder,
      medicine: medicine,
      snoozeDuration: snoozeDuration,
      snoozeIndex: DateTime.now().millisecondsSinceEpoch % 1000,
    );

    if (kDebugMode) {
      print(
          'Scheduled snooze notification for ${medicine.displayName} in ${snoozeDuration.toString()}');
    }
  }

  /// Bulk update notifications for multiple reminders
  Future<void> bulkUpdateNotifications({
    required List<Reminder> reminders,
    required List<Medicine> medicines,
  }) async {
    final medicineMap = {for (var medicine in medicines) medicine.id: medicine};

    for (final reminder in reminders) {
      final medicine = medicineMap[reminder.userMedicineId];
      if (medicine != null) {
        await _notificationService.cancelReminderNotifications(reminder.id!);

        if (reminder.isActive) {
          await _notificationService.scheduleReminderNotifications(
            reminder: reminder,
            medicine: medicine,
          );
        }
      }
    }

    if (kDebugMode) {
      print('Bulk updated notifications for ${reminders.length} reminders');
    }
  }

  /// Get notification service for direct access if needed
  EnhancedReminderNotificationService get notificationService =>
      _notificationService;
}
